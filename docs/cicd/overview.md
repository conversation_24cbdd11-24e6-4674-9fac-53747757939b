# CI/CD Architecture Overview

## 🏗️ High-Level Architecture

The ragtech repository implements a multi-platform CI/CD strategy with different cloud providers for different applications:

```mermaid
graph TB
    subgraph "GitHub Repository"
        A[ragtech Repository]
        B[.github/workflows/]
        C[GitHub Secrets]
    end
    
    subgraph "Applications"
        D[Cognisearch<br/>AWS-based]
        E[Filtro Curricular<br/>Azure-based]
        F[Static Pages<br/>AWS-based]
    end
    
    subgraph "Cloud Platforms"
        G[AWS Services<br/>ECR, ECS, S3, CloudFront]
        H[Azure Services<br/>ACR, App Services, Key Vault]
    end
    
    subgraph "Secrets Management"
        I[GitHub Secrets]
        J[Azure Key Vault]
        K[AWS Secrets Manager]
    end
    
    A --> B
    B --> D
    B --> E
    B --> F
    
    D --> G
    E --> H
    F --> G
    
    I --> B
    J --> H
    K --> G
    
    C --> I
```

## 🔄 Workflow Overview

### 1. Cognisearch Application (AWS)
- **Workflow**: `.github/workflows/deploy.yml`
- **Triggers**: Push/PR to `main` with changes in `apps/cognisearch-cloud/`
- **Platform**: AWS (ECR, ECS)
- **Deployment**: Containerized applications to ECS

### 2. Filtro Curricular (Azure)
- **Infrastructure Workflow**: `.github/workflows/filtro-curricular-infrastructure.yml`
- **Application Workflow**: `.github/workflows/filtro-curricular-applications.yml`
- **Triggers**: Push to `main`/`dev`, PR to `main`, manual dispatch
- **Platform**: Azure (ACR, App Services, Key Vault)
- **Deployment**: Infrastructure-as-Code + Containerized applications

### 3. Static Pages (AWS)
- **Workflow**: `.github/workflows/deploy-static-pages.yml`
- **Triggers**: Push to `main` with changes in `apps/static-pages/`
- **Platform**: AWS (S3, CloudFront)
- **Deployment**: Static file sync to S3 with CDN invalidation

## 🌍 Environment Strategy

### Environment Mapping
| Environment | Branch | Deployment | Approval Required |
|-------------|--------|------------|-------------------|
| **dev** | `dev` | Automatic | No |
| **staging** | `main` | Automatic | No |
| **uat** | Manual | Manual | Yes |
| **prod** | Manual | Manual | Yes |

### Environment-Specific Configurations
- **Development**: Debug enabled, shorter cache TTL, relaxed security
- **Staging**: Production-like settings, testing configurations
- **UAT**: Production settings, user acceptance testing
- **Production**: Optimized performance, strict security, monitoring enabled

## 🔐 Secrets Architecture

### Secret Sources
1. **GitHub Secrets**: Platform credentials, API keys
2. **Azure Key Vault**: Application secrets for Azure-deployed apps
3. **Environment Variables**: Runtime configuration injection

### Secret Flow
```mermaid
sequenceDiagram
    participant GH as GitHub Actions
    participant AKV as Azure Key Vault
    participant APP as Application
    
    Note over GH: Workflow starts
    GH->>GH: Read GitHub Secrets
    GH->>AKV: Authenticate with Azure
    GH->>AKV: Store/Update secrets
    GH->>APP: Deploy with Key Vault references
    APP->>AKV: Fetch secrets at runtime
    Note over APP: Application uses secrets
```

## 🚀 Deployment Patterns

### 1. Infrastructure-First Pattern (Filtro Curricular)
1. **Infrastructure Deployment**: Terraform provisions Azure resources
2. **Secret Management**: Secrets stored in Azure Key Vault
3. **Application Deployment**: Containers deployed to provisioned infrastructure
4. **Configuration Injection**: Key Vault references in App Service settings

### 2. Direct Deployment Pattern (Cognisearch)
1. **Container Build**: Docker images built and pushed to ECR
2. **Service Update**: ECS services updated with new images
3. **Secret Injection**: Environment variables from GitHub Secrets

### 3. Static Content Pattern (Static Pages)
1. **File Sync**: Static files synced to S3
2. **CDN Invalidation**: CloudFront cache invalidated
3. **DNS Update**: Route 53 records updated if needed

## 📊 Monitoring and Observability

### Application Monitoring
- **Azure**: Application Insights for Filtro Curricular
- **AWS**: CloudWatch for Cognisearch
- **GitHub**: Workflow execution logs and artifacts

### Key Metrics
- Deployment success rate
- Build duration
- Application health checks
- Secret rotation compliance

## 🔧 Key Technologies

### Infrastructure as Code
- **Terraform**: Azure infrastructure provisioning
- **GitHub Actions**: CI/CD orchestration

### Containerization
- **Docker**: Application containerization
- **Azure Container Registry**: Container image storage (Azure apps)
- **Amazon ECR**: Container image storage (AWS apps)

### Secrets Management
- **Azure Key Vault**: Centralized secret storage for Azure apps
- **GitHub Secrets**: CI/CD credentials and API keys
- **App Service Configuration**: Runtime secret injection

## 🎯 Best Practices Implemented

### Security
- ✅ Secrets never stored in code
- ✅ Least privilege access policies
- ✅ Environment-specific secret isolation
- ✅ Automatic secret rotation capabilities

### Reliability
- ✅ Infrastructure validation before deployment
- ✅ Rollback capabilities
- ✅ Health checks and monitoring
- ✅ Artifact retention for debugging

### Efficiency
- ✅ Parallel job execution where possible
- ✅ Conditional deployments based on changes
- ✅ Caching for faster builds
- ✅ Environment-specific optimizations

## 🔄 Continuous Improvement

### Planned Enhancements
- [ ] Automated testing integration
- [ ] Blue-green deployment strategies
- [ ] Enhanced monitoring and alerting
- [ ] Secret rotation automation
- [ ] Multi-region deployment support

---

**Next Steps**: 
- Review specific workflow documentation in the `workflows/` directory
- Set up secrets management following the `secrets-management/` guides
- Configure monitoring as described in the setup guides
