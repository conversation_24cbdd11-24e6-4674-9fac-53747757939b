# GitHub Actions Workflow Coordination

## 📋 Overview

The Filtro Curricular project uses two main GitHub Actions workflows that need to be coordinated:

1. **Infrastructure Workflow** (`filtro-curricular-infrastructure.yml`) - Deploys Azure infrastructure using Terraform
2. **Applications Workflow** (`filtro-curricular-applications.yml`) - Builds and deploys containerized applications

## 🔄 Coordination Logic

### Problem
When both infrastructure and application code changes are pushed simultaneously, both workflows trigger. The application deployment may fail if it tries to deploy before the infrastructure is ready.

### Solution
The applications workflow now automatically detects and waits for any running infrastructure workflow to complete before proceeding.

## 🏗️ How It Works

### 1. Infrastructure Detection
```yaml
check-infrastructure-workflow:
  - Checks for running infrastructure workflows on the same commit/branch
  - Uses GitHub API to query workflow status
  - Sets outputs indicating if waiting is required
```

### 2. Conditional Waiting
```yaml
wait-for-infrastructure:
  - Only runs if infrastructure workflow is detected
  - Polls infrastructure workflow status every 30 seconds
  - Waits up to 1 hour for completion
  - Fails if infrastructure deployment fails
```

### 3. Coordinated Deployment
```yaml
get-infrastructure-info:
  - Depends on both detection and waiting jobs
  - Only proceeds when infrastructure is ready
  - Continues normal application deployment flow
```

### Example Workflow Flow
```mermaid
graph TD
    A[Push to Repository] --> B{Both Workflows Triggered?}
    B -->|Yes| C[Infrastructure Workflow Starts]
    B -->|No| D[Single Workflow Runs Normally]
    C --> E[Applications Workflow Detects Infrastructure]
    E --> F[Applications Workflow Waits]
    F --> G{Infrastructure Success?}
    G -->|Yes| H[Applications Deployment Proceeds]
    G -->|No| I[Applications Deployment Fails]
    H --> J[Both Deployments Complete]
    style A fill:#e1f5fe
    style J fill:#c8e6c9
```

## 🎯 Trigger Scenarios

### Scenario 1: Infrastructure Changes Only
- Infrastructure workflow runs
- Applications workflow doesn't trigger
- **Result**: Infrastructure deploys normally

### Scenario 2: Application Changes Only
- Applications workflow runs
- No infrastructure workflow detected
- **Result**: Applications deploy immediately

### Scenario 3: Both Infrastructure and Application Changes
- Both workflows trigger simultaneously
- Applications workflow detects running infrastructure workflow
- Applications workflow waits for infrastructure completion
- **Result**: Infrastructure deploys first, then applications

### Scenario 4: Manual Deployments
- Manual infrastructure deployment via workflow_dispatch
- Subsequent application deployment automatically waits
- **Result**: Proper coordination maintained

## 📊 Monitoring

### Workflow Logs
The coordination process provides detailed logging:

```
🔍 Checking for running infrastructure workflows...
🏗️ Infrastructure workflow is running (ID: 12345). Application deployment will wait.
⏳ Infrastructure workflow still running. Waiting 30 seconds...
✅ Infrastructure workflow completed successfully!
```

### GitHub UI
- Infrastructure workflow run ID is displayed in logs
- Direct links to related workflows are provided
- Deployment summary shows coordination status

## ⚙️ Configuration

### Timeouts
- **Maximum wait time**: 1 hour (3600 seconds)
- **Check interval**: 30 seconds
- **Configurable** in workflow file

### Error Handling
- Infrastructure failure → Application deployment fails
- Infrastructure cancellation → Application deployment fails
- Timeout → Application deployment fails

## 🚨 Troubleshooting

### Common Issues

1. **Long Wait Times**
   - Check infrastructure workflow for issues
   - Monitor Azure resource provisioning
   - Consider infrastructure complexity

2. **Timeout Errors**
   - Increase `MAX_WAIT_TIME` if needed
   - Check for stuck infrastructure deployments
   - Verify Azure service availability

3. **False Positives**
   - Workflow detection uses commit SHA and branch
   - Ensure proper branch naming
   - Check for concurrent deployments

### Manual Override
If coordination fails, you can:
1. Cancel the stuck infrastructure workflow
2. Re-run the application workflow
3. Use manual deployment with specific environment

## 🔧 Customization

### Adjusting Wait Time
```yaml
MAX_WAIT_TIME=7200  # 2 hours
WAIT_INTERVAL=60    # Check every minute
```

### Adding Notifications
The coordination system integrates with existing notification jobs and can be extended to send alerts during long waits.

## 📈 Benefits

1. **Reliability**: Prevents application deployment failures due to missing infrastructure
2. **Automation**: No manual intervention required for coordination
3. **Visibility**: Clear logging and status reporting
4. **Flexibility**: Works with both automatic and manual deployments
5. **Safety**: Fails fast if infrastructure deployment fails

## 🔗 Related Documentation

- [GitHub Actions Setup Guide](../infrastructure/azure/filtro-curricular/docs/GITHUB_ACTIONS_SETUP.md)
- [Deployment Workflows](./workflows/filtro-curricular-azure.md)
- [Infrastructure Documentation](../infrastructure/azure/filtro-curricular/docs/README.md)
