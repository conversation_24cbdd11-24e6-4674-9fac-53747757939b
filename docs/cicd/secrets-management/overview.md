# Secrets Management Architecture

## 🔐 Overview

The ragtech repository implements a multi-layered secrets management strategy that ensures secure handling of sensitive information across different cloud platforms and environments.

## 🏗️ Secrets Architecture

```mermaid
graph TB
    subgraph "Secret Sources"
        A[GitHub Secrets]
        B[Azure Key Vault]
        C[AWS Secrets Manager]
    end
    
    subgraph "CI/CD Pipeline"
        D[GitHub Actions]
        E[Terraform]
        F[Application Deployment]
    end
    
    subgraph "Applications"
        G[Filtro Curricular<br/>Azure Apps]
        H[Cognisearch<br/>AWS Apps]
        I[Static Pages<br/>AWS S3]
    end
    
    subgraph "Runtime Environment"
        J[App Service Environment Variables]
        K[ECS Task Environment Variables]
        L[Container Runtime]
    end
    
    A --> D
    D --> E
    E --> B
    B --> G
    G --> J
    J --> L
    
    A --> F
    F --> H
    H --> K
    K --> L
    
    A --> I
```

## 🔑 Secret Categories

### 1. Platform Authentication Secrets
**Stored in**: GitHub Secrets  
**Purpose**: Authenticate CI/CD pipelines with cloud platforms

| Secret Name | Platform | Description | Usage |
|-------------|----------|-------------|-------|
| `AZURE_CREDENTIALS` | Azure | Service Principal JSON | Infrastructure deployment, app deployment |
| `AWS_ACCESS_KEY_ID` | AWS | AWS access key | ECR, ECS, S3 operations |
| `AWS_SECRET_ACCESS_KEY` | AWS | AWS secret key | ECR, ECS, S3 operations |

### 2. Application Secrets
**Stored in**: Azure Key Vault (for Azure apps), GitHub Secrets (for AWS apps)  
**Purpose**: Application runtime configuration

| Secret Name | Application | Azure Key Vault Name | GitHub Secret | Description |
|-------------|-------------|----------------------|---------------|-------------|
| `OPENAI_API_KEY` | Filtro Curricular | `openai-api-key` | `OPENAI_API_KEY` | OpenAI GPT API key |
| `OPENAI_TOKEN` | Filtro Curricular | `openai-token` | `OPENAI_TOKEN` | OpenAI Assistant API token |
| `GPT_API_KEY` | Cognisearch | N/A | `GPT_API_KEY` | OpenAI GPT API key (AWS app) |
| `ASSISTANT_ID_JURIDICO` | Filtro Curricular | `assistant-id-juridico` | `ASSISTANT_ID_JURIDICO` | Legal domain assistant |
| `ASSISTANT_ID_CALIDAD` | Filtro Curricular | `assistant-id-calidad` | `ASSISTANT_ID_CALIDAD` | Quality domain assistant |

### 3. Service-Specific Secrets
**Stored in**: GitHub Secrets  
**Purpose**: Cloud service configuration

| Secret Name | Service | Description |
|-------------|---------|-------------|
| `AWS_ECR_URL` | ECR | Container registry URL |
| `COGNISEARCH_CLOUDFRONT_DISTRIBUTION_ID` | CloudFront | CDN distribution ID |
| `RAGTECH_CLOUDFRONT_DISTRIBUTION_ID` | CloudFront | CDN distribution ID |

## 🔄 Secret Lifecycle

### 1. Secret Creation and Storage

#### For Azure Applications (Filtro Curricular)
```mermaid
sequenceDiagram
    participant Dev as Developer
    participant GH as GitHub Secrets
    participant GA as GitHub Actions
    participant TF as Terraform
    participant AKV as Azure Key Vault
    participant APP as App Service
    
    Dev->>GH: Store platform credentials
    Dev->>GH: Store application secrets
    GA->>GH: Read secrets during workflow
    GA->>TF: Pass secrets to Terraform
    TF->>AKV: Store secrets in Key Vault
    TF->>APP: Configure Key Vault references
    APP->>AKV: Fetch secrets at runtime
```

#### For AWS Applications (Cognisearch)
```mermaid
sequenceDiagram
    participant Dev as Developer
    participant GH as GitHub Secrets
    participant GA as GitHub Actions
    participant ECR as ECR
    participant ECS as ECS
    participant APP as Application
    
    Dev->>GH: Store all secrets
    GA->>GH: Read secrets during workflow
    GA->>ECR: Push container with env vars
    GA->>ECS: Update service with secrets
    ECS->>APP: Inject environment variables
```

### 2. Secret Access Patterns

#### Azure Key Vault Pattern
```yaml
# App Service Configuration
app_settings = {
  "GPT_API_KEY" = "@Microsoft.KeyVault(VaultName=${key_vault_name};SecretName=openai-api-key)"
  "OPENAI_TOKEN" = "@Microsoft.KeyVault(VaultName=${key_vault_name};SecretName=openai-token)"
}
```

#### Direct Environment Variable Pattern
```yaml
# GitHub Actions to ECS
environment:
  - GPT_API_KEY: ${{ secrets.GPT_API_KEY }}
  - OPENAI_TOKEN: ${{ secrets.OPENAI_TOKEN }}
```

## 🛡️ Security Measures

### Access Control
1. **GitHub Secrets**: Repository-level access control
2. **Azure Key Vault**: RBAC with managed identities
3. **AWS Secrets**: IAM policies with least privilege

### Encryption
- **At Rest**: All secrets encrypted in respective secret stores
- **In Transit**: TLS encryption for all secret retrieval
- **In Memory**: Secrets loaded only when needed

### Audit and Monitoring
- **GitHub**: Workflow logs (secrets redacted)
- **Azure**: Key Vault access logs
- **AWS**: CloudTrail for secret access

## 🔧 Secret Configuration Process

### Step 1: GitHub Secrets Setup
```bash
# Set platform credentials
gh secret set AZURE_CREDENTIALS --body "$(cat azure-credentials.json)"
gh secret set AWS_ACCESS_KEY_ID --body "AKIA..."
gh secret set AWS_SECRET_ACCESS_KEY --body "..."

# Set application secrets
gh secret set OPENAI_API_KEY --body "sk-proj-..."
gh secret set OPENAI_TOKEN --body "sk-..."
```

### Step 2: Azure Key Vault Configuration
```bash
# Automated via Terraform during infrastructure deployment
terraform apply -var="openai_api_key=$OPENAI_API_KEY"
```

### Step 3: Application Configuration
```python
# Python application secret loading
import os
from azure.keyvault.secrets import SecretClient
from azure.identity import DefaultAzureCredential

# For Azure apps - Key Vault integration
credential = DefaultAzureCredential()
client = SecretClient(vault_url="https://vault.vault.azure.net/", credential=credential)
openai_key = client.get_secret("openai-api-key").value

# For AWS apps - Environment variables
openai_key = os.getenv('GPT_API_KEY')
```

## 📊 Secret Rotation Strategy

### Automated Rotation
- **Azure Key Vault**: Supports automatic rotation for supported secret types
- **GitHub Secrets**: Manual rotation with workflow notifications

### Rotation Process
1. **Generate new secret** in source system (OpenAI, etc.)
2. **Update GitHub Secrets** with new values
3. **Trigger deployment** to update Key Vault
4. **Verify application** continues to function
5. **Revoke old secret** after confirmation

## 🚨 Emergency Procedures

### Secret Compromise Response
1. **Immediate**: Revoke compromised secret at source
2. **Update**: Generate new secret and update all stores
3. **Deploy**: Trigger emergency deployment with new secrets
4. **Monitor**: Watch for any unauthorized usage
5. **Document**: Record incident and lessons learned

### Backup and Recovery
- **GitHub Secrets**: Documented in secure location
- **Azure Key Vault**: Backup enabled with point-in-time recovery
- **AWS Secrets**: Cross-region replication where applicable

## 📈 Best Practices Implemented

### ✅ Security Best Practices
- Secrets never stored in code or logs
- Least privilege access policies
- Regular secret rotation
- Audit logging enabled
- Environment isolation

### ✅ Operational Best Practices
- Automated secret deployment
- Health checks after secret updates
- Rollback procedures documented
- Monitoring and alerting configured
- Documentation kept current

---

**Next Steps**:
- Configure [Azure Key Vault](azure-keyvault.md) for Azure applications
- Set up [GitHub Secrets](github-secrets.md) for CI/CD authentication
- Implement [secret rotation](secret-lifecycle.md) procedures
