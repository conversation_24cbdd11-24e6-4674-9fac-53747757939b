# GitHub Secrets Configuration and Management

## 🔐 Overview

GitHub Secrets serve as the primary entry point for all sensitive information in the ragtech CI/CD pipelines. This document covers the complete setup, configuration, and management of secrets in GitHub.

## 🏗️ GitHub Secrets Architecture

```mermaid
graph TB
    subgraph "Secret Categories"
        A[Platform Credentials]
        B[Application Secrets]
        C[Service Configuration]
    end
    
    subgraph "GitHub Repository"
        D[Repository Secrets]
        E[Environment Secrets]
        F[Organization Secrets]
    end
    
    subgraph "Workflows"
        G[Infrastructure Workflows]
        H[Application Workflows]
        I[Static Page Workflows]
    end
    
    subgraph "Target Platforms"
        J[Azure Services]
        K[AWS Services]
        L[OpenAI APIs]
    end
    
    A --> D
    B --> D
    C --> D
    
    D --> G
    D --> H
    D --> I
    
    G --> J
    H --> J
    H --> K
    I --> K
    
    G --> L
    H --> L
    
    style D fill:#e3f2fd
    style G fill:#fff3e0
    style J fill:#f3e5f5
```

## 📋 Complete Secrets Inventory

### Platform Authentication Secrets

#### Azure Credentials
```yaml
AZURE_CREDENTIALS:
  description: "Azure Service Principal credentials for infrastructure and application deployment"
  format: "JSON object with clientId, clientSecret, subscriptionId, tenantId"
  used_by: 
    - filtro-curricular-infrastructure.yml
    - filtro-curricular-applications.yml
  example: |
    {
      "clientId": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx",
      "clientSecret": "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
      "subscriptionId": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx",
      "tenantId": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx",
      "activeDirectoryEndpointUrl": "https://login.microsoftonline.com",
      "resourceManagerEndpointUrl": "https://management.azure.com/",
      "activeDirectoryGraphResourceId": "https://graph.windows.net/",
      "sqlManagementEndpointUrl": "https://management.core.windows.net:8443/",
      "galleryEndpointUrl": "https://gallery.azure.com/",
      "managementEndpointUrl": "https://management.core.windows.net/"
    }
```

#### AWS Credentials
```yaml
AWS_ACCESS_KEY_ID:
  description: "AWS access key for ECR, ECS, S3, and CloudFront operations"
  format: "AKIA followed by 16 alphanumeric characters"
  used_by:
    - deploy.yml (Cognisearch)
    - deploy-static-pages.yml
  example: "AKIAIOSFODNN7EXAMPLE"

AWS_SECRET_ACCESS_KEY:
  description: "AWS secret access key corresponding to the access key ID"
  format: "40-character base64-encoded string"
  used_by:
    - deploy.yml (Cognisearch)
    - deploy-static-pages.yml
  example: "wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY"

AWS_ECR_URL:
  description: "Amazon ECR repository URL for container images"
  format: "AWS account ID.dkr.ecr.region.amazonaws.com"
  used_by:
    - deploy.yml (Cognisearch)
  example: "************.dkr.ecr.sa-east-1.amazonaws.com"
```

### Application Secrets

#### OpenAI Credentials
```yaml
OPENAI_API_KEY:
  description: "OpenAI API key for GPT models (primary key)"
  format: "sk-proj- followed by 64+ characters"
  used_by:
    - filtro-curricular-infrastructure.yml (stored in Key Vault)
    - deploy.yml (Cognisearch - direct usage)
  azure_keyvault_name: "openai-api-key"
  app_setting_name: "GPT_API_KEY"
  example: "********************************************************************************************************************************************************************"

OPENAI_TOKEN:
  description: "OpenAI token for Assistant API access"
  format: "sk- followed by 48+ characters"
  used_by:
    - filtro-curricular-infrastructure.yml (stored in Key Vault)
    - deploy.yml (Cognisearch - direct usage)
  azure_keyvault_name: "openai-token"
  app_setting_name: "OPENAI_TOKEN"
  example: "sk-1234567890abcdef1234567890abcdef12345678"

GPT_API_KEY:
  description: "Alternative OpenAI API key (used by Cognisearch)"
  format: "sk-proj- followed by 64+ characters"
  used_by:
    - deploy.yml (Cognisearch - direct usage)
  note: "This is a separate key from OPENAI_API_KEY for Cognisearch application"
```

#### OpenAI Assistant IDs
```yaml
ASSISTANT_ID_JURIDICO:
  description: "OpenAI Assistant ID for legal domain processing"
  format: "asst_ followed by 24+ characters"
  used_by:
    - filtro-curricular-infrastructure.yml (stored in Key Vault)
  azure_keyvault_name: "assistant-id-juridico"
  app_setting_name: "ASSISTANT_ID_JURIDICO"
  example: "asst_1234567890abcdef12345678"

ASSISTANT_ID_CALIDAD:
  description: "OpenAI Assistant ID for quality domain processing"
  format: "asst_ followed by 24+ characters"
  used_by:
    - filtro-curricular-infrastructure.yml (stored in Key Vault)
  azure_keyvault_name: "assistant-id-calidad"
  app_setting_name: "ASSISTANT_ID_CALIDAD"
  example: "asst_abcdef1234567890abcdef12"
```

### Service Configuration Secrets

#### CloudFront Distribution IDs
```yaml
COGNISEARCH_CLOUDFRONT_DISTRIBUTION_ID:
  description: "CloudFront distribution ID for Cognisearch static website"
  format: "E followed by 13 alphanumeric characters"
  used_by:
    - deploy-static-pages.yml
  example: "E1234567890ABC"

RAGTECH_CLOUDFRONT_DISTRIBUTION_ID:
  description: "CloudFront distribution ID for Ragtech static website"
  format: "E followed by 13 alphanumeric characters"
  used_by:
    - deploy-static-pages.yml
  example: "EABCDEF1234567"
```

## 🔧 Secret Setup and Configuration

### Initial Setup Process

#### 1. Install GitHub CLI
```bash
# Install GitHub CLI
curl -fsSL https://cli.github.com/packages/githubcli-archive-keyring.gpg | sudo dd of=/usr/share/keyrings/githubcli-archive-keyring.gpg
echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/githubcli-archive-keyring.gpg] https://cli.github.com/packages stable main" | sudo tee /etc/apt/sources.list.d/github-cli.list > /dev/null
sudo apt update && sudo apt install gh

# Authenticate with GitHub
gh auth login
```

#### 2. Set Platform Credentials
```bash
# Azure Service Principal (create first using Azure CLI)
az ad sp create-for-rbac \
  --name "sp-ragtech-github-actions" \
  --role contributor \
  --scopes /subscriptions/$(az account show --query id -o tsv) \
  --sdk-auth > azure-credentials.json

# Store in GitHub
gh secret set AZURE_CREDENTIALS --body "$(cat azure-credentials.json)"

# Clean up local file
rm azure-credentials.json
```

```bash
# AWS Credentials (create IAM user with programmatic access)
gh secret set AWS_ACCESS_KEY_ID --body "AKIAIOSFODNN7EXAMPLE"
gh secret set AWS_SECRET_ACCESS_KEY --body "wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY"
gh secret set AWS_ECR_URL --body "************.dkr.ecr.sa-east-1.amazonaws.com"
```

#### 3. Set Application Secrets
```bash
# OpenAI Credentials (obtain from OpenAI platform)
gh secret set OPENAI_API_KEY --body "sk-proj-your-api-key-here"
gh secret set OPENAI_TOKEN --body "sk-your-token-here"
gh secret set GPT_API_KEY --body "sk-proj-your-gpt-key-here"

# Assistant IDs (create assistants in OpenAI platform)
gh secret set ASSISTANT_ID_JURIDICO --body "asst_your-juridico-assistant-id"
gh secret set ASSISTANT_ID_CALIDAD --body "asst_your-calidad-assistant-id"
```

#### 4. Set Service Configuration
```bash
# CloudFront Distribution IDs (get from AWS Console)
gh secret set COGNISEARCH_CLOUDFRONT_DISTRIBUTION_ID --body "E1234567890ABC"
gh secret set RAGTECH_CLOUDFRONT_DISTRIBUTION_ID --body "EABCDEF1234567"
```

### Verification and Management

#### List All Secrets
```bash
# List all repository secrets
gh secret list

# Expected output:
# ASSISTANT_ID_CALIDAD        Updated 2024-01-15
# ASSISTANT_ID_JURIDICO       Updated 2024-01-15
# AWS_ACCESS_KEY_ID           Updated 2024-01-15
# AWS_ECR_URL                 Updated 2024-01-15
# AWS_SECRET_ACCESS_KEY       Updated 2024-01-15
# AZURE_CREDENTIALS           Updated 2024-01-15
# COGNISEARCH_CLOUDFRONT_DISTRIBUTION_ID  Updated 2024-01-15
# GPT_API_KEY                 Updated 2024-01-15
# OPENAI_API_KEY              Updated 2024-01-15
# OPENAI_TOKEN                Updated 2024-01-15
# RAGTECH_CLOUDFRONT_DISTRIBUTION_ID      Updated 2024-01-15
```

#### Update Secrets
```bash
# Update individual secrets
gh secret set OPENAI_API_KEY --body "sk-proj-new-key-value"

# Update from file (for complex JSON secrets)
gh secret set AZURE_CREDENTIALS --body "$(cat new-azure-credentials.json)"

# Delete secret (use with caution)
gh secret delete OLD_SECRET_NAME
```

## 🔄 Secret Usage in Workflows

### Workflow Secret Access Patterns

#### Direct Environment Variable Usage
```yaml
# In workflow steps
- name: Deploy to AWS
  env:
    AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
    AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
  run: |
    aws configure set aws_access_key_id $AWS_ACCESS_KEY_ID
    aws configure set aws_secret_access_key $AWS_SECRET_ACCESS_KEY
```

#### Terraform Variable Injection
```yaml
# In infrastructure workflows
- name: Create terraform.tfvars
  run: |
    cat > terraform.tfvars << EOF
    openai_api_key        = "${{ secrets.OPENAI_API_KEY }}"
    openai_token          = "${{ secrets.OPENAI_TOKEN }}"
    assistant_id_juridico = "${{ secrets.ASSISTANT_ID_JURIDICO }}"
    assistant_id_calidad  = "${{ secrets.ASSISTANT_ID_CALIDAD }}"
    EOF
```

#### Azure Authentication
```yaml
# Azure login using service principal
- name: Azure Login
  uses: azure/login@v2
  with:
    creds: ${{ secrets.AZURE_CREDENTIALS }}
```

### Secret Flow Diagram
```mermaid
sequenceDiagram
    participant Dev as Developer
    participant GH as GitHub Secrets
    participant WF as Workflow
    participant AZ as Azure/AWS
    participant APP as Application
    
    Dev->>GH: Store secrets via CLI/UI
    WF->>GH: Read secrets during execution
    WF->>AZ: Authenticate with platform
    WF->>AZ: Deploy/configure services
    AZ->>APP: Inject secrets as env vars
    APP->>APP: Use secrets for API calls
```

## 🛡️ Security Best Practices

### Access Control
- ✅ **Repository Level**: Secrets scoped to repository
- ✅ **Workflow Permissions**: Minimal required permissions
- ✅ **Environment Protection**: Production secrets in protected environments
- ✅ **Audit Logging**: All secret access logged

### Secret Management
- ✅ **No Logging**: Secrets automatically redacted in logs
- ✅ **Encryption**: Secrets encrypted at rest and in transit
- ✅ **Rotation**: Regular secret rotation procedures
- ✅ **Least Privilege**: Secrets only accessible to required workflows

### Operational Security
```yaml
# Example of secure secret usage in workflow
jobs:
  deploy:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      id-token: write  # Required for OIDC
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: sa-east-1
          mask-aws-account-id: true  # Security feature
```

## 📊 Monitoring and Auditing

### Secret Usage Monitoring
```bash
# View workflow runs that used secrets
gh run list --workflow=filtro-curricular-infrastructure.yml --limit 10

# Check specific run for secret usage
gh run view RUN_ID --log | grep -i "secret\|credential" | head -20
```

### Audit Trail
- **GitHub Audit Log**: Available in organization settings
- **Workflow Logs**: Secret access logged (values redacted)
- **Platform Logs**: Azure/AWS logs show authentication events

## 🚨 Emergency Procedures

### Secret Compromise Response
```bash
# 1. Immediately rotate compromised secret
gh secret set COMPROMISED_SECRET_NAME --body "new-secure-value"

# 2. Revoke old secret at source (OpenAI, AWS, Azure)
# 3. Trigger workflows to update downstream systems
gh workflow run filtro-curricular-infrastructure.yml --field environment=prod

# 4. Monitor for unauthorized usage
gh run list --limit 20 | grep -E "(failed|error)"
```

### Backup and Recovery
```bash
# Document current secrets (metadata only, not values)
gh secret list > secrets-inventory-$(date +%Y%m%d).txt

# Backup secret configuration (for disaster recovery documentation)
cat > secret-recovery-plan.md << EOF
# Secret Recovery Plan
## Platform Credentials
- AZURE_CREDENTIALS: Recreate service principal
- AWS_ACCESS_KEY_ID/SECRET: Recreate IAM user
## Application Secrets  
- OPENAI_API_KEY: Regenerate from OpenAI platform
- OPENAI_TOKEN: Regenerate from OpenAI platform
EOF
```

## 🔄 Secret Rotation Schedule

### Recommended Rotation Frequency
| Secret Type | Rotation Frequency | Automation Level |
|-------------|-------------------|------------------|
| **Platform Credentials** | 90 days | Manual |
| **OpenAI API Keys** | 60 days | Manual |
| **Service Configuration** | As needed | Manual |
| **Development Secrets** | 30 days | Manual |

### Rotation Process
1. **Generate new secret** at source system
2. **Update GitHub secret** with new value
3. **Trigger deployment workflows** to update downstream systems
4. **Verify functionality** across all environments
5. **Revoke old secret** at source system
6. **Document rotation** in change log

---

**Next Steps**:
- Set up [secret rotation automation](secret-lifecycle.md#automated-rotation)
- Configure [Azure Key Vault integration](azure-keyvault.md)
- Review [emergency procedures](../troubleshooting/common-issues.md#secret-compromise-response)
