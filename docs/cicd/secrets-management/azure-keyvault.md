# Azure Key Vault Configuration and Management

## 🔐 Overview

Azure Key Vault serves as the centralized secrets management solution for the Filtro Curricular application. This document covers the complete setup, configuration, and management of secrets in Azure Key Vault.

## 🏗️ Key Vault Architecture

```mermaid
graph TB
    subgraph "Secret Sources"
        A[GitHub Secrets] --> B[GitHub Actions]
        C[Developer] --> D[Azure CLI/Portal]
    end
    
    subgraph "Azure Key Vault"
        E[Key Vault Instance]
        F[Access Policies]
        G[Secrets]
        H[Audit Logs]
    end
    
    subgraph "Applications"
        I[App Service Backend]
        J[App Service Frontend]
        K[Managed Identity]
    end
    
    B --> E
    D --> E
    E --> F
    E --> G
    E --> H
    
    F --> K
    K --> I
    K --> J
    
    style E fill:#e3f2fd
    style G fill:#fff3e0
    style K fill:#f3e5f5
```

## 🔧 Key Vault Configuration

### Terraform Configuration
The Key Vault is automatically provisioned via Terraform with the following configuration:

```hcl
# Key Vault for secrets management
resource "azurerm_key_vault" "main" {
  name                = "${local.project_name_short}-kv-${var.environment}-${random_string.suffix.result}"
  location            = azurerm_resource_group.main.location
  resource_group_name = azurerm_resource_group.main.name
  tenant_id           = data.azurerm_client_config.current.tenant_id
  sku_name            = "standard"

  # Environment-specific security settings
  soft_delete_retention_days = var.environment == "prod" ? 90 : 7
  purge_protection_enabled   = var.environment == "prod" ? true : false

  # Network access rules
  network_acls {
    default_action = var.environment == "prod" ? "Deny" : "Allow"
    bypass         = "AzureServices"
    virtual_network_subnet_ids = var.environment == "prod" && var.enable_vnet ? [azurerm_subnet.app_service[0].id] : []
  }

  tags = merge(var.tags, {
    Environment = var.environment
    Purpose     = "secrets-management"
  })
}
```

### Environment-Specific Settings

| Environment | Soft Delete Retention | Purge Protection | Network Access | 
|-------------|----------------------|------------------|----------------|
| **dev** | 7 days | Disabled | Allow all |
| **staging** | 7 days | Disabled | Allow all |
| **uat** | 30 days | Enabled | Restricted |
| **prod** | 90 days | Enabled | VNet only |

## 🔑 Secret Management

### Automated Secret Storage
Secrets are automatically stored during infrastructure deployment:

```hcl
# OpenAI API Key
resource "azurerm_key_vault_secret" "openai_api_key" {
  name         = "openai-api-key"
  value        = var.openai_api_key
  key_vault_id = azurerm_key_vault.main.id
  
  tags = {
    Purpose = "OpenAI GPT API access"
    Application = "filtro-curricular"
  }
}

# OpenAI Token
resource "azurerm_key_vault_secret" "openai_token" {
  name         = "openai-token"
  value        = var.openai_token
  key_vault_id = azurerm_key_vault.main.id
  
  tags = {
    Purpose = "OpenAI Assistant API access"
    Application = "filtro-curricular"
  }
}
```

### Secret Naming Convention
| Secret Purpose | Key Vault Name | App Setting Name | Description |
|----------------|----------------|------------------|-------------|
| OpenAI GPT API | `openai-api-key` | `GPT_API_KEY` | GPT model access |
| OpenAI Assistant | `openai-token` | `OPENAI_TOKEN` | Assistant API access |
| Legal Assistant | `assistant-id-juridico` | `ASSISTANT_ID_JURIDICO` | Legal domain assistant |
| Quality Assistant | `assistant-id-calidad` | `ASSISTANT_ID_CALIDAD` | Quality domain assistant |

## 🛡️ Access Control

### Access Policies Configuration

#### Service Principal Access (GitHub Actions)
```hcl
resource "azurerm_key_vault_access_policy" "github_actions" {
  key_vault_id = azurerm_key_vault.main.id
  tenant_id    = data.azurerm_client_config.current.tenant_id
  object_id    = data.azurerm_client_config.current.object_id

  secret_permissions = [
    "Get", "List", "Set", "Delete", "Recover", "Backup", "Restore"
  ]
}
```

#### App Service Managed Identity Access
```hcl
resource "azurerm_key_vault_access_policy" "backend_app" {
  key_vault_id = azurerm_key_vault.main.id
  tenant_id    = data.azurerm_client_config.current.tenant_id
  object_id    = azurerm_linux_web_app.backend.identity[0].principal_id

  secret_permissions = [
    "Get", "List"
  ]
}
```

### Permission Matrix
| Principal | Get | List | Set | Delete | Backup | Restore |
|-----------|-----|------|-----|--------|--------|---------|
| **GitHub Actions SP** | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |
| **Backend App MI** | ✅ | ✅ | ❌ | ❌ | ❌ | ❌ |
| **Frontend App MI** | ✅ | ✅ | ❌ | ❌ | ❌ | ❌ |
| **Developers** | ✅ | ✅ | ❌ | ❌ | ❌ | ❌ |

## 🔗 App Service Integration

### Key Vault References
App Services use Key Vault references to securely access secrets:

```hcl
locals {
  keyvault_references = {
    "GPT_API_KEY"   = "@Microsoft.KeyVault(VaultName=${azurerm_key_vault.main.name};SecretName=openai-api-key)"
    "OPENAI_TOKEN"  = "@Microsoft.KeyVault(VaultName=${azurerm_key_vault.main.name};SecretName=openai-token)"
    "ASSISTANT_ID_JURIDICO" = "@Microsoft.KeyVault(VaultName=${azurerm_key_vault.main.name};SecretName=assistant-id-juridico)"
    "ASSISTANT_ID_CALIDAD"  = "@Microsoft.KeyVault(VaultName=${azurerm_key_vault.main.name};SecretName=assistant-id-calidad)"
  }
}
```

### Runtime Secret Resolution
```mermaid
sequenceDiagram
    participant APP as App Service
    participant MI as Managed Identity
    participant AAD as Azure AD
    participant KV as Key Vault
    
    APP->>MI: Request secret access
    MI->>AAD: Authenticate with managed identity
    AAD->>MI: Return access token
    MI->>KV: Request secret with token
    KV->>KV: Validate permissions
    KV->>MI: Return secret value
    MI->>APP: Inject as environment variable
    
    Note over APP: Secret available as os.getenv('GPT_API_KEY')
```

## 🛠️ Manual Secret Management

### Using Azure CLI

#### List Secrets
```bash
# List all secrets in Key Vault
az keyvault secret list --vault-name filtro-kv-dev-abc123 --output table

# Show secret metadata (not value)
az keyvault secret show --vault-name filtro-kv-dev-abc123 --name openai-api-key --query "attributes"
```

#### Set/Update Secrets
```bash
# Set a new secret
az keyvault secret set \
  --vault-name filtro-kv-dev-abc123 \
  --name openai-api-key \
  --value "sk-proj-new-key-value" \
  --description "OpenAI API Key for GPT models"

# Update existing secret
az keyvault secret set \
  --vault-name filtro-kv-dev-abc123 \
  --name openai-token \
  --value "sk-new-token-value"
```

#### Get Secret Value (Use with caution)
```bash
# Get secret value (be careful - this shows actual secret)
az keyvault secret show \
  --vault-name filtro-kv-dev-abc123 \
  --name openai-api-key \
  --query "value" -o tsv
```

### Using Management Script
The repository includes a management script for easier secret handling:

```bash
# Navigate to infrastructure directory
cd apps/infrastructure/azure/filtro-curricular

# List all secrets
./scripts/manage-secrets.sh list

# Interactive secret update
./scripts/manage-secrets.sh update

# Update from JSON file
./scripts/manage-secrets.sh update-from-file secrets/secrets.json

# Get secret information
./scripts/manage-secrets.sh info openai-api-key
```

## 📊 Monitoring and Auditing

### Key Vault Audit Logs
```bash
# View Key Vault access logs
az monitor activity-log list \
  --resource-group rg-filtro-dev \
  --start-time 2024-01-01T00:00:00Z \
  --end-time 2024-01-31T23:59:59Z \
  --query "[?contains(resourceId, 'keyvault')]" \
  --output table

# Monitor secret access patterns
az monitor diagnostic-settings list \
  --resource "/subscriptions/.../resourceGroups/rg-filtro-dev/providers/Microsoft.KeyVault/vaults/filtro-kv-dev-abc123"
```

### Application Insights Integration
```bash
# Check App Service secret access logs
az webapp log tail --name filtro-be-dev-abc123 --resource-group rg-filtro-dev | grep -i "keyvault\|secret"
```

## 🔄 Secret Rotation

### Automated Rotation Setup
```bash
# Enable Key Vault event notifications
az eventgrid event-subscription create \
  --name secret-expiry-notifications \
  --source-resource-id "/subscriptions/.../resourceGroups/rg-filtro-dev/providers/Microsoft.KeyVault/vaults/filtro-kv-dev-abc123" \
  --endpoint-type webhook \
  --endpoint "https://your-automation-endpoint.com/rotate-secrets" \
  --included-event-types Microsoft.KeyVault.SecretNearExpiry
```

### Manual Rotation Process
1. **Generate new secret** at source (OpenAI platform)
2. **Update GitHub secret** with new value
3. **Trigger infrastructure workflow** to update Key Vault
4. **Verify application** continues to function
5. **Revoke old secret** at source

```bash
# Step-by-step rotation
# 1. Update GitHub secret
gh secret set OPENAI_API_KEY --body "sk-proj-new-key"

# 2. Trigger infrastructure update
gh workflow run filtro-curricular-infrastructure.yml --field environment=dev

# 3. Verify secret updated in Key Vault
az keyvault secret show --vault-name filtro-kv-dev-abc123 --name openai-api-key --query "attributes.updated"

# 4. Test application functionality
curl https://filtro-be-dev-abc123.azurewebsites.net/health
```

## 🚨 Security Best Practices

### Access Control
- ✅ **Least Privilege**: Applications only have Get/List permissions
- ✅ **Managed Identity**: No stored credentials in applications
- ✅ **Network Isolation**: Production Key Vault restricted to VNet
- ✅ **Audit Logging**: All access logged and monitored

### Secret Management
- ✅ **No Hardcoding**: Secrets never stored in code
- ✅ **Environment Isolation**: Separate Key Vaults per environment
- ✅ **Versioning**: Key Vault maintains secret versions
- ✅ **Soft Delete**: Protection against accidental deletion

### Operational Security
- ✅ **Regular Rotation**: Secrets rotated every 90 days
- ✅ **Monitoring**: Access patterns monitored for anomalies
- ✅ **Backup**: Secrets backed up for disaster recovery
- ✅ **Documentation**: Secret usage documented and maintained

## 🔧 Troubleshooting

### Common Issues

#### Issue 1: Key Vault Reference Not Resolving
```bash
# Check App Service configuration
az webapp config appsettings show \
  --name filtro-be-dev-abc123 \
  --resource-group rg-filtro-dev \
  --setting-names GPT_API_KEY

# Verify managed identity
az webapp identity show \
  --name filtro-be-dev-abc123 \
  --resource-group rg-filtro-dev
```

#### Issue 2: Access Denied Errors
```bash
# Check access policies
az keyvault show \
  --name filtro-kv-dev-abc123 \
  --query "properties.accessPolicies"

# Verify network access
az keyvault show \
  --name filtro-kv-dev-abc123 \
  --query "properties.networkAcls"
```

#### Issue 3: Secret Not Found
```bash
# List all secrets
az keyvault secret list \
  --vault-name filtro-kv-dev-abc123 \
  --output table

# Check secret versions
az keyvault secret list-versions \
  --vault-name filtro-kv-dev-abc123 \
  --name openai-api-key
```

---

**Next Steps**:
- Configure [secret rotation automation](secret-lifecycle.md#secret-rotation-setup)
- Set up [monitoring and alerting](../troubleshooting/debugging-guide.md#key-vault-monitoring)
- Review [emergency procedures](../troubleshooting/common-issues.md#secret-compromise-response)
