# Secret Lifecycle: OPENAI_TOKEN and GPT_API_KEY

## 🔍 Overview

This document traces the complete lifecycle of the two critical OpenAI secrets (`OPENAI_TOKEN` and `GPT_API_KEY`) from their creation to runtime usage in applications.

## 🗺️ Secret Journey Map

```mermaid
graph TD
    subgraph "1. Secret Creation"
        A[OpenAI Platform] --> B[Generate API Keys]
        B --> C[OPENAI_TOKEN<br/>sk-...]
        B --> D[GPT_API_KEY<br/>sk-proj-...]
    end
    
    subgraph "2. Initial Storage"
        C --> E[GitHub Secrets<br/>OPENAI_TOKEN]
        D --> F[GitHub Secrets<br/>GPT_API_KEY]
    end
    
    subgraph "3. CI/CD Pipeline"
        E --> G[GitHub Actions<br/>Infrastructure Workflow]
        F --> G
        G --> H[Terraform Variables]
        H --> I[Azure Key Vault<br/>Storage]
    end
    
    subgraph "4. Application Deployment"
        I --> J[App Service<br/>Configuration]
        J --> K[Key Vault References<br/>@Microsoft.KeyVault(...)]
    end
    
    subgraph "5. Runtime Access"
        K --> L[Managed Identity<br/>Authentication]
        L --> M[Application Code<br/>Secret Retrieval]
        M --> N[OpenAI API Calls]
    end
    
    style A fill:#fff2cc
    style N fill:#d5e8d4
```

## 📋 Detailed Secret Flow

### Phase 1: Secret Creation and Initial Storage

#### 1.1 OpenAI Platform Setup
```bash
# Developer creates API keys on OpenAI platform
# OPENAI_TOKEN: Used for OpenAI Assistant API
# GPT_API_KEY: Used for OpenAI GPT models
```

#### 1.2 GitHub Secrets Configuration
```bash
# Store secrets in GitHub repository
gh secret set OPENAI_TOKEN --body "sk-..."
gh secret set GPT_API_KEY --body "sk-proj-..."
gh secret set OPENAI_API_KEY --body "sk-proj-..."  # Alias for GPT_API_KEY
```

### Phase 2: CI/CD Pipeline Integration

#### 2.1 Infrastructure Workflow Trigger
```yaml
# .github/workflows/filtro-curricular-infrastructure.yml
on:
  push:
    branches: [ main, dev ]
    paths:
      - 'apps/infrastructure/azure/filtro-curricular/**'
```

#### 2.2 Secret Injection into Terraform
```yaml
# GitHub Actions step
- name: 📝 Create terraform.tfvars
  run: |
    cat > terraform.tfvars << EOF
    # OpenAI Configuration
    openai_api_key        = "${{ secrets.OPENAI_API_KEY }}"
    openai_token          = "${{ secrets.OPENAI_TOKEN }}"
    assistant_id_juridico = "${{ secrets.ASSISTANT_ID_JURIDICO }}"
    assistant_id_calidad  = "${{ secrets.ASSISTANT_ID_CALIDAD }}"
    EOF
```

#### 2.3 Terraform Variable Processing
```hcl
# apps/infrastructure/azure/filtro-curricular/variables.tf
variable "openai_api_key" {
  description = "OpenAI API Key"
  type        = string
  sensitive   = true
}

variable "openai_token" {
  description = "OpenAI Token"
  type        = string
  sensitive   = true
}
```

### Phase 3: Azure Key Vault Storage

#### 3.1 Key Vault Secret Creation
```hcl
# apps/infrastructure/azure/filtro-curricular/main.tf
resource "azurerm_key_vault_secret" "openai_api_key" {
  name         = "openai-api-key"
  value        = var.openai_api_key
  key_vault_id = azurerm_key_vault.main.id
}

resource "azurerm_key_vault_secret" "openai_token" {
  name         = "openai-token"
  value        = var.openai_token
  key_vault_id = azurerm_key_vault.main.id
}
```

#### 3.2 Access Policy Configuration
```hcl
# Grant App Service access to Key Vault
resource "azurerm_key_vault_access_policy" "backend_app" {
  key_vault_id = azurerm_key_vault.main.id
  tenant_id    = data.azurerm_client_config.current.tenant_id
  object_id    = azurerm_linux_web_app.backend.identity[0].principal_id

  secret_permissions = [
    "Get",
    "List"
  ]
}
```

### Phase 4: App Service Configuration

#### 4.1 Key Vault Reference Setup
```hcl
# apps/infrastructure/azure/filtro-curricular/modules/app-config/main.tf
locals {
  openai_secrets = {
    "openai-api-key" = {
      app_setting_name = "GPT_API_KEY"
      description      = "OpenAI API Key for GPT models"
      required         = true
    }
    "openai-token" = {
      app_setting_name = "OPENAI_TOKEN"
      description      = "OpenAI Token for assistant API"
      required         = true
    }
  }

  keyvault_references = {
    for secret_name, config in local.openai_secrets :
    config.app_setting_name => "@Microsoft.KeyVault(VaultName=${var.key_vault_name};SecretName=${secret_name})"
  }
}
```

#### 4.2 App Service Environment Variables
```hcl
# Final app settings applied to App Service
app_settings = {
  "GPT_API_KEY"   = "@Microsoft.KeyVault(VaultName=filtro-kv-dev-abc123;SecretName=openai-api-key)"
  "OPENAI_TOKEN"  = "@Microsoft.KeyVault(VaultName=filtro-kv-dev-abc123;SecretName=openai-token)"
  # ... other settings
}
```

### Phase 5: Runtime Secret Access

#### 5.1 Managed Identity Authentication
```python
# apps/filtro-curricular/filtro-curricular-be-api/src/app/utils/constants.py
import os
from azure.identity import DefaultAzureCredential
from azure.keyvault.secrets import SecretClient

# App Service automatically uses managed identity
credential = DefaultAzureCredential()
```

#### 5.2 Application Secret Loading
```python
# Environment variable access (Key Vault reference resolved by Azure)
def load_environment():
    """Load environment variables including Key Vault references"""
    
    # These are automatically resolved by Azure App Service
    openai_api_key = os.getenv('GPT_API_KEY')  # Resolves Key Vault reference
    openai_token = os.getenv('OPENAI_TOKEN')   # Resolves Key Vault reference
    
    if not openai_api_key:
        print("❌ GPT_API_KEY not found in environment")
    if not openai_token:
        print("❌ OPENAI_TOKEN not found in environment")
    
    return {
        'gpt_api_key': openai_api_key,
        'openai_token': openai_token
    }
```

#### 5.3 OpenAI API Integration
```python
# Application usage of secrets
import openai
from app.utils.constants import load_environment

env_vars = load_environment()

# Configure OpenAI client
openai.api_key = env_vars['gpt_api_key']

# Use in API calls
def call_openai_gpt(prompt):
    response = openai.ChatCompletion.create(
        model="gpt-4",
        messages=[{"role": "user", "content": prompt}],
        headers={"Authorization": f"Bearer {env_vars['gpt_api_key']}"}
    )
    return response

def call_openai_assistant(thread_id):
    # Use OPENAI_TOKEN for assistant API
    headers = {"Authorization": f"Bearer {env_vars['openai_token']}"}
    # ... assistant API calls
```

## 🔄 Secret Update Process

### Updating Secrets in Production

#### Step 1: Update GitHub Secrets
```bash
# Update the secret in GitHub
gh secret set OPENAI_API_KEY --body "sk-proj-NEW_KEY_VALUE"
gh secret set OPENAI_TOKEN --body "sk-NEW_TOKEN_VALUE"
```

#### Step 2: Trigger Infrastructure Update
```bash
# Manual workflow dispatch to update Key Vault
gh workflow run filtro-curricular-infrastructure.yml \
  --field environment=prod \
  --field destroy=false
```

#### Step 3: Verify Secret Update
```bash
# Check Key Vault secret versions
az keyvault secret list --vault-name filtro-kv-prod-xyz789 --output table
az keyvault secret show --vault-name filtro-kv-prod-xyz789 --name openai-api-key
```

#### Step 4: Application Restart (if needed)
```bash
# Restart App Service to pick up new secrets
az webapp restart --name filtro-be-prod-xyz789 --resource-group rg-filtro-prod
```

## 📊 Secret Usage Monitoring

### Key Vault Access Logs
```bash
# Monitor secret access in Azure
az monitor activity-log list \
  --resource-group rg-filtro-prod \
  --start-time 2024-01-01 \
  --end-time 2024-01-31 \
  --query "[?contains(resourceId, 'keyvault')]"
```

### Application Logs
```python
# Application-level secret usage logging
import logging

logger = logging.getLogger(__name__)

def load_secrets_with_logging():
    try:
        secrets = load_environment()
        logger.info("✅ Secrets loaded successfully")
        return secrets
    except Exception as e:
        logger.error(f"❌ Failed to load secrets: {e}")
        raise
```

## 🚨 Troubleshooting Secret Issues

### Common Issues and Solutions

#### Issue 1: Key Vault Reference Not Resolving
```bash
# Check App Service configuration
az webapp config appsettings list --name filtro-be-dev-abc123 --resource-group rg-filtro-dev

# Verify managed identity
az webapp identity show --name filtro-be-dev-abc123 --resource-group rg-filtro-dev

# Check Key Vault access policy
az keyvault show --name filtro-kv-dev-abc123 --query "properties.accessPolicies"
```

#### Issue 2: Secret Not Found in Application
```python
# Debug secret loading
import os
print("Environment variables:")
for key, value in os.environ.items():
    if 'API' in key or 'TOKEN' in key:
        print(f"{key}: {'***' if value else 'NOT SET'}")
```

#### Issue 3: OpenAI API Authentication Failure
```python
# Test OpenAI connectivity
import openai
import os

try:
    openai.api_key = os.getenv('GPT_API_KEY')
    models = openai.Model.list()
    print("✅ OpenAI authentication successful")
except Exception as e:
    print(f"❌ OpenAI authentication failed: {e}")
```

## 📈 Best Practices for Secret Lifecycle

### ✅ Security Best Practices
- **Regular Rotation**: Rotate secrets every 90 days
- **Least Privilege**: Grant minimal required permissions
- **Audit Logging**: Monitor all secret access
- **Environment Isolation**: Separate secrets per environment

### ✅ Operational Best Practices
- **Automated Deployment**: Use CI/CD for secret updates
- **Health Checks**: Verify application after secret changes
- **Rollback Plan**: Maintain previous secret versions
- **Documentation**: Keep secret usage documented

---

**Next Steps**:
- Set up [secret rotation automation](../setup-guides/azure-setup.md#secret-rotation)
- Configure [monitoring and alerting](../troubleshooting/debugging-guide.md#secret-monitoring)
- Review [emergency procedures](../troubleshooting/common-issues.md#secret-compromise)
