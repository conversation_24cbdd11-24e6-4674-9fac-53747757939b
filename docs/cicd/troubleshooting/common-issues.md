# Common CI/CD Issues and Solutions

## 🚨 Overview

This guide covers the most common issues encountered in the ragtech CI/CD pipelines and their solutions.

## 🔐 Secrets and Authentication Issues

### Issue 1: Azure Authentication Failure

#### Symptoms
```
Error: Failed to authenticate with Azure
Error: AADSTS70002: Error validating credentials
```

#### Diagnosis
```bash
# Check GitHub secret format
gh secret list | grep AZURE

# Verify service principal exists
az ad sp list --display-name "sp-ragtech-github-actions" --output table
```

#### Solutions
```bash
# 1. Recreate service principal
az ad sp create-for-rbac \
  --name "sp-ragtech-github-actions" \
  --role contributor \
  --scopes /subscriptions/$(az account show --query id -o tsv) \
  --sdk-auth

# 2. Update GitHub secret with new credentials
gh secret set AZURE_CREDENTIALS --body "$(cat azure-credentials.json)"

# 3. Verify permissions
az role assignment list --assignee "service-principal-id" --output table
```

### Issue 2: Key Vault Access Denied

#### Symptoms
```
Error: The user, group or application does not have secrets get permission
Error: Access denied to Key Vault
```

#### Diagnosis
```bash
# Check Key Vault access policies
az keyvault show --name filtro-kv-dev-abc123 --query "properties.accessPolicies"

# Check managed identity
az webapp identity show --name filtro-be-dev-abc123 --resource-group rg-filtro-dev
```

#### Solutions
```bash
# 1. Add access policy for App Service managed identity
PRINCIPAL_ID=$(az webapp identity show --name filtro-be-dev-abc123 --resource-group rg-filtro-dev --query "principalId" -o tsv)
az keyvault set-policy \
  --name filtro-kv-dev-abc123 \
  --object-id $PRINCIPAL_ID \
  --secret-permissions get list

# 2. Verify network access rules
az keyvault update --name filtro-kv-dev-abc123 --default-action Allow

# 3. Check firewall rules
az keyvault network-rule list --name filtro-kv-dev-abc123
```

### Issue 3: OpenAI API Key Invalid

#### Symptoms
```
Error: Invalid API key provided
Error: You didn't provide an API key
```

#### Diagnosis
```bash
# Check if secret exists in GitHub
gh secret list | grep OPENAI

# Verify secret in Key Vault
az keyvault secret show --vault-name filtro-kv-dev-abc123 --name openai-api-key --query "attributes.enabled"

# Test API key directly
curl -H "Authorization: Bearer sk-proj-..." https://api.openai.com/v1/models
```

#### Solutions
```bash
# 1. Update GitHub secret
gh secret set OPENAI_API_KEY --body "sk-proj-new-key-here"

# 2. Trigger infrastructure update to sync to Key Vault
gh workflow run filtro-curricular-infrastructure.yml --field environment=dev

# 3. Restart application to pick up new secret
az webapp restart --name filtro-be-dev-abc123 --resource-group rg-filtro-dev
```

## 🏗️ Infrastructure Deployment Issues

### Issue 4: Terraform State Lock

#### Symptoms
```
Error: Error acquiring the state lock
Error: Lock Info: ID: xxx-xxx-xxx
```

#### Diagnosis
```bash
# Check current state
terraform show

# List state locks (if using Azure backend)
az storage blob list --container-name tfstate --account-name stterraformstate123
```

#### Solutions
```bash
# 1. Wait for lock to release (if another operation is running)
# 2. Force unlock (use with caution)
terraform force-unlock LOCK_ID

# 3. If using Azure backend, check blob lease
az storage blob show --container-name tfstate --name filtro-curricular.tfstate --account-name stterraformstate123
```

### Issue 5: Resource Name Conflicts

#### Symptoms
```
Error: A resource with the ID already exists
Error: The storage account name is already taken
```

#### Diagnosis
```bash
# Check existing resources
az resource list --resource-group rg-filtro-dev --output table

# Check storage account name availability
az storage account check-name --name filtrofiltrocurricular123
```

#### Solutions
```bash
# 1. Use unique naming with random suffix (already implemented)
# 2. Import existing resource if appropriate
terraform import azurerm_storage_account.main /subscriptions/.../resourceGroups/.../providers/Microsoft.Storage/storageAccounts/...

# 3. Destroy and recreate if safe
terraform destroy -target=azurerm_storage_account.main
terraform apply
```

### Issue 6: Azure Region Limitations

#### Symptoms
```
Error: The specified location 'chilecentral' is not available for resource type
Error: Log Analytics workspace is not supported in this region
```

#### Diagnosis
```bash
# Check available locations for resource type
az provider show --namespace Microsoft.OperationalInsights --query "resourceTypes[?resourceType=='workspaces'].locations"
```

#### Solutions
```bash
# 1. Use supported region for monitoring resources
# Update terraform.tfvars:
monitoring_location = "eastus"  # Supported region

# 2. Verify region support before deployment
az account list-locations --query "[?name=='chilecentral']"
```

## 🚀 Application Deployment Issues

### Issue 7: Container Build Failures

#### Symptoms
```
Error: failed to solve: process "/bin/sh -c npm install" did not complete successfully
Error: Docker build failed
```

#### Diagnosis
```bash
# Check Dockerfile syntax
docker build -t test-image .

# Verify base image availability
docker pull node:20-alpine

# Check build logs in GitHub Actions
gh run view --log
```

#### Solutions
```bash
# 1. Fix Dockerfile issues
# 2. Update base image versions
FROM node:20-alpine  # Use specific, stable version

# 3. Add error handling in build steps
RUN npm ci --only=production || (cat /root/.npm/_logs/*.log && exit 1)

# 4. Clear Docker cache if needed
docker system prune -a
```

### Issue 8: App Service Deployment Timeout

#### Symptoms
```
Error: Timeout waiting for App Service to start
Error: Container didn't respond to HTTP pings
```

#### Diagnosis
```bash
# Check App Service logs
az webapp log tail --name filtro-be-dev-abc123 --resource-group rg-filtro-dev

# Check container status
az webapp show --name filtro-be-dev-abc123 --resource-group rg-filtro-dev --query "state"

# Verify health endpoint
curl https://filtro-be-dev-abc123.azurewebsites.net/health
```

#### Solutions
```bash
# 1. Increase startup timeout
az webapp config set --name filtro-be-dev-abc123 --resource-group rg-filtro-dev --startup-file "timeout 300"

# 2. Check environment variables
az webapp config appsettings list --name filtro-be-dev-abc123 --resource-group rg-filtro-dev

# 3. Restart App Service
az webapp restart --name filtro-be-dev-abc123 --resource-group rg-filtro-dev

# 4. Scale up if resource constrained
az appservice plan update --name asp-filtro-dev --resource-group rg-filtro-dev --sku B2
```

### Issue 9: Environment Variable Not Loading

#### Symptoms
```
Error: Environment variable 'GPT_API_KEY' not found
Error: KeyError: 'OPENAI_TOKEN'
```

#### Diagnosis
```bash
# Check App Service configuration
az webapp config appsettings list --name filtro-be-dev-abc123 --resource-group rg-filtro-dev --output table

# Verify Key Vault reference format
az webapp config appsettings show --name filtro-be-dev-abc123 --resource-group rg-filtro-dev --setting-names GPT_API_KEY

# Test Key Vault access
az keyvault secret show --vault-name filtro-kv-dev-abc123 --name openai-api-key
```

#### Solutions
```bash
# 1. Fix Key Vault reference format
az webapp config appsettings set --name filtro-be-dev-abc123 --resource-group rg-filtro-dev \
  --settings GPT_API_KEY="@Microsoft.KeyVault(VaultName=filtro-kv-dev-abc123;SecretName=openai-api-key)"

# 2. Enable managed identity if not enabled
az webapp identity assign --name filtro-be-dev-abc123 --resource-group rg-filtro-dev

# 3. Grant Key Vault access
PRINCIPAL_ID=$(az webapp identity show --name filtro-be-dev-abc123 --resource-group rg-filtro-dev --query "principalId" -o tsv)
az keyvault set-policy --name filtro-kv-dev-abc123 --object-id $PRINCIPAL_ID --secret-permissions get list
```

## 🔄 GitHub Actions Issues

### Issue 10: Workflow Not Triggering

#### Symptoms
```
Workflow doesn't run on push/PR
Manual trigger not available
```

#### Diagnosis
```bash
# Check workflow syntax
gh workflow list

# Verify file paths in trigger
git log --oneline --name-only | head -10

# Check branch protection rules
gh api repos/:owner/:repo/branches/main/protection
```

#### Solutions
```bash
# 1. Fix workflow YAML syntax
yamllint .github/workflows/filtro-curricular-infrastructure.yml

# 2. Verify path filters
on:
  push:
    paths:
      - 'apps/infrastructure/azure/filtro-curricular/**'  # Ensure correct path

# 3. Check permissions
permissions:
  contents: read
  id-token: write  # Required for Azure authentication
```

### Issue 11: Artifact Upload/Download Failures

#### Symptoms
```
Error: Failed to upload artifact
Error: Artifact not found
```

#### Diagnosis
```bash
# Check artifact retention settings
gh api repos/:owner/:repo/actions/artifacts

# Verify artifact names match
gh run view --log | grep -i artifact
```

#### Solutions
```bash
# 1. Use consistent artifact names
- name: 📤 Upload Terraform Plan
  uses: actions/upload-artifact@v4
  with:
    name: terraform-plan-${{ needs.determine-environment.outputs.environment }}
    path: tfplan
    retention-days: 1

# 2. Ensure artifacts exist before download
- name: 📥 Download Terraform Plan
  uses: actions/download-artifact@v4
  with:
    name: terraform-plan-${{ needs.determine-environment.outputs.environment }}
```

## 📊 Monitoring and Debugging

### Debug Commands

#### GitHub Actions Debugging
```bash
# View recent workflow runs
gh run list --limit 10

# View specific run details
gh run view RUN_ID --log

# Re-run failed jobs
gh run rerun RUN_ID --failed
```

#### Azure Resource Debugging
```bash
# Check resource health
az resource list --resource-group rg-filtro-dev --query "[].{Name:name,Type:type,Location:location,Status:properties.provisioningState}" --output table

# Monitor App Service metrics
az monitor metrics list --resource "/subscriptions/.../resourceGroups/rg-filtro-dev/providers/Microsoft.Web/sites/filtro-be-dev-abc123" --metric "Requests" --interval PT1M

# Check Activity Log
az monitor activity-log list --resource-group rg-filtro-dev --start-time 2024-01-01T00:00:00Z --end-time 2024-01-01T23:59:59Z
```

#### Application Debugging
```bash
# Stream application logs
az webapp log tail --name filtro-be-dev-abc123 --resource-group rg-filtro-dev

# Download log files
az webapp log download --name filtro-be-dev-abc123 --resource-group rg-filtro-dev

# Check container logs
az webapp log config --name filtro-be-dev-abc123 --resource-group rg-filtro-dev --docker-container-logging filesystem
```

## 🆘 Emergency Procedures

### Rollback Deployment
```bash
# 1. Identify last known good deployment
gh run list --workflow=filtro-curricular-applications.yml --status=success --limit 5

# 2. Get previous image tag
PREVIOUS_TAG="sha-abc123"

# 3. Update App Service to use previous image
az webapp config container set \
  --name filtro-be-dev-abc123 \
  --resource-group rg-filtro-dev \
  --docker-custom-image-name "filtrocrdev.azurecr.io/filtro-curricular-be-api:$PREVIOUS_TAG"

# 4. Restart App Service
az webapp restart --name filtro-be-dev-abc123 --resource-group rg-filtro-dev
```

### Secret Compromise Response
```bash
# 1. Immediately revoke compromised secret at source (OpenAI, etc.)
# 2. Generate new secret
# 3. Update GitHub secret
gh secret set OPENAI_API_KEY --body "sk-proj-new-key"

# 4. Trigger infrastructure update
gh workflow run filtro-curricular-infrastructure.yml --field environment=dev

# 5. Monitor for unauthorized usage
az monitor activity-log list --resource-group rg-filtro-dev --start-time $(date -u -d '1 hour ago' +%Y-%m-%dT%H:%M:%SZ)
```

---

**Need More Help?**
- Check [Azure setup guide](../setup-guides/azure-setup.md) for initial configuration
- Review [secrets management documentation](../secrets-management/overview.md) for security issues
- Contact DevOps team for infrastructure problems
- Check GitHub Actions documentation for workflow issues
