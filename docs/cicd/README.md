# CI/CD and Secrets Management Documentation

This directory contains comprehensive documentation for the CI/CD workflows and secrets management processes in the ragtech repository.

## 📁 Documentation Structure

```
docs/cicd/
├── README.md                           # This file - overview and navigation
├── overview.md                         # High-level CI/CD architecture overview
├── workflows/
│   ├── cognisearch-aws.md             # Cognisearch application (AWS-based)
│   ├── filtro-curricular-azure.md     # Filtro Curricular application (Azure-based)
│   └── static-pages.md                # Static pages deployment
├── secrets-management/
│   ├── overview.md                    # Secrets management architecture
│   ├── azure-keyvault.md              # Azure Key Vault configuration
│   ├── github-secrets.md              # GitHub Secrets setup
│   └── secret-lifecycle.md            # Secret lifecycle and rotation
├── setup-guides/
│   ├── azure-setup.md                 # Azure infrastructure setup
│   ├── aws-setup.md                   # AWS infrastructure setup
│   └── github-actions-setup.md        # GitHub Actions configuration
├── troubleshooting/
│   ├── common-issues.md               # Common deployment issues
│   ├── secret-access-issues.md        # Secret access troubleshooting
│   └── debugging-guide.md             # Debugging workflows
└── diagrams/
    ├── cicd-overview.mermaid          # Overall CI/CD flow
    ├── azure-secrets-flow.mermaid     # Azure secrets management flow
    └── aws-deployment-flow.mermaid    # AWS deployment flow
```

## 🚀 Quick Start

1. **For new team members**: Start with [overview.md](overview.md) to understand the overall architecture
2. **For Azure deployments**: Read [filtro-curricular-azure.md](workflows/filtro-curricular-azure.md)
3. **For AWS deployments**: Read [cognisearch-aws.md](workflows/cognisearch-aws.md)
4. **For secrets setup**: Follow [azure-keyvault.md](secrets-management/azure-keyvault.md)
5. **For troubleshooting**: Check [common-issues.md](troubleshooting/common-issues.md)

## 🔑 Key Concepts

### Applications in this Repository

1. **Cognisearch** (`apps/cognisearch-cloud/`)
   - **Platform**: AWS
   - **Services**: ECR, ECS, S3, CloudFront
   - **Secrets**: AWS credentials, OpenAI keys

2. **Filtro Curricular** (`apps/filtro-curricular/`)
   - **Platform**: Azure
   - **Services**: Container Registry, App Services, Key Vault
   - **Secrets**: Azure credentials, OpenAI keys, Assistant IDs

3. **Static Pages** (`apps/static-pages/`)
   - **Platform**: AWS
   - **Services**: S3, CloudFront
   - **Secrets**: AWS credentials, CloudFront distribution IDs

### Environments

- **dev**: Development environment (auto-deploy from `dev` branch)
- **staging**: Staging environment (auto-deploy from `main` branch)
- **uat**: User Acceptance Testing (manual deployment)
- **prod**: Production environment (manual deployment with approval)

## 📊 Workflow Triggers

| Workflow | Trigger | Paths | Environments |
|----------|---------|-------|--------------|
| Cognisearch | Push to `main`, PR to `main` | `apps/cognisearch-cloud/**` | Production |
| Filtro Curricular Infrastructure | Push to `main`/`dev`, PR to `main` | `apps/infrastructure/azure/filtro-curricular/**` | dev, staging, uat, prod |
| Filtro Curricular Applications | Push to `main`/`dev`, PR to `main` | `apps/filtro-curricular/**` | dev, staging, uat, prod |
| Static Pages | Push to `main` | `apps/static-pages/**` | Production |

## 🔐 Critical Secrets

### OpenAI Secrets (Traced in this documentation)
- `OPENAI_TOKEN`: OpenAI API token for assistant API
- `GPT_API_KEY`: OpenAI API key for GPT models
- `ASSISTANT_ID_JURIDICO`: OpenAI Assistant ID for legal domain
- `ASSISTANT_ID_CALIDAD`: OpenAI Assistant ID for quality domain

### Platform Secrets
- `AZURE_CREDENTIALS`: Azure service principal credentials
- `AWS_ACCESS_KEY_ID` / `AWS_SECRET_ACCESS_KEY`: AWS credentials
- Various service-specific secrets (CloudFront distribution IDs, etc.)

## 📈 Monitoring and Observability

- **Azure**: Application Insights integration
- **AWS**: CloudWatch integration
- **GitHub Actions**: Workflow run logs and artifacts

## 🆘 Getting Help

1. Check the [troubleshooting guide](troubleshooting/common-issues.md)
2. Review workflow logs in GitHub Actions
3. Verify secret access in respective cloud platforms
4. Contact the DevOps team for infrastructure issues

---

**Last Updated**: 2025-06-16  
**Maintained By**: DevOps Team  
**Repository**: [ragtech](https://github.com/jherrera-rgt/ragtech)
