# Cognisearch - AWS CI/CD Workflow

## 📋 Overview

The Cognisearch application uses AWS services for deployment with a straightforward containerized approach. The workflow builds Docker images, pushes them to Amazon ECR, and updates ECS services.

## 🔄 Workflow File

- **File**: `.github/workflows/deploy.yml`
- **Purpose**: Builds and deploys Cognisearch frontend and backend applications
- **Platform**: AWS (ECR, ECS)

## 🎯 Triggers and Conditions

### Workflow Triggers
```yaml
on:
  push:
    branches: [main]
    paths:
      - apps/cognisearch-cloud/cgnsrch-chatbot-fe-ionic/**
      - apps/cognisearch-cloud/cgnsrch-chatbot-be-pfapi/**
  pull_request:
    branches: [main]
    paths:
      - apps/cognisearch-cloud/cgnsrch-chatbot-fe-ionic/**
      - apps/cognisearch-cloud/cgnsrch-chatbot-be-pfapi/**
```

### Path-Based Deployment
- **Frontend Changes**: `apps/cognisearch-cloud/cgnsrch-chatbot-fe-ionic/**`
- **Backend Changes**: `apps/cognisearch-cloud/cgnsrch-chatbot-be-pfapi/**`
- **Both**: Changes in either path trigger both deployments

## 🏗️ Deployment Architecture

```mermaid
graph TD
    A[Code Push to main] --> B{Path Filter}
    B -->|Frontend/Backend Changes| C[Build Frontend]
    B -->|Frontend/Backend Changes| D[Build Backend]
    
    C --> E[Push Frontend to ECR]
    D --> F[Push Backend to ECR]
    
    E --> G[Update Frontend ECS Service]
    F --> H[Update Backend ECS Service]
    
    G --> I[Frontend Health Check]
    H --> J[Backend Health Check]
    
    I --> K[Deployment Complete]
    J --> K
    
    style A fill:#e1f5fe
    style K fill:#c8e6c9
```

## 🚀 Deployment Flow

### Job 1: Frontend Deployment (`build_and_deploy_frontend`)

#### Steps Overview
1. **Checkout Code**: Get latest source code
2. **AWS Authentication**: Configure AWS credentials
3. **ECR Login**: Authenticate with Amazon ECR
4. **Build Image**: Create Docker image for frontend
5. **Push to ECR**: Upload image to container registry
6. **Update ECS**: Deploy new image to ECS service

#### Detailed Steps
```yaml
steps:
  - name: Checkout Code
    uses: actions/checkout@v3

  - name: Set up AWS credentials
    uses: aws-actions/configure-aws-credentials@v1
    with:
      aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
      aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      aws-region: sa-east-1

  - name: Log in to Amazon ECR
    run: |
      aws ecr get-login-password --region sa-east-1 | \
      docker login --username AWS --password-stdin ${{ secrets.AWS_ECR_URL }}

  - name: Build and Push Frontend Image to ECR
    working-directory: apps/cognisearch-cloud/cgnsrch-chatbot-fe-ionic
    run: |
      docker build -t ${{ secrets.AWS_ECR_URL }}/cgnsrch-chatbot-fe-ionic:latest .
      docker push ${{ secrets.AWS_ECR_URL }}/cgnsrch-chatbot-fe-ionic:latest

  - name: Update Frontend ECS Service
    run: |
      aws ecs update-service \
        --cluster cognisearch-cluster \
        --service frontend-service \
        --force-new-deployment \
        --region sa-east-1
```

### Job 2: Backend Deployment (`build_and_deploy_backend`)

#### Dependencies
- **Needs**: `build_and_deploy_frontend` (runs after frontend)
- **Reason**: Ensures frontend is available before backend deployment

#### Steps Overview
1. **Checkout Code**: Get latest source code
2. **AWS Authentication**: Configure AWS credentials
3. **ECR Login**: Authenticate with Amazon ECR
4. **Build Image**: Create Docker image for backend
5. **Push to ECR**: Upload image to container registry
6. **Update ECS**: Deploy new image to ECS service

#### Detailed Steps
```yaml
steps:
  - name: Checkout Code
    uses: actions/checkout@v3

  - name: Set up AWS credentials
    uses: aws-actions/configure-aws-credentials@v1
    with:
      aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
      aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      aws-region: sa-east-1

  - name: Log in to Amazon ECR
    run: |
      aws ecr get-login-password --region sa-east-1 | \
      docker login --username AWS --password-stdin ${{ secrets.AWS_ECR_URL }}

  - name: Build and Push Backend Image to ECR
    working-directory: apps/cognisearch-cloud/cgnsrch-chatbot-be-pfapi
    run: |
      docker build -t ${{ secrets.AWS_ECR_URL }}/cgnsrch-chatbot-be-pfapi:latest .
      docker push ${{ secrets.AWS_ECR_URL }}/cgnsrch-chatbot-be-pfapi:latest

  - name: Update Backend ECS Service
    run: |
      aws ecs update-service \
        --cluster cognisearch-cluster \
        --service backend-service \
        --force-new-deployment \
        --region sa-east-1
```

## 🔐 Secrets Management

### Required GitHub Secrets
```yaml
secrets:
  AWS_ACCESS_KEY_ID: # AWS access key for ECR/ECS access
  AWS_SECRET_ACCESS_KEY: # AWS secret key for ECR/ECS access
  AWS_ECR_URL: # ECR repository URL (e.g., 123456789.dkr.ecr.sa-east-1.amazonaws.com)
```

### Secret Usage Pattern
```mermaid
sequenceDiagram
    participant GH as GitHub Actions
    participant ECR as Amazon ECR
    participant ECS as Amazon ECS
    participant APP as Application
    
    GH->>GH: Read AWS credentials from secrets
    GH->>ECR: Authenticate and push image
    GH->>ECS: Update service with new image
    ECS->>ECR: Pull new image
    ECS->>APP: Start container with new image
    APP->>APP: Load environment variables
```

### Environment Variables in Containers
The applications receive environment variables directly from the ECS task definition:

```yaml
# Backend container environment
environment:
  - GPT_API_KEY: ${{ secrets.GPT_API_KEY }}
  - OPENAI_TOKEN: ${{ secrets.OPENAI_TOKEN }}
  - ASSISTANT_ID_JURIDICO: ${{ secrets.ASSISTANT_ID_JURIDICO }}
  - ASSISTANT_ID_CALIDAD: ${{ secrets.ASSISTANT_ID_CALIDAD }}
```

## 📦 Application Components

### Frontend Application (`cgnsrch-chatbot-fe-ionic`)
- **Technology**: Angular + Ionic + Caddy web server
- **Container**: Multi-stage Docker build
- **Port**: 80
- **Build Process**: 
  1. Node.js build stage for Angular compilation
  2. Caddy serving stage for static file delivery
- **Configuration**: Environment-specific backend URLs

### Backend Application (`cgnsrch-chatbot-be-pfapi`)
- **Technology**: Python Flask API
- **Container**: Python 3.11 with Flask
- **Port**: 5000
- **Secrets**: OpenAI credentials, AWS credentials
- **Features**: Document processing, AI integration

## 🌍 AWS Infrastructure

### Amazon ECR (Elastic Container Registry)
- **Purpose**: Store Docker images
- **Repositories**:
  - `cgnsrch-chatbot-fe-ionic`: Frontend images
  - `cgnsrch-chatbot-be-pfapi`: Backend images
- **Region**: `sa-east-1` (South America - São Paulo)

### Amazon ECS (Elastic Container Service)
- **Cluster**: `cognisearch-cluster`
- **Services**:
  - `frontend-service`: Runs frontend containers
  - `backend-service`: Runs backend containers
- **Deployment**: Rolling updates with force new deployment

### Networking and Load Balancing
- **Application Load Balancer**: Routes traffic to services
- **Target Groups**: Health checks and traffic distribution
- **Security Groups**: Network access control

## 🔧 Deployment Strategy

### Rolling Deployment
1. **Image Build**: New Docker image built and pushed to ECR
2. **Service Update**: ECS service updated with `--force-new-deployment`
3. **Task Replacement**: ECS gradually replaces old tasks with new ones
4. **Health Checks**: New tasks must pass health checks before old tasks are terminated
5. **Completion**: All tasks running new image version

### Zero-Downtime Deployment
- **Load Balancer**: Continues routing to healthy tasks
- **Health Checks**: Ensure new tasks are ready before receiving traffic
- **Gradual Rollout**: Old tasks terminated only after new tasks are healthy

## 📊 Monitoring and Health Checks

### ECS Service Monitoring
```bash
# Check service status
aws ecs describe-services \
  --cluster cognisearch-cluster \
  --services frontend-service backend-service \
  --region sa-east-1

# View task status
aws ecs list-tasks \
  --cluster cognisearch-cluster \
  --service-name backend-service \
  --region sa-east-1
```

### Application Health Endpoints
```bash
# Backend health check
curl https://api.cognisearch.cl/health

# Frontend availability
curl https://www.cognisearch.cl/
```

### CloudWatch Integration
- **Container Logs**: Automatically sent to CloudWatch Logs
- **Metrics**: CPU, memory, network metrics available
- **Alarms**: Can be configured for automated responses

## 🚨 Error Handling and Rollback

### Automatic Rollback Triggers
- **Health Check Failures**: If new tasks fail health checks
- **Service Instability**: If service becomes unstable after deployment
- **Resource Constraints**: If cluster lacks resources for new tasks

### Manual Rollback Process
```bash
# 1. Identify previous task definition
aws ecs describe-services \
  --cluster cognisearch-cluster \
  --services backend-service \
  --region sa-east-1 \
  --query "services[0].deployments"

# 2. Update service to previous task definition
aws ecs update-service \
  --cluster cognisearch-cluster \
  --service backend-service \
  --task-definition cognisearch-backend:PREVIOUS_REVISION \
  --region sa-east-1

# 3. Force new deployment
aws ecs update-service \
  --cluster cognisearch-cluster \
  --service backend-service \
  --force-new-deployment \
  --region sa-east-1
```

## 📈 Performance Optimizations

### Build Optimizations
- **Docker Layer Caching**: Optimized Dockerfile layer ordering
- **Multi-stage Builds**: Separate build and runtime environments
- **Parallel Jobs**: Frontend and backend build simultaneously

### Runtime Optimizations
- **ECS Service Auto Scaling**: Automatic scaling based on metrics
- **Application Load Balancer**: Efficient traffic distribution
- **CloudFront CDN**: Static asset caching and delivery

## 🔄 Continuous Improvement

### Planned Enhancements
- [ ] **Blue-Green Deployment**: Zero-downtime deployments
- [ ] **Automated Testing**: Integration and end-to-end tests
- [ ] **Security Scanning**: Container vulnerability scanning
- [ ] **Performance Monitoring**: Application performance insights
- [ ] **Cost Optimization**: Resource usage optimization

---

**Next Steps**:
- Review [AWS setup guide](../setup-guides/aws-setup.md) for infrastructure configuration
- Configure [monitoring and alerting](../troubleshooting/debugging-guide.md) for production
- Implement [security best practices](../troubleshooting/common-issues.md#security-issues)
