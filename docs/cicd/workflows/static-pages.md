# Static Pages - AWS S3 + CloudFront Deployment

## 📋 Overview

The static pages workflow handles deployment of static websites to AWS S3 with CloudFront CDN distribution. This workflow manages two separate static sites: Cognisearch and Ragtech company websites.

## 🔄 Workflow File

- **File**: `.github/workflows/deploy-static-pages.yml`
- **Purpose**: Deploy static HTML/CSS/JS files to S3 and invalidate CDN cache
- **Platform**: AWS (S3, CloudFront)

## 🎯 Triggers and Conditions

### Workflow Triggers
```yaml
on:
  push:
    branches: [main]
    paths:
      - 'apps/static-pages/**'
```

### Deployment Scope
- **Trigger**: Only when files in `apps/static-pages/` directory change
- **Branch**: Only `main` branch deployments
- **Sites**: Both Cognisearch and Ragtech sites deploy together

## 🏗️ Deployment Architecture

```mermaid
graph TD
    A[Push to main branch] --> B{Static Pages Changed?}
    B -->|Yes| C[Checkout Code]
    B -->|No| D[Skip Deployment]
    
    C --> E[Setup AWS Credentials]
    E --> F[Sync Cognisearch to S3]
    E --> G[Sync Ragtech to S3]
    
    F --> H[Invalidate Cognisearch CloudFront]
    G --> I[Invalidate Ragtech CloudFront]
    
    H --> J[Deployment Complete]
    I --> J
    
    style A fill:#e1f5fe
    style J fill:#c8e6c9
    style D fill:#ffecb3
```

## 🚀 Deployment Flow

### Job: `sync_and_invalidate`

#### Step 1: Environment Setup
```yaml
- name: Checkout Code
  uses: actions/checkout@v3

- name: Set up AWS credentials
  uses: aws-actions/configure-aws-credentials@v1
  with:
    aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
    aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
    aws-region: sa-east-1
```

#### Step 2: Cognisearch Site Deployment
```yaml
- name: Sync Cognisearch Static Pages to S3
  working-directory: apps/static-pages/staticpage-cognisearch-aws/site
  run: |
    aws s3 sync . s3://www.cognisearch.cl --delete --region sa-east-1
    echo "Cognisearch static pages synced to s3://www.cognisearch.cl"

- name: Invalidate CloudFront Cache - Cognisearch
  run: |
    aws cloudfront create-invalidation \
      --distribution-id ${{ secrets.COGNISEARCH_CLOUDFRONT_DISTRIBUTION_ID }} \
      --paths '/*'
    echo "CloudFront cache invalidated for www.cognisearch.cl"
```

#### Step 3: Ragtech Site Deployment
```yaml
- name: Sync Ragtech Static Pages to S3
  working-directory: apps/static-pages/staticpage-ragtech-aws/site
  run: |
    aws s3 sync . s3://www.ragtech.cl --delete --region sa-east-1
    echo "Ragtech static pages synced to s3://www.ragtech.cl"

- name: Invalidate CloudFront Cache - Ragtech
  run: |
    aws cloudfront create-invalidation \
      --distribution-id ${{ secrets.RAGTECH_CLOUDFRONT_DISTRIBUTION_ID }} \
      --paths '/*'
    echo "CloudFront cache invalidated for www.ragtech.cl"
```

## 🔐 Secrets Management

### Required GitHub Secrets
```yaml
secrets:
  AWS_ACCESS_KEY_ID: # AWS access key for S3/CloudFront access
  AWS_SECRET_ACCESS_KEY: # AWS secret key for S3/CloudFront access
  COGNISEARCH_CLOUDFRONT_DISTRIBUTION_ID: # CloudFront distribution ID for Cognisearch
  RAGTECH_CLOUDFRONT_DISTRIBUTION_ID: # CloudFront distribution ID for Ragtech
```

### Secret Usage Flow
```mermaid
sequenceDiagram
    participant GH as GitHub Actions
    participant S3 as Amazon S3
    participant CF as CloudFront
    participant DNS as Route 53
    participant User as End User
    
    GH->>GH: Read AWS credentials
    GH->>S3: Sync static files
    GH->>CF: Create cache invalidation
    CF->>S3: Fetch updated content
    User->>DNS: Request www.cognisearch.cl
    DNS->>CF: Route to CloudFront
    CF->>User: Serve cached/updated content
```

## 📁 Directory Structure

### Static Pages Organization
```
apps/static-pages/
├── staticpage-cognisearch-aws/
│   └── site/
│       ├── index.html
│       ├── css/
│       ├── js/
│       ├── images/
│       └── assets/
└── staticpage-ragtech-aws/
    └── site/
        ├── index.html
        ├── css/
        ├── js/
        ├── images/
        └── assets/
```

### File Sync Behavior
- **Source**: Local `site/` directories
- **Destination**: S3 buckets (`s3://www.cognisearch.cl`, `s3://www.ragtech.cl`)
- **Sync Options**: `--delete` (removes files not present in source)
- **Region**: `sa-east-1` (South America - São Paulo)

## 🌐 AWS Infrastructure

### Amazon S3 Buckets
#### Cognisearch Bucket (`www.cognisearch.cl`)
- **Purpose**: Host Cognisearch static website
- **Configuration**: Static website hosting enabled
- **Public Access**: Configured for public read access
- **CORS**: Configured for cross-origin requests

#### Ragtech Bucket (`www.ragtech.cl`)
- **Purpose**: Host Ragtech static website
- **Configuration**: Static website hosting enabled
- **Public Access**: Configured for public read access
- **CORS**: Configured for cross-origin requests

### CloudFront Distributions
#### Cognisearch Distribution
- **Origin**: S3 bucket `www.cognisearch.cl`
- **Domain**: `www.cognisearch.cl`
- **SSL**: AWS Certificate Manager certificate
- **Caching**: Optimized for static content

#### Ragtech Distribution
- **Origin**: S3 bucket `www.ragtech.cl`
- **Domain**: `www.ragtech.cl`
- **SSL**: AWS Certificate Manager certificate
- **Caching**: Optimized for static content

### Route 53 DNS
- **Cognisearch**: DNS records pointing to CloudFront distribution
- **Ragtech**: DNS records pointing to CloudFront distribution
- **SSL Certificates**: Managed by AWS Certificate Manager

## 🔧 Deployment Process Details

### S3 Sync Operation
```bash
# Sync command breakdown
aws s3 sync . s3://www.cognisearch.cl \
  --delete \                    # Remove files not in source
  --region sa-east-1           # Specify region
  
# What happens:
# 1. Compare local files with S3 objects
# 2. Upload new/modified files
# 3. Delete S3 objects not present locally
# 4. Preserve file metadata and permissions
```

### CloudFront Cache Invalidation
```bash
# Invalidation command breakdown
aws cloudfront create-invalidation \
  --distribution-id E1234567890ABC \  # Distribution ID from secrets
  --paths '/*'                        # Invalidate all paths

# What happens:
# 1. Create invalidation request for all files
# 2. CloudFront marks cached content as stale
# 3. Next requests fetch fresh content from S3
# 4. New content gets cached at edge locations
```

## 📊 Monitoring and Verification

### Deployment Verification
```bash
# Check S3 sync status
aws s3 ls s3://www.cognisearch.cl --recursive --human-readable

# Verify CloudFront invalidation
aws cloudfront list-invalidations --distribution-id E1234567890ABC

# Test website accessibility
curl -I https://www.cognisearch.cl
curl -I https://www.ragtech.cl
```

### Performance Monitoring
```bash
# CloudFront metrics
aws cloudwatch get-metric-statistics \
  --namespace AWS/CloudFront \
  --metric-name Requests \
  --dimensions Name=DistributionId,Value=E1234567890ABC \
  --start-time 2024-01-01T00:00:00Z \
  --end-time 2024-01-01T23:59:59Z \
  --period 3600 \
  --statistics Sum

# S3 bucket metrics
aws cloudwatch get-metric-statistics \
  --namespace AWS/S3 \
  --metric-name BucketSizeBytes \
  --dimensions Name=BucketName,Value=www.cognisearch.cl Name=StorageType,Value=StandardStorage \
  --start-time 2024-01-01T00:00:00Z \
  --end-time 2024-01-01T23:59:59Z \
  --period 86400 \
  --statistics Average
```

## 🚨 Error Handling and Troubleshooting

### Common Issues

#### Issue 1: S3 Sync Permission Denied
```bash
# Check bucket policy
aws s3api get-bucket-policy --bucket www.cognisearch.cl

# Verify IAM permissions
aws iam get-user-policy --user-name github-actions-user --policy-name S3AccessPolicy
```

#### Issue 2: CloudFront Invalidation Fails
```bash
# Check distribution status
aws cloudfront get-distribution --id E1234567890ABC

# Verify invalidation limits (1000 free per month)
aws cloudfront list-invalidations --distribution-id E1234567890ABC
```

#### Issue 3: Website Not Updating
```bash
# Check invalidation status
aws cloudfront get-invalidation \
  --distribution-id E1234567890ABC \
  --id INVALIDATION_ID

# Force browser cache refresh
curl -H "Cache-Control: no-cache" https://www.cognisearch.cl
```

### Rollback Procedure
```bash
# 1. Identify previous version (if versioning enabled)
aws s3api list-object-versions --bucket www.cognisearch.cl

# 2. Restore previous version
aws s3 sync s3://backup-bucket/cognisearch-backup/ s3://www.cognisearch.cl --delete

# 3. Invalidate CloudFront cache
aws cloudfront create-invalidation \
  --distribution-id $COGNISEARCH_CLOUDFRONT_DISTRIBUTION_ID \
  --paths '/*'
```

## 📈 Performance Optimizations

### S3 Optimizations
- **Transfer Acceleration**: Enabled for faster uploads
- **Compression**: Gzip compression for text files
- **Metadata**: Proper Content-Type headers set
- **Lifecycle Policies**: Automatic cleanup of old versions

### CloudFront Optimizations
- **Caching Behavior**: Optimized TTL for different file types
- **Compression**: Automatic compression enabled
- **HTTP/2**: Enabled for better performance
- **Edge Locations**: Global distribution for low latency

### Cost Optimization
- **S3 Storage Class**: Standard for frequently accessed content
- **CloudFront Price Class**: Optimized for target regions
- **Monitoring**: CloudWatch alarms for unusual usage

## 🔄 Continuous Improvement

### Planned Enhancements
- [ ] **Automated Testing**: HTML validation and link checking
- [ ] **Performance Testing**: Lighthouse CI integration
- [ ] **Security Headers**: Security header validation
- [ ] **SEO Optimization**: SEO score monitoring
- [ ] **A/B Testing**: CloudFront-based A/B testing

### Best Practices Implemented
- ✅ **Atomic Deployments**: S3 sync with delete ensures consistency
- ✅ **Cache Invalidation**: Immediate content updates via CloudFront
- ✅ **SSL/TLS**: HTTPS enforced with AWS Certificate Manager
- ✅ **Global CDN**: CloudFront edge locations for performance
- ✅ **Monitoring**: CloudWatch metrics and alarms

---

**Next Steps**:
- Configure [AWS infrastructure](../setup-guides/aws-setup.md) for static hosting
- Set up [monitoring and alerting](../troubleshooting/debugging-guide.md) for websites
- Implement [security best practices](../troubleshooting/common-issues.md#security-headers)
