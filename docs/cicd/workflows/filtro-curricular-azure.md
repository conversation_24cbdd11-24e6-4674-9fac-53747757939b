# Filtro Curricular - Azure CI/CD Workflow

## 📋 Overview

The Filtro Curricular application uses a two-stage deployment approach on Azure:
1. **Infrastructure Deployment**: Provisions Azure resources using Terraform
2. **Application Deployment**: Builds and deploys containerized applications

## 🔄 Workflow Files

### 1. Infrastructure Workflow
- **File**: `.github/workflows/filtro-curricular-infrastructure.yml`
- **Purpose**: Provisions and manages Azure infrastructure
- **Terraform**: Uses Infrastructure as Code

### 2. Application Workflow
- **File**: `.github/workflows/filtro-curricular-applications.yml`
- **Purpose**: Builds and deploys backend/frontend applications
- **Dependencies**: Requires infrastructure to be deployed first

## 🎯 Triggers and Conditions

### Infrastructure Workflow Triggers
```yaml
on:
  push:
    branches: [ main, dev ]
    paths:
      - 'apps/infrastructure/azure/filtro-curricular/**'
      - '.github/workflows/filtro-curricular-infrastructure.yml'
  pull_request:
    branches: [ main ]
    paths:
      - 'apps/infrastructure/azure/filtro-curricular/**'
  workflow_dispatch:
    inputs:
      environment: [dev, staging, uat, prod]
      destroy: boolean
```

### Application Workflow Triggers
```yaml
on:
  push:
    branches: [ main, dev ]
    paths:
      - 'apps/filtro-curricular/filtro-curricular-be-api/**'
      - 'apps/filtro-curricular/filtro-curricular-fe-web/**'
  pull_request:
    branches: [ main ]
  workflow_dispatch:
    inputs:
      environment: [dev, staging, uat, prod]
      component: [both, backend, frontend]
      force_deploy: boolean
```

## 🏗️ Infrastructure Deployment Flow

```mermaid
graph TD
    A[Trigger: Push/PR/Manual] --> B[Determine Environment]
    B --> C{Should Deploy?}
    C -->|No| D[Skip Deployment]
    C -->|Yes| E[Setup Terraform]
    E --> F[Azure Authentication]
    F --> G[Create terraform.tfvars]
    G --> H[Terraform Init]
    H --> I[Terraform Validate]
    I --> J[Terraform Plan]
    J --> K[Upload Plan Artifact]
    K --> L[Terraform Apply]
    L --> M[Extract Outputs]
    M --> N[Notify Success]
    
    style A fill:#e1f5fe
    style N fill:#c8e6c9
    style D fill:#ffecb3
```

### Key Infrastructure Components
1. **Resource Group**: Container for all resources
2. **Azure Container Registry**: Stores Docker images
3. **App Service Plan**: Compute resources for applications
4. **App Services**: Backend and Frontend web applications
5. **Key Vault**: Centralized secrets management
6. **Storage Account**: File storage and application data
7. **Application Insights**: Monitoring and telemetry

## 🚀 Application Deployment Flow

```mermaid
graph TD
    A[Trigger: Code Changes] --> B[Determine Environment]
    B --> C[Detect Changes]
    C --> D{Deploy Backend?}
    C --> E{Deploy Frontend?}
    
    D -->|Yes| F[Build Backend Image]
    F --> G[Push to ACR]
    G --> H[Restart Backend App Service]
    
    E -->|Yes| I[Build Frontend Image]
    I --> J[Push to ACR]
    J --> K[Restart Frontend App Service]
    
    H --> L[Health Check Backend]
    K --> M[Health Check Frontend]
    L --> N[Deployment Complete]
    M --> N
    
    style A fill:#e1f5fe
    style N fill:#c8e6c9
```

## 🔐 Secrets Management Integration

### GitHub Secrets Used
```yaml
secrets:
  AZURE_CREDENTIALS: # Service Principal for Azure authentication
  OPENAI_API_KEY: # OpenAI API key for GPT models
  OPENAI_TOKEN: # OpenAI token for assistant API
  ASSISTANT_ID_JURIDICO: # Legal domain assistant ID
  ASSISTANT_ID_CALIDAD: # Quality domain assistant ID
```

### Azure Key Vault Integration
The workflow automatically:
1. **Stores secrets** in Azure Key Vault during infrastructure deployment
2. **Configures App Services** with Key Vault references
3. **Enables managed identity** for secure secret access

### Secret Flow in terraform.tfvars
```hcl
# OpenAI Configuration
openai_api_key        = "${{ secrets.OPENAI_API_KEY }}"
openai_token          = "${{ secrets.OPENAI_TOKEN }}"
assistant_id_juridico = "${{ secrets.ASSISTANT_ID_JURIDICO }}"
assistant_id_calidad  = "${{ secrets.ASSISTANT_ID_CALIDAD }}"
```

## 🌍 Environment-Specific Behavior

### Environment Determination Logic
```yaml
# Automatic environment mapping
- main branch → staging environment
- dev branch → dev environment
- manual dispatch → user-selected environment
```

### Environment-Specific Configurations
| Environment | Log Level | Debug Mode | Cache TTL | Retry Attempts |
|-------------|-----------|------------|-----------|----------------|
| **dev** | DEBUG | true | 300s | 3 |
| **staging** | INFO | false | 600s | 5 |
| **prod** | WARNING | false | 1800s | 5 |

## 📦 Application Components

### Backend API (`filtro-curricular-be-api`)
- **Technology**: Python Flask API
- **Container**: Docker with Python 3.11
- **Port**: 5000
- **Secrets**: OpenAI credentials, storage connection strings
- **Health Check**: `/health` endpoint

### Frontend Web (`filtro-curricular-fe-web`)
- **Technology**: Angular + Caddy web server
- **Container**: Multi-stage Docker build
- **Port**: 80
- **Configuration**: Environment-specific backend URLs
- **Health Check**: HTTP 200 on root path

## 🔧 Key Workflow Jobs

### Infrastructure Jobs
1. **determine-environment**: Decides target environment and deployment necessity
2. **terraform-plan**: Validates and plans infrastructure changes
3. **terraform-apply**: Applies infrastructure changes with approval gates

### Application Jobs
1. **determine-environment**: Environment and change detection
2. **get-infrastructure-info**: Retrieves infrastructure outputs
3. **build-and-test**: Builds and tests applications
4. **deploy-backend**: Deploys backend container
5. **deploy-frontend**: Deploys frontend container

## 📊 Monitoring and Health Checks

### Application Health Monitoring
```bash
# Backend health check
curl https://${backend_app_name}.azurewebsites.net/health

# Frontend health check
curl https://${frontend_app_name}.azurewebsites.net/
```

### Deployment Verification
- **Container Registry**: Verify image push success
- **App Service**: Confirm service restart and health
- **Key Vault**: Validate secret access
- **Application Insights**: Monitor application telemetry

## 🚨 Error Handling and Rollback

### Automatic Rollback Triggers
- Health check failures after deployment
- Container startup failures
- Key Vault access issues

### Manual Rollback Process
1. Identify last known good deployment
2. Redeploy previous container image version
3. Verify application health
4. Update monitoring dashboards

## 📈 Performance Optimizations

### Build Optimizations
- **Docker layer caching**: Reduces build times
- **Parallel job execution**: Backend and frontend deploy simultaneously
- **Conditional deployments**: Only deploy changed components

### Runtime Optimizations
- **App Service warm-up**: Prevents cold start delays
- **Connection pooling**: Optimized database connections
- **CDN integration**: Static asset delivery optimization

---

**Next Steps**:
- Review [secrets management documentation](../secrets-management/azure-keyvault.md)
- Set up [Azure infrastructure](../setup-guides/azure-setup.md)
- Configure [monitoring and alerting](../troubleshooting/debugging-guide.md)
