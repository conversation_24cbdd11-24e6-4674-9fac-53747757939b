# Terraform State Lock Troubleshooting Guide for Azure

## Overview

This guide provides comprehensive instructions for resolving Terraform state lock issues when using Azure Storage as the backend. State locks are a safety mechanism that prevents multiple users or processes from modifying the same Terraform state simultaneously, but they can sometimes become stuck and require manual intervention.

## Problem Description

Terraform state locks occur when a Terraform operation is interrupted or fails to complete properly, leaving the state file locked in Azure Storage. This prevents any subsequent Terraform operations from running until the lock is manually released.

## Common Error Symptoms

### Typical Error Message
```
Error: Error acquiring the state lock

Error message: state blob is already locked
Lock Info:
  ID:        93e0832a-f6da-6fbd-93e3-88c5defc4caa
  Path:      terraform-state/filtro-curricular-dev.tfstate
  Operation: OperationTypePlan
  Who:       runner@fv-az811-352
  Version:   1.5.0
  Created:   2025-06-16 20:16:00.793944965 +0000 UTC
  Info:      

Terraform acquires a state lock to protect the state from being written
by multiple users at the same time. Please resolve the issue above and try
again. For most commands, you can disable locking with the "-lock=false"
flag, but this is not recommended.
```

### Key Information from Error
- **Lock ID**: Unique identifier for the lock
- **Path**: Location of the state file in Azure Storage
- **Who**: The user/system that created the lock
- **Created**: Timestamp when the lock was created
- **Operation**: The Terraform operation that created the lock

## Prerequisites

Before proceeding, ensure you have:
- Azure CLI installed and configured
- Appropriate permissions to access the Azure Storage Account
- Access to the subscription containing the Terraform state storage

## Solution Steps

### Step 1: Authenticate to Azure

```bash
# Login to Azure (if not already authenticated)
az login

# Verify you're in the correct subscription
az account show --query "name" --output tsv

# Set the correct subscription if needed
az account set --subscription "YOUR_SUBSCRIPTION_ID"
```

### Step 2: Identify Backend Configuration

From your Terraform backend configuration, identify:
- **Resource Group**: `filtro-curricular-terraform-state-rg`
- **Storage Account**: `filtrocurriculartfstate`
- **Container**: `terraform-state`
- **State File**: `filtro-curricular-dev.tfstate` (or your specific environment)

### Step 3: Primary Method - Break the Lease

```bash
# Break the lease on the state blob to unlock it
az storage blob lease break \
  --account-name "filtrocurriculartfstate" \
  --container-name "terraform-state" \
  --blob-name "filtro-curricular-dev.tfstate"
```

### Step 4: Alternative Method - Using Storage Account Key

If the primary method fails due to authentication issues:

```bash
# Get the storage account key
STORAGE_KEY=$(az storage account keys list \
  --resource-group "filtro-curricular-terraform-state-rg" \
  --account-name "filtrocurriculartfstate" \
  --query "[0].value" \
  --output tsv)

# Break the lease using the storage account key
az storage blob lease break \
  --account-name "filtrocurriculartfstate" \
  --account-key "$STORAGE_KEY" \
  --container-name "terraform-state" \
  --blob-name "filtro-curricular-dev.tfstate"
```

## Verification Steps

### Confirm the Lock is Released

```bash
# Check the lease status of the blob
az storage blob show \
  --account-name "filtrocurriculartfstate" \
  --container-name "terraform-state" \
  --name "filtro-curricular-dev.tfstate" \
  --query "properties.lease" \
  --output table
```

Expected output should show:
- **Status**: `unlocked` or `available`
- **State**: `available`

### Test Terraform Operations

```bash
# Navigate to your Terraform directory
cd apps/infrastructure/azure/filtro-curricular

# Test with a simple Terraform command
terraform init
terraform plan
```

## When State Locks Occur

State locks typically occur in the following scenarios:

### 1. **Interrupted Operations**
- GitHub Actions jobs cancelled mid-execution
- Network timeouts during Terraform operations
- Manual interruption (Ctrl+C) during local runs

### 2. **System Failures**
- CI/CD runner crashes or is terminated unexpectedly
- Network connectivity issues
- Azure Storage service interruptions

### 3. **Concurrent Operations**
- Multiple users running Terraform simultaneously
- Overlapping CI/CD pipeline executions
- Automated and manual operations running concurrently

## Prevention Best Practices

### 1. **CI/CD Pipeline Design**
```yaml
# Use proper job dependencies to prevent concurrent runs
jobs:
  terraform-plan:
    # ... job configuration
  
  terraform-apply:
    needs: terraform-plan
    # Ensures apply only runs after plan completes
```

### 2. **Timeout Configuration**
```bash
# Set reasonable timeouts for Terraform operations
terraform plan -lock-timeout=10m
terraform apply -lock-timeout=10m
```

### 3. **Monitoring and Alerts**
- Set up monitoring for long-running Terraform operations
- Configure alerts for failed CI/CD pipelines
- Implement automatic cleanup for stale locks

### 4. **Team Coordination**
- Establish clear deployment windows
- Use branch protection rules to control when deployments occur
- Implement approval processes for production deployments

## Advanced Troubleshooting

### Check Lock Details
```bash
# Get detailed information about the blob and any active leases
az storage blob show \
  --account-name "filtrocurriculartfstate" \
  --container-name "terraform-state" \
  --name "filtro-curricular-dev.tfstate" \
  --output json
```

### Force Unlock with Terraform (Use with Caution)
```bash
# Only use this if Azure CLI methods fail
terraform force-unlock LOCK_ID
```

⚠️ **Warning**: Only use `terraform force-unlock` as a last resort, and ensure no other Terraform operations are running.

## Emergency Procedures

If all unlock methods fail:

1. **Contact Azure Support** for assistance with storage account issues
2. **Restore from backup** if you have state file backups
3. **Recreate state** by importing existing resources (advanced users only)

## Related Documentation

- [Terraform State Locking Documentation](https://www.terraform.io/docs/language/state/locking.html)
- [Azure Storage Blob Leases](https://docs.microsoft.com/en-us/rest/api/storageservices/lease-blob)
- [Terraform Azure Backend Configuration](https://www.terraform.io/docs/language/settings/backends/azurerm.html)

---

**Last Updated**: 2025-06-17  
**Applies To**: Terraform with Azure Storage Backend  
**Tested With**: Terraform v1.5.0, Azure CLI v2.x
