# megamind-poc

## Stack usable

- Workflow Data
    - Airflow
    - Kubeflow

- AI Workflow
    - Langchain
  
- Apps multiplataforma
    - <https://flutter.dev/> FLutter

### Arquitectura RAG

- [RAG Retrieval Augmented Generation Architecture](https://medium.com/@danielbelemduarte/llm-architectures-rag-what-it-takes-to-scale-92f2d0114d3c)
- [Fine Tunning VS Embeddings](https://www.athos-cap.com/como-crear-un-chatgpt-privado-con-tus-propios-datos/)
- [How to Build a State-of-the-Art (SOTA) RAG Engine?](https://www.lyzr.ai/how-to-build-a-state-of-the-art-sota-rag-engine/)
- [Mixture of Experts](https://medium.com/@bijit211987/mixture-of-experts-moe-scaling-ai-horizons-44de79ba2e89)

### Ejemplos Ollama

- [ollama](https://github.com/jmorganca/ollama)
- [libreria de modelos en ollama](https://ollama.ai/library)
- [Run Llama2 uncersored locally](https://ollama.ai/blog/run-llama2-uncensored-locally)
- [Ejemplos de Codigo ollama](https://github.com/jmorganca/ollama/tree/main/examples)
- [langchain python rag websummary](https://github.com/jmorganca/ollama/tree/main/examples/langchain-python-rag-websummary)

## Links to Read

- [GPT Store Setup](https://blog.finxter.com/openai-gpt-store-get-your-gpts-ready-in-5-simple-steps/?tl_inbound=1&tl_target_all=1&tl_period_type=3)

## Links on Prompt

- [https://www.promptingguide.ai/introduction/tips](https://www.promptingguide.ai/introduction/tips)
- [Best Prompt Techniques for Best LLM Responses](https://medium.com/the-modern-scientist/best-prompt-techniques-for-best-llm-responses-24d2ff4f6bca)

## InfoSec

- [NIST Revela Desafíos Cruciales en Aprendizaje Automático Adversarial: Un Análisis Profundo de Riesgos y Estrategias de Mitigación](https://ciberseguridad-es.blogspot.com/2024/01/nist-revela-desafios-cruciales-en.html)

## QA

[QA](doc/project/QA-README.md)
