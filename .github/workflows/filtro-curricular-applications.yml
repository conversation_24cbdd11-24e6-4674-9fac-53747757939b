name: 🚀 Filtro Curricular - Application Deployment

on:
  push:
    branches: [ main, dev ]
    paths:
      - 'apps/filtro-curricular/filtro-curricular-be-api/**'
      - 'apps/filtro-curricular/filtro-curricular-fe-web/**'
      - '.github/workflows/filtro-curricular-applications.yml'
  pull_request:
    branches: [ main ]
    paths:
      - 'apps/filtro-curricular/filtro-curricular-be-api/**'
      - 'apps/filtro-curricular/filtro-curricular-fe-web/**'
      - '.github/workflows/filtro-curricular-applications.yml'
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'dev'
        type: choice
        options:
          - dev
          - staging
          - uat
          - prod
      component:
        description: 'Component to deploy'
        required: true
        default: 'both'
        type: choice
        options:
          - both
          - backend
          - frontend
      force_deploy:
        description: 'Force deployment regardless of changes'
        required: false
        default: false
        type: boolean

env:
  TERRAFORM_WORKING_DIRECTORY: 'apps/infrastructure/azure/filtro-curricular'

jobs:
  determine-environment:
    name: 🎯 Determine Environment
    runs-on: ubuntu-latest
    outputs:
      environment: ${{ steps.env.outputs.environment }}
      should_deploy: ${{ steps.env.outputs.should_deploy }}
      deploy_backend: ${{ steps.changes.outputs.backend }}
      deploy_frontend: ${{ steps.changes.outputs.frontend }}
    steps:
      - name: 📥 Checkout Code
        uses: actions/checkout@v4
        with:
          fetch-depth: 2
      
      - name: 🔍 Detect Changes
        id: changes
        run: |
          if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
            # Check if force deploy is enabled
            if [ "${{ github.event.inputs.force_deploy }}" = "true" ]; then
              echo "🚨 Force deployment enabled - deploying both components"
              echo "backend=true" >> $GITHUB_OUTPUT
              echo "frontend=true" >> $GITHUB_OUTPUT
            elif [ "${{ github.event.inputs.component }}" = "both" ]; then
              echo "backend=true" >> $GITHUB_OUTPUT
              echo "frontend=true" >> $GITHUB_OUTPUT
            elif [ "${{ github.event.inputs.component }}" = "backend" ]; then
              echo "backend=true" >> $GITHUB_OUTPUT
              echo "frontend=false" >> $GITHUB_OUTPUT
            elif [ "${{ github.event.inputs.component }}" = "frontend" ]; then
              echo "backend=false" >> $GITHUB_OUTPUT
              echo "frontend=true" >> $GITHUB_OUTPUT
            fi
          else
            # Check for changes in backend
            if git diff --name-only HEAD~1 HEAD | grep -q "apps/filtro-curricular/filtro-curricular-be-api/"; then
              echo "backend=true" >> $GITHUB_OUTPUT
            else
              echo "backend=false" >> $GITHUB_OUTPUT
            fi

            # Check for changes in frontend
            if git diff --name-only HEAD~1 HEAD | grep -q "apps/filtro-curricular/filtro-curricular-fe-web/"; then
              echo "frontend=true" >> $GITHUB_OUTPUT
            else
              echo "frontend=false" >> $GITHUB_OUTPUT
            fi
          fi
      
      - name: Determine environment and deployment
        id: env
        run: |
          if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
            echo "environment=${{ github.event.inputs.environment }}" >> $GITHUB_OUTPUT
            echo "should_deploy=true" >> $GITHUB_OUTPUT
          elif [ "${{ github.ref }}" = "refs/heads/main" ]; then
            echo "environment=staging" >> $GITHUB_OUTPUT
            echo "should_deploy=true" >> $GITHUB_OUTPUT
          elif [ "${{ github.ref }}" = "refs/heads/dev" ]; then
            echo "environment=dev" >> $GITHUB_OUTPUT
            echo "should_deploy=true" >> $GITHUB_OUTPUT
          else
            echo "environment=dev" >> $GITHUB_OUTPUT
            echo "should_deploy=false" >> $GITHUB_OUTPUT
          fi

  get-infrastructure-info:
    name: 📊 Get Infrastructure Info
    runs-on: ubuntu-latest
    needs: determine-environment
    if: needs.determine-environment.outputs.should_deploy == 'true'
    environment: ${{ needs.determine-environment.outputs.environment }}
    
    defaults:
      run:
        working-directory: ${{ env.TERRAFORM_WORKING_DIRECTORY }}
    
    outputs:
      acr_name: ${{ steps.terraform-output.outputs.acr_name }}
      acr_login_server: ${{ steps.terraform-output.outputs.acr_login_server }}
      resource_group_name: ${{ steps.terraform-output.outputs.resource_group_name }}
      backend_app_name: ${{ steps.terraform-output.outputs.backend_app_name }}
      frontend_app_name: ${{ steps.terraform-output.outputs.frontend_app_name }}
    
    steps:
    - name: 📥 Checkout Code
      uses: actions/checkout@v4
    
    - name: 🔧 Setup Terraform
      uses: hashicorp/setup-terraform@v3
      with:
        terraform_version: '1.5.0'
        terraform_wrapper: false
    
    - name: 🔐 Azure Login
      uses: azure/login@v2
      with:
        creds: ${{ secrets.AZURE_CREDENTIALS }}
        enable-AzPSSession: true
        environment: azurecloud
        allow-no-subscriptions: false
        audience: api://AzureADTokenExchange
        auth-type: SERVICE_PRINCIPAL

    - name: 📝 Set ARM Environment Variables
      run: |
        # Extract Azure credentials from JSON and set ARM environment variables for Terraform
        echo "ARM_CLIENT_ID=$(echo '${{ secrets.AZURE_CREDENTIALS }}' | jq -r '.clientId')" >> $GITHUB_ENV
        echo "ARM_CLIENT_SECRET=$(echo '${{ secrets.AZURE_CREDENTIALS }}' | jq -r '.clientSecret')" >> $GITHUB_ENV
        echo "ARM_SUBSCRIPTION_ID=$(echo '${{ secrets.AZURE_CREDENTIALS }}' | jq -r '.subscriptionId')" >> $GITHUB_ENV
        echo "ARM_TENANT_ID=$(echo '${{ secrets.AZURE_CREDENTIALS }}' | jq -r '.tenantId')" >> $GITHUB_ENV
        echo "ARM_USE_CLI=false" >> $GITHUB_ENV

    - name: 🚀 Terraform Init
      run: terraform init
    
    - name: 📊 Get Terraform Outputs
      id: terraform-output
      run: |
        echo "acr_name=$(terraform output -raw container_registry_name)" >> $GITHUB_OUTPUT
        echo "acr_login_server=$(terraform output -raw container_registry_login_server)" >> $GITHUB_OUTPUT
        echo "resource_group_name=$(terraform output -raw resource_group_name)" >> $GITHUB_OUTPUT
        echo "backend_app_name=$(terraform output -raw backend_app_service_name)" >> $GITHUB_OUTPUT
        echo "frontend_app_name=$(terraform output -raw frontend_app_service_name)" >> $GITHUB_OUTPUT

  build-and-test:
    name: 🧪 Build and Test
    runs-on: ubuntu-latest
    needs: [determine-environment, get-infrastructure-info]
    if: needs.determine-environment.outputs.should_deploy == 'true'
    
    steps:
    - name: 📥 Checkout Code
      uses: actions/checkout@v4
    
    - name: 🐍 Setup Python (for backend tests)
      if: needs.determine-environment.outputs.deploy_backend == 'true'
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: 🔧 Setup Node.js (for frontend tests)
      if: needs.determine-environment.outputs.deploy_frontend == 'true'
      uses: actions/setup-node@v4
      with:
        node-version: '20'
    
    - name: 🧪 Test Backend
      if: needs.determine-environment.outputs.deploy_backend == 'true'
      working-directory: apps/filtro-curricular/filtro-curricular-be-api/src
      run: |
        pip install -r requirements.txt
        # Add your backend tests here
        echo "Backend tests passed"
    
    - name: 🧪 Test Frontend
      if: needs.determine-environment.outputs.deploy_frontend == 'true'
      working-directory: apps/filtro-curricular/filtro-curricular-fe-web
      run: |
        npm ci
        # npm run test -- --watch=false --browsers=ChromeHeadless
        echo "Frontend tests passed"

  deploy-backend:
    name: 🚀 Deploy Backend
    runs-on: ubuntu-latest
    needs: [determine-environment, get-infrastructure-info, build-and-test]
    if: needs.determine-environment.outputs.should_deploy == 'true' && needs.determine-environment.outputs.deploy_backend == 'true'
    environment: ${{ needs.determine-environment.outputs.environment }}
    
    steps:
    - name: 📥 Checkout Code
      uses: actions/checkout@v4
    
    - name: 🔐 Azure Login
      uses: azure/login@v2
      with:
        creds: ${{ secrets.AZURE_CREDENTIALS }}
        enable-AzPSSession: true
        environment: azurecloud
        allow-no-subscriptions: false
        audience: api://AzureADTokenExchange
        auth-type: SERVICE_PRINCIPAL

    - name: 🔑 Login to Azure Container Registry
      run: az acr login --name ${{ needs.get-infrastructure-info.outputs.acr_name }}
    
    - name: 🏗️ Build Backend Image
      working-directory: apps/filtro-curricular/filtro-curricular-be-api
      run: |
        IMAGE_TAG="${{ github.sha }}"
        ACR_SERVER="${{ needs.get-infrastructure-info.outputs.acr_login_server }}"
        
        docker build \
          --tag "$ACR_SERVER/filtro-curricular-be-api:$IMAGE_TAG" \
          --tag "$ACR_SERVER/filtro-curricular-be-api:latest" \
          --tag "$ACR_SERVER/filtro-curricular-be-api:${{ needs.determine-environment.outputs.environment }}-latest" \
          .
    
    - name: 📤 Push Backend Image
      run: |
        ACR_SERVER="${{ needs.get-infrastructure-info.outputs.acr_login_server }}"
        IMAGE_TAG="${{ github.sha }}"
        
        docker push "$ACR_SERVER/filtro-curricular-be-api:$IMAGE_TAG"
        docker push "$ACR_SERVER/filtro-curricular-be-api:latest"
        docker push "$ACR_SERVER/filtro-curricular-be-api:${{ needs.determine-environment.outputs.environment }}-latest"
    
    - name: 🔄 Restart Backend App Service
      run: |
        echo "Restarting backend app service to pick up new container image..."
        az webapp restart \
          --name ${{ needs.get-infrastructure-info.outputs.backend_app_name }} \
          --resource-group ${{ needs.get-infrastructure-info.outputs.resource_group_name }}

        echo "Waiting for app service to restart..."
        sleep 15

    - name: 🔐 Verify Key Vault Integration
      run: |
        echo "Verifying Key Vault secrets integration..."

        # Get Key Vault name
        KEY_VAULT_NAME=$(az keyvault list \
          --resource-group ${{ needs.get-infrastructure-info.outputs.resource_group_name }} \
          --query "[0].name" \
          --output tsv)

        echo "Key Vault: $KEY_VAULT_NAME"

        # Check if required secrets exist
        echo "Checking required OpenAI secrets..."
        REQUIRED_SECRETS=("openai-api-key" "openai-token" "assistant-id-juridico" "assistant-id-calidad")

        for secret in "${REQUIRED_SECRETS[@]}"; do
          if az keyvault secret show --vault-name "$KEY_VAULT_NAME" --name "$secret" --query "name" -o tsv &>/dev/null; then
            echo "✅ Secret exists: $secret"
          else
            echo "❌ Missing secret: $secret"
            echo "⚠️  Please run: ./scripts/manage-secrets.sh update"
          fi
        done

        # Verify app service can access Key Vault
        echo "Verifying app service Key Vault access..."
        az webapp config appsettings list \
          --name ${{ needs.get-infrastructure-info.outputs.backend_app_name }} \
          --resource-group ${{ needs.get-infrastructure-info.outputs.resource_group_name }} \
          --query "[?contains(name, 'GPT') || contains(name, 'OPENAI') || contains(name, 'ASSISTANT')].{Name:name, Value:value}" \
          --output table
    
    - name: ⏳ Wait for Backend Deployment
      run: |
        echo "Waiting for backend deployment to complete..."
        sleep 30

        # Check if the app is responding
        BACKEND_URL="https://${{ needs.get-infrastructure-info.outputs.backend_app_name }}.azurewebsites.net"
        echo "Testing backend health at: $BACKEND_URL/status"

        for i in {1..10}; do
          echo "Health check attempt $i of 10..."

          # Try to get detailed response
          if response=$(curl -s -w "HTTP_CODE:%{http_code}" "$BACKEND_URL/status" 2>&1); then
            http_code=$(echo "$response" | grep -o "HTTP_CODE:[0-9]*" | cut -d: -f2)
            response_body=$(echo "$response" | sed 's/HTTP_CODE:[0-9]*$//')

            echo "HTTP Status: $http_code"
            echo "Response: $response_body"

            if [ "$http_code" = "200" ]; then
              echo "✅ Backend is responding successfully!"
              break
            fi
          else
            echo "❌ Failed to connect to backend"
          fi

          if [ $i -eq 10 ]; then
            echo "🚨 Backend health check failed after 10 attempts"
            echo "Trying alternative endpoints for debugging..."
            curl -v "$BACKEND_URL" || true
            curl -v "$BACKEND_URL/health" || true

            echo "Checking app service logs for errors..."
            az webapp log tail \
              --name ${{ needs.get-infrastructure-info.outputs.backend_app_name }} \
              --resource-group ${{ needs.get-infrastructure-info.outputs.resource_group_name }} \
              --provider application \
              --max-events 50 || true

            echo "Checking Key Vault access from app service..."
            az webapp config appsettings list \
              --name ${{ needs.get-infrastructure-info.outputs.backend_app_name }} \
              --resource-group ${{ needs.get-infrastructure-info.outputs.resource_group_name }} \
              --query "[?contains(name, 'OPENAI') || contains(name, 'GPT')].{Name:name, Source:slotSetting}" \
              --output table || true

            exit 1
          fi

          echo "Waiting 30 seconds before next attempt..."
          sleep 30
        done

  deploy-frontend:
    name: 🚀 Deploy Frontend
    runs-on: ubuntu-latest
    needs: [determine-environment, get-infrastructure-info, build-and-test]
    if: needs.determine-environment.outputs.should_deploy == 'true' && needs.determine-environment.outputs.deploy_frontend == 'true'
    environment: ${{ needs.determine-environment.outputs.environment }}
    
    steps:
    - name: 📥 Checkout Code
      uses: actions/checkout@v4
    
    - name: 🔐 Azure Login
      uses: azure/login@v2
      with:
        creds: ${{ secrets.AZURE_CREDENTIALS }}
        enable-AzPSSession: true
        environment: azurecloud
        allow-no-subscriptions: false
        audience: api://AzureADTokenExchange
        auth-type: SERVICE_PRINCIPAL

    - name: 🔑 Login to Azure Container Registry
      run: az acr login --name ${{ needs.get-infrastructure-info.outputs.acr_name }}
    
    - name: 🏗️ Build Frontend Image
      working-directory: apps/filtro-curricular/filtro-curricular-fe-web
      run: |
        IMAGE_TAG="${{ github.sha }}"
        ACR_SERVER="${{ needs.get-infrastructure-info.outputs.acr_login_server }}"
        
        docker build \
          --tag "$ACR_SERVER/filtro-curricular-fe-web:$IMAGE_TAG" \
          --tag "$ACR_SERVER/filtro-curricular-fe-web:latest" \
          --tag "$ACR_SERVER/filtro-curricular-fe-web:${{ needs.determine-environment.outputs.environment }}-latest" \
          .
    
    - name: 📤 Push Frontend Image
      run: |
        ACR_SERVER="${{ needs.get-infrastructure-info.outputs.acr_login_server }}"
        IMAGE_TAG="${{ github.sha }}"
        
        docker push "$ACR_SERVER/filtro-curricular-fe-web:$IMAGE_TAG"
        docker push "$ACR_SERVER/filtro-curricular-fe-web:latest"
        docker push "$ACR_SERVER/filtro-curricular-fe-web:${{ needs.determine-environment.outputs.environment }}-latest"
    
    - name: 🔄 Restart Frontend App Service
      run: |
        az webapp restart \
          --name ${{ needs.get-infrastructure-info.outputs.frontend_app_name }} \
          --resource-group ${{ needs.get-infrastructure-info.outputs.resource_group_name }}
    
    - name: ⏳ Wait for Frontend Deployment
      run: |
        echo "Waiting for frontend deployment to complete..."
        sleep 30
        
        # Check if the app is responding
        FRONTEND_URL="https://${{ needs.get-infrastructure-info.outputs.frontend_app_name }}.azurewebsites.net"
        for i in {1..10}; do
          if curl -f "$FRONTEND_URL" > /dev/null 2>&1; then
            echo "Frontend is responding"
            break
          fi
          echo "Attempt $i: Frontend not ready, waiting..."
          sleep 30
        done

  notify-success:
    name: 📧 Notify Success
    runs-on: ubuntu-latest
    needs: [determine-environment, get-infrastructure-info, deploy-backend, deploy-frontend]
    if: always() && (needs.deploy-backend.result == 'success' || needs.deploy-frontend.result == 'success')
    
    steps:
    - name: Placeholder Step
      run: echo "Notifications are currently disabled. Configure SMTP and Teams webhook secrets to enable notifications."

    # TODO: Re-enable email notifications once SMTP secrets are configured
    # - name: Send Email Notification
    #   if: vars.NOTIFICATION_EMAIL
    #   uses: dawidd6/action-send-mail@v3
    #   with:
    #     server_address: ${{ vars.SMTP_SERVER || 'smtp.office365.com' }}
    #     server_port: ${{ vars.SMTP_PORT || '587' }}
    #     username: ${{ secrets.SMTP_USERNAME }}
    #     password: ${{ secrets.SMTP_PASSWORD }}
    #     subject: "✅ Filtro Curricular Applications Deployed - ${{ needs.determine-environment.outputs.environment }}"
    #     to: ${{ vars.NOTIFICATION_EMAIL }}
    #     from: ${{ secrets.SMTP_USERNAME }}
    #     body: |
    #       Application deployment completed successfully!
    #
    #       Environment: ${{ needs.determine-environment.outputs.environment }}
    #       Backend Deployed: ${{ needs.determine-environment.outputs.deploy_backend }}
    #       Frontend Deployed: ${{ needs.determine-environment.outputs.deploy_frontend }}
    #
    #       Backend URL: https://${{ needs.get-infrastructure-info.outputs.backend_app_name }}.azurewebsites.net
    #       Frontend URL: https://${{ needs.get-infrastructure-info.outputs.frontend_app_name }}.azurewebsites.net
    #
    #       Commit: ${{ github.sha }}
    #       Branch: ${{ github.ref_name }}
    #       Actor: ${{ github.actor }}

    # TODO: Re-enable Teams notifications once webhook URL is configured
    # - name: 📢 Send Teams Notification
    #   if: secrets.TEAMS_WEBHOOK_URL
    #   uses: skitionek/notify-microsoft-teams@master
    #   with:
    #     webhook_url: ${{ secrets.TEAMS_WEBHOOK_URL }}
    #     title: "✅ Application Deployment Success"
    #     message: |
    #       **Filtro Curricular Applications Deployed**
    #
    #       **Environment:** ${{ needs.determine-environment.outputs.environment }}
    #       **Backend Deployed:** ${{ needs.determine-environment.outputs.deploy_backend }}
    #       **Frontend Deployed:** ${{ needs.determine-environment.outputs.deploy_frontend }}
    #
    #       **Backend URL:** https://${{ needs.get-infrastructure-info.outputs.backend_app_name }}.azurewebsites.net
    #       **Frontend URL:** https://${{ needs.get-infrastructure-info.outputs.frontend_app_name }}.azurewebsites.net
    #
    #       **Commit:** ${{ github.sha }}
    #       **Branch:** ${{ github.ref_name }}
    #       **Actor:** ${{ github.actor }}

  notify-failure:
    name: 🚨 Notify Failure
    runs-on: ubuntu-latest
    needs: [determine-environment, get-infrastructure-info, deploy-backend, deploy-frontend]
    if: failure()
    
    steps:
    - name: 📝 Placeholder Step
      run: echo "Notifications are currently disabled. Configure SMTP and Teams webhook secrets to enable notifications."

    # TODO: Re-enable email notifications once SMTP secrets are configured
    # - name: 📧 Send Email Notification
    #   if: vars.NOTIFICATION_EMAIL
    #   uses: dawidd6/action-send-mail@v3
    #   with:
    #     server_address: ${{ vars.SMTP_SERVER || 'smtp.office365.com' }}
    #     server_port: ${{ vars.SMTP_PORT || '587' }}
    #     username: ${{ secrets.SMTP_USERNAME }}
    #     password: ${{ secrets.SMTP_PASSWORD }}
    #     subject: "❌ Filtro Curricular Application Deployment Failed - ${{ needs.determine-environment.outputs.environment }}"
    #     to: ${{ vars.NOTIFICATION_EMAIL }}
    #     from: ${{ secrets.SMTP_USERNAME }}
    #     body: |
    #       Application deployment failed!
    #
    #       Environment: ${{ needs.determine-environment.outputs.environment }}
    #
    #       Please check the GitHub Actions logs for details.
    #
    #       Commit: ${{ github.sha }}
    #       Branch: ${{ github.ref_name }}
    #       Actor: ${{ github.actor }}

    # TODO: Re-enable Teams notifications once webhook URL is configured
    # - name: 📢 Send Teams Notification
    #   if: secrets.TEAMS_WEBHOOK_URL
    #   uses: skitionek/notify-microsoft-teams@master
    #   with:
    #     webhook_url: ${{ secrets.TEAMS_WEBHOOK_URL }}
    #     title: "❌ Application Deployment Failed"
    #     message: |
    #       **Filtro Curricular Application Deployment Failed**
    #
    #       **Environment:** ${{ needs.determine-environment.outputs.environment }}
    #
    #       Please check the GitHub Actions logs for details.
    #
    #       **Commit:** ${{ github.sha }}
    #       **Branch:** ${{ github.ref_name }}
    #       **Actor:** ${{ github.actor }}
