name: CI/CD Deploy Static Pages

on:
  push:
    branches: [main]
    paths:
      - 'apps/static-pages/**'

jobs:
  sync_and_invalidate:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout Code
        uses: actions/checkout@v3

      - name: Set up AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: sa-east-1

      - name: Sync Cognisearch Static Pages to S3
        working-directory: apps/static-pages/staticpage-cognisearch-aws/site
        run: |
          aws s3 sync . s3://www.cognisearch.cl --delete --region sa-east-1
          echo "Cognisearch static pages synced to s3://www.cognisearch.cl"

      - name: Invalidate CloudFront Cache - Cognisearch
        run: |
          aws cloudfront create-invalidation \
            --distribution-id ${{ secrets.COGNISEARCH_CLOUDFRONT_DISTRIBUTION_ID }} \
            --paths '/*'
          echo "CloudFront cache invalidated for www.cognisearch.cl"

      - name: Sync Ragtech Static Pages to S3
        working-directory: apps/static-pages/staticpage-ragtech-aws/site
        run: |
          aws s3 sync . s3://www.ragtech.cl --delete --region sa-east-1
          echo "Ragtech static pages synced to s3://www.ragtech.cl"

      - name: Invalidate CloudFront Cache - Ragtech
        run: |
          aws cloudfront create-invalidation \
            --distribution-id ${{ secrets.RAGTECH_CLOUDFRONT_DISTRIBUTION_ID }} \
            --paths '/*'
          echo "CloudFront cache invalidated for www.ragtech.cl"