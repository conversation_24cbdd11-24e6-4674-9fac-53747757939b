name: CI/CD Deployment Cognisearch FrontEnd & Backend Artifacts
run-name: ${{ github.actor }} CI/CD Deployment for Cognisearch Artifacts

on:
  push:
    branches: [main]
    paths:
      - apps/cognisearch-cloud/cgnsrch-chatbot-fe-ionic/**
      - apps/cognisearch-cloud/cgnsrch-chatbot-be-pfapi/**
  pull_request:
    branches: [main]
    paths:
      - apps/cognisearch-cloud/cgnsrch-chatbot-fe-ionic/**
      - apps/cognisearch-cloud/cgnsrch-chatbot-be-pfapi/**

jobs:
  build_and_deploy_backend:
    runs-on: ubuntu-latest
    needs: build_and_deploy_frontend # Puede ejecutarse después del frontend si es necesario

    steps:
    # Step 1: Checkout the code
    - name: Checkout Code
      uses: actions/checkout@v3

    # Step 2: Set up AWS credentials
    - name: Set up AWS credentials
      uses: aws-actions/configure-aws-credentials@v1
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: sa-east-1

    # Step 3: Log in to Amazon ECR
    - name: Log in to Amazon ECR
      run: |
        aws ecr get-login-password --region sa-east-1 | docker login --username AWS --password-stdin ${{ secrets.AWS_ECR_URL }}

    # Step 4: Build and push Backend Docker image
    - name: Build and Push Backend Image to ECR
      working-directory: apps/cognisearch-cloud/cgnsrch-chatbot-be-pfapi
      run: |
        docker build \
        -t ${{ secrets.AWS_ECR_URL }}/cgnsrch-chatbot-be-pfapi:latest .
        docker push ${{ secrets.AWS_ECR_URL }}/cgnsrch-chatbot-be-pfapi:latest

    # Step 5: Deploy Backend service in ECS
    - name: Update Backend ECS Service
      run: |
        aws ecs update-service \
          --cluster cognisearch-cluster \
          --service backend-service \
          --force-new-deployment \
          --region sa-east-1
        echo "Backend service deployed."

  build_and_deploy_frontend:
    runs-on: ubuntu-latest

    steps:
    # Step 1: Checkout the code
    - name: Checkout Code
      uses: actions/checkout@v3

    # Step 2: Set up AWS credentials
    - name: Set up AWS credentials
      uses: aws-actions/configure-aws-credentials@v1
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: sa-east-1

    # Step 3: Log in to Amazon ECR
    - name: Log in to Amazon ECR
      run: |
        aws ecr get-login-password --region sa-east-1 | docker login --username AWS --password-stdin ${{ secrets.AWS_ECR_URL }}

    # Step 4: Build and push Frontend Docker image
    - name: Build and Push Frontend Image to ECR
      working-directory: apps/cognisearch-cloud/cgnsrch-chatbot-fe-ionic
      run: |
        docker build -t ${{ secrets.AWS_ECR_URL }}/cgnsrch-chatbot-fe-ionic:latest .
        docker push ${{ secrets.AWS_ECR_URL }}/cgnsrch-chatbot-fe-ionic:latest

    # Step 5: Deploy Frontend service in ECS
    - name: Update Frontend ECS Service
      run: |
        aws ecs update-service \
          --cluster cognisearch-cluster \
          --service frontend-service \
          --force-new-deployment \
          --region sa-east-1
        echo "Frontend service deployed."
