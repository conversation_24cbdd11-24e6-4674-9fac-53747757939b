# Plan de trabajo para creación de CogniSearch

> *Se le llamará así por el momento para tener una referencia

## **Introducción**

Este documento es un marco general que será actualizado constantemente
basado en la información que se reúna con información de usuarios
finales y con la investigación de los fundadores, sirve como punto de
apoyo para el trabajo de equipo.

### **Fundadores:**

    - <PERSON> (Relaciones públicas, RRHH, Requerimientos Legales)
    - <PERSON> (DEV AI, Arquitectura, Diseño)
    - <PERSON> (DEV AI, Arquitectura, Diseño)
    - <PERSON> (Relaciones públicas, RRHH)
    - <PERSON> (QA, DEV, Relaciones públicas)

### **Asesor legal (Requerimientos legales):**

> Xxxxxxxxxxx

## Contenido

[**Plan de trabajo para creación de CogniSearch**](#plan-de-trabajo-para-creación-de-cognisearch)

[**Introducción**](#introducción)

[**Fundadores**](#fundadores)

[**Asesor legal (Requerimientos legales):**](#asesor-legal-requerimientos-legales)

[**Fase de Inicio (2-3 Meses)**](#fase-de-inicio-2-3-meses)

[**Objetivos:**](#objetivos)

[**Tareas Clave:**](#tareas-clave)

[**Creación del documento de visión del proyecto.**](#creación-del-documento-de-visión-del-proyecto)

[**Investigación de mercado:**](#investigación-de-mercado)

[**Consulta con expertos legales en tecnología y contratos gubernamentales.**](#consulta-con-expertos-legales-en-tecnología-y-contratos-gubernamentales)

[**Qué hace el software? (Para RRHH)**](#¿qué-hace-el-software?-para-rrhh)

[**Estrategia de Ventas, Marketing**](#estrategia-de-ventas-marketing)

[**Objetivos:**](#objetivos-1)

[**Tareas Clave:**](#tareas-clave-1)

[**Objetivos:**](#objetivos-2)

[**Tareas:**](#tareas)

[**Responsables:**](#responsable)

[**Capacitación (3-4 Meses)**](#capacitación-3-4-meses)

[**Objetivos:**](#objetivos-3)

[**Tareas Clave:**](#tareas-clave-2)

[**Responsables:**](#responsables)

[**Responsables:**](#responsables-1)

[**Estrategias de Expansión y Marketing (Paralell y Ongoing)**](#estrategias-de-expansión-y-marketing-paralell-y-ongoing)

[**Objetivos:**](#objetivos-4)

[**Tareas Clave:**](#tareas-clave-3)

[**Responsables:**](#responsables-2)

[**Revisiones y Ajustes**](#revisiones-y-ajustes)

## **Gestión de Proyecto**

### **Metodología Ágil:**

> Implementar metodologías ágiles para una mejor adaptabilidad y
> respuesta rápida a cambios.

### **Seguimiento y Reporte:**

> Establecer métricas de seguimiento para evaluar el progreso del
> proyecto y reportar a los stakeholders.

### **Responsable:**

> Rodrigo Vargas – Entregable: **Gestion_Proyecto_CogniSearch.docx** :soon:

## **Fase de Inicio (2-3 Meses)**

### **Objetivos:**

Establecer la visión del proyecto. (Rodrigo Vargas) :ok:

Realizar un análisis de mercado y de competidores. (Rodrigo Vargas):ok:

Definir los requerimientos legales y de cumplimiento. (Ricardo Román):soon:

Establecer procedimiento de Estrategia de Ventas Propuesta de Valor.(Rodrigo Vargas):ok:

Establecer los casos de Usuario y 'dolor' del Cliente (Rodrigo Vargas,Ricardo Román):soon:

Fase 1: Preparación y Análisis de Datos (1-2 Meses)

Fase 2: Desarrollo de la Solución LLM-GPT (3-6 Meses)

Fase 3: Pruebas Piloto (2-3 Meses)

## **Tareas Clave:**

### **Creación del documento de visión del proyecto.**

Visión:

> "Ser líderes innovadores en la transformación digital del sector
> público y privado, ofreciendo soluciones avanzadas de inteligencia
> artificial que empoderen a los ministerios, municipalidades y privados
> de cualquier sector con acceso a información precisa y análisis en
> tiempo real de sus documentos. Nuestra visión es revolucionar la
> manera en que el gobierno y los privados interactúan con los datos,
> transformándolos en conocimiento accesible y accionable, para mejorar
> la toma de decisiones y la eficiencia en la prestación de servicios a
> los ciudadanos. Nos esforzamos por desarrollar tecnologías que no solo
> respondan a las preguntas actuales, sino que también anticipen las
> necesidades futuras, garantizando la sostenibilidad, la seguridad y la
> integridad de los datos en todas nuestras operaciones. Comprometidos
> con la innovación continua y la excelencia, aspiramos a establecer
> nuevos estándares en el uso ético y responsable de la IA en la
> administración pública y privada, contribuyendo así a un país más
> conectado, informado y transparente."

### **Investigación de mercado**

> Después de una investigación de un mes en el mercado chileno respecto
> a “Soluciones de inteligencia artificial iguales o similares al modelo
> de trabajo que proponemos con manejo de data con terminología técnica
> LLM, GPT, PaLM, LaMDA, Lama etc.”, no se identificaron competidores
> importantes ni publicados, solo aplicaciones de pago que tienen acceso
> a internet con la empresa Openai.com (chatGPT nested ´GPTs´), que son
> irrelevantes relevantes para el proyecto. No existen competidores
> respecto a modelos ya creados, y no están entrenados en la data
> privada o pública, no se encontraron páginas web o publicidad web con
> esta idea o similar a ella. Conclusión: Hay luz verde para el trabajo
> a realizar.

### **Consulta con expertos legales en tecnología y contratos gubernamentales**

> Cumplimiento Normativo y Ético:
>
> Cumplimiento Legal: Asegurarse de que el software y su uso cumplan con
> todas las leyes chilenas, incluyendo protección de datos y privacidad.
>
> Ética en IA: Asegurarse de que el uso del software GPT sea ético,
> transparente y responsable.

## **¿Qué hace el software? (Para RRHH)**

- Es una solución avanzada de inteligencia artificial (IA) diseñada para
  el sector público y privado. Este software utiliza tecnología LLM y
  otros modelos disponibles, que son un tipo de modelo de lenguaje
  basado en IA, adaptada para manejar y responder en tiempos muy cortos
  a consultas complejas directas hechas por el Usuario en un entorno de
  Base de Conocimiento privado o abierto, puede manejar documentos como
  PDF, Doc y otros formatos. Su diseño enfocado en la seguridad de los
  datos y la personalización lo hace particularmente adecuado para
  aplicaciones sensibles y específicas dentro del sector público y
  privado chileno.

Funciones principales:

**Aplicación Local (On-Premise):** A diferencia de muchas soluciones
basadas en la nube, este software se implementa localmente en las
instalaciones del cliente. Esto proporciona mayor seguridad de los
datos, control sobre la infraestructura y un rendimiento optimizado
debido a la menor latencia en el acceso a los datos.

**Ingestión y Análisis de Datos:** El software está diseñado para
recopilar y procesar grandes volúmenes de texto. Utiliza una técnica de
ingestión de datos para cargar y dividir documentos en secciones
manejables, lo que es crucial debido a las limitaciones de los modelos
de lenguaje en cuanto al tamaño del texto que pueden procesar.

**Creación de Embeddings Numéricos:** Crea representaciones numéricas
(embeddings) de cada fragmento de texto. Esto es esencial para
identificar los fragmentos de texto más relevantes para una consulta
específica, buscando similitudes en el espacio de embeddings.

**Almacenamiento Eficiente:** Los embeddings se almacenan en bases de
datos vectoriales, permitiendo una búsqueda rápida y eficiente de datos
relevantes.

**Consultas y Generación de Respuestas:** El software genera un
embedding para cada consulta y busca en los documentos los fragmentos
más relevantes. Luego, inserta la pregunta y las secciones seleccionadas
en un mensaje dirigido a un modelo de lenguaje de gran escala (LLM),
como GPT, para generar una respuesta adecuada y relevante.

## **Estrategia de Ventas, Marketing**

**Propuesta de Valor:**

- Seguridad y Control de Datos

Propuesta: Al mantener los datos y el hardware en las instalaciones del
cliente, se ofrece un mayor control y seguridad. Esto es crucial para
entidades gubernamentales que manejan información sensible y
confidencial.

Beneficio: Minimiza riesgos de brechas de datos y garantiza el
cumplimiento de las normativas locales e internacionales de privacidad y
protección de datos.

- Personalización y Adaptabilidad

Propuesta: La solución se puede personalizar profundamente para
adaptarse a las necesidades específicas y procesos internos del Estado
de Chile o las empresas privadas.

Beneficio: Permite un ajuste más fino del software, optimizando su
rendimiento y eficacia en el contexto específico de los ministerios y
municipalidades chilenas.

- Independencia y Autonomía Tecnológica

Propuesta: Evita la dependencia de proveedores externos para el
almacenamiento y procesamiento de datos, garantizando autonomía
tecnológica.

Beneficio: Reduce la vulnerabilidad a problemas externos como fallos de
Internet, problemas de proveedores de servicios en la nube, y otras
interrupciones que están fuera del control del cliente.

- Rendimiento y Acceso Inmediato

Propuesta: Al estar alojado localmente, el software puede ofrecer un
rendimiento más rápido y un acceso inmediato a los datos sin latencias
de red.

Beneficio: Mejora la eficiencia operativa y la velocidad de respuesta,
lo que es crucial para decisiones rápidas y eficaces en el ámbito
gubernamental.

- Soporte y Mantenimiento Personalizado

Propuesta: Ofrecer un soporte y mantenimiento dedicado y personalizado,
adaptado a la infraestructura y necesidades del cliente.

Beneficio: Garantiza una respuesta rápida y eficiente a cualquier
problema técnico, optimizando el tiempo de actividad y la fiabilidad del
sistema.

- Formación y Capacitación Continua

Propuesta: Proporcionar formación y capacitación continua a los usuarios
del software, adaptándose a sus niveles de habilidad y necesidades.

Beneficio: Asegura que el personal pueda utilizar el software de manera
efectiva, maximizando su potencial y contribuyendo a un mejor servicio
público.

- Compromiso con la Innovación y Actualización Continua

Propuesta: Compromiso con la actualización y mejora continua del
software, incorporando las últimas innovaciones en IA y tecnología.

Beneficio: Asegura que el software se mantenga relevante y efectivo a
largo plazo, adaptándose a las cambiantes necesidades tecnológicas y de
información.

## **Diseño de Casos de Usuario (1-2 Meses)**

### **Objetivos:**

> Desarrollar un diseño primario de casos/historias de usuario para
> entregarlos a Desarrollo con el “dolor del cliente” así poder gatillar
> un código dirigido. (Rodrigo Vargas) :soon:

### **Tareas Clave:**

> Creación de casos de Usuario dirigidos, Documento:
> **Casos_de_Uso_Reales_v1.xlsx**

## **DEV Fase 1: Preparación y Análisis de Datos (1-2 Meses)**

### **Objetivos:**

> Establecer una base sólida de datos para el desarrollo de la solución
> LLM-GPT.

### **Tareas:**

> Recolección y categorización de fuentes de datos relevantes.
>
> Ingestión y pre-procesamiento de datos.
>
> Creación de embeddings numéricos para fragmentos de texto.
>
> Almacenamiento de embeddings en bases de datos vectoriales.

### **Responsables:** Desarrolladores y especialistas en datos

## **DEV Fase 2: Desarrollo de la Solución GPT (3-6 Meses)**

### **Objetivos:**

> Desarrollar una solución GPT personalizada que se adapte a las
> necesidades específicas del Cliente.

### **Tareas:**

> Configuración y entrenamiento de modelos de lenguaje.
>
> Desarrollo de algoritmos para la consulta y clasificación de
> documentos.
>
> Pruebas iniciales y ajustes del modelo.

### **Responsables:**

> Desarrolladores y equipo de QA.

## **DEV Fase 3: Implementación y Pruebas Piloto (2-3 Meses)**

### **Objetivos:**

> Implementar la solución en entornos de prueba y recoger feedback.

### **Tareas:**

> Despliegue de la solución en entornos controlados.
>
> Capacitación de usuarios en ministerios y municipalidades
> seleccionadas.
>
> Recolección y análisis de feedback para mejoras.

### **Responsables:**

> Desarrolladores, equipo de QA y especialistas en capacitación.

## **DEV Fase 4: Lanzamiento y Escalabilidad (1-2 Meses)**

### **Objetivos:**

> Lanzar la solución a nivel nacional sectorial y asegurar su
> escalabilidad.

### **Tareas:**

> Implementación completa en todos los ministerios y municipalidades.
>
> Monitoreo del rendimiento y resolución de problemas.
>
> Planificación para la escalabilidad y actualizaciones futuras.

### **Responsables:**

> Desarrolladores, equipo de soporte técnico y administración.

## **DEV Fase 5: Mantenimiento y Mejora Continua (Ongoing)**

### **Objetivos:**

> Asegurar el funcionamiento óptimo de la solución y su adaptación a
> necesidades futuras.

### **Tareas:**

> Soporte técnico y mantenimiento regular.
>
> Actualizaciones basadas en nuevos requerimientos o tecnologías.
>
> Evaluaciones periódicas del rendimiento y satisfacción del usuario.

### **Responsables:**

> Equipo de soporte técnico, desarrolladores y administración.

## **Capacitación (3-4 Meses)**

### **Objetivos:**

> Capacitar a los usuarios finales.

### **Tareas Clave:**

> Sesiones de capacitación y documentación.

### **Responsables:**

> Equipo de capacitación (Capacitación y soporte).
>
> Encuestas de satisfacción y análisis de feedback.
>
> RR.HH. y Socios (Gestión de relaciones con clientes).

## **Estrategias de Expansión y Marketing (Paralell y Ongoing)**

### **Objetivos:**

> Promover el software en otros ministerios y municipalidades.
>
> Planificar la expansión a largo plazo.

### **Tareas Clave:**

> Campañas de marketing y presentaciones.
>
> Análisis de oportunidades de expansión.

### **Responsables:**

> Socios y equipo de marketing (si se contrata).

### **Revisiones y Ajustes**

> Reuniones Semanales de Equipo: Para seguimiento del progreso y
> resolución de problemas.
>
> Revisiones Mensuales con Stakeholders: Para presentar avances y
> recibir feedback.
