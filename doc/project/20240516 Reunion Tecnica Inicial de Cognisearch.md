# Reunion Tecnica Inicial de Cognisearch

Fecha : 2024-05-16

Asistentes:
    <PERSON>:

- FrontEnd
- Alcances Desarrollo y Bases económicas
- Planeacion Proyecto y relaciondos

## Acuerdos

## FrontEnd

- Ofrecer CogniSearch como SaaS , por ende necesitamos dividir responsabilidades de componentes, y es el momento de cambiar el Stack del Frontend.
    -> Conclusion, migrar el frontend de Streamlit a IONIC Framework.
        -> Beneficios, baja curva de aprendizaje
            -> bajo costo de especialistas.

- Recomendacion : IONIC - Version Angular y TypeScript.
      - <https://ionicframework.com/>

### Priorizacion de Epicas

1. Epica de migracion a nube + microservicios
    1. Servicio FrontEnd
        - Login
        - Chatbot
        - Acceso a los features (Chat casual, Chat Trabajo, Resumen, ETC)
        - Captura de metricas de uso
    2. Backend
        - Logica De Flujo de conversacion de chatbot LLM
        - Resumen
        - Acceso a API De modelos DeepInfra o similares.
    3. VectorStore
        - ChromaDB
        - PineCone
2. Recolección de Metadata
    1. Lanzar a la nube con usuarios Avanzados.
    2. Guardar en sabana tabular.
        1. Datos de Cliente usuario
        2. Datos servidos
        3. Datos del modelo
        4. Prompt y Respuesta
        5. Feedback del usuario.
3. Hacer Analitica avanzada con la metadata
4. Hacer Entrenamiento sobre modelo base para

## Base económica para asegurar infrastructura de la solución

- Definir Roadmap de features con el fin de calcular costos y estimacion de recursos del proyecto post lanzamiento.

- Necesidades Iniciales:
    - UX
    - Developer
    - Founder Dedicado al proyecto.
    - Data Scientist de origen academico con base Magister o PHD.

- Participación en Concursos:
    - Públicos.
    - Privados.
    - Nacionales.
    - Internacionales.
    - Buscar consursos disponibles:
        - Ojala con recursos sin compromisos de participación.
        - @Alvaro ira a buscar las convocatorias mas convenientes.

### Plan de Trabajo y Gestión de Proyectos

- Documentos
    - Introducción a Cognisearch
    - Plan de Trabajo (mejorable)
    - Gestión de Proyectos (mejorable)
    - Marcos Teórico Cognisearch
    - Marco Teórico Matemático.

- Otros Documentos Necesarios.
    - Documentos Comerciales a los clientes.
    - Plan de Implementación Base (agnostico al cliente)
    - Alcances legales de las respuestas y ética de IA.
        - Consideraciones por normas de IA de Chile
        - Consideracione a la ley de transparencia.
        - Consideraciones a la leyes de datos personales u otras caracteristicas protegidas por ley.
    - Material de capacitacion para el usuario

- Otras consideraciones
    - Features Opcionales? o convertibles a features standard.
        - Rescate de Fuentes originales
        - Plugin al chat intranet.
        - Modo acceso de base de datos (Consultas a base de datos Estructuradas de la empresa con lenguaje natural)
        - Conexcion a sistemas transversales
            - ERP
            - Customer Service.
        - Salida en formatos específicos
            - DOCX
            - EXCEL
            - PPT
            - Carga Opcional de documentos con URL u documentos que son ocultos solo para tener titulo o indice.
                - Documentos solo con indice invertido al titulo de documento y pagina
                - Documentos sin ocultamiento que se pueden mostrar con su contenido original ( No quedarse con la copia original )
                - Esta responsabilidad hay que traspasarla al usuario ya que nosotros entregamos la herramienta pero la responsabilidad del guardado del original depende de éste.

- Cobros en el proyecto
    - Etapa de Setup (Puesta en marcha)
        - Digitalizacion de documentos si es por escaneo
        - Ingesta de documentos electronicos Inicial Segun la calidad del origen (PDF Digital o PDF de Escaneo)
            - Configuración de los sistemas.
            - Infraestructura si aplica.
            - Guardado de documentos especiales (referencias al origen)
        - Etapa de Fee
            - Considerar
                - Número de usuarios.
                - Número de consultas.
            - Metricas y estadisticas de uso.
                - Tendencias
                - Nube de palabras
        - Otros
            - Desarrollos adicionales especificos del clientes.
