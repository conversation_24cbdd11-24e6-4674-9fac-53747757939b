

Ideas para el futuro:
    Features:
        Ingesta:
            ☐ Ver la forma de en el proceso de ingesta aparte del chanquin agregar información del documento para indexarlo y que permita a la respuesta final del lllm dar la opción de dar un índice de documentos usados que se puedan abrir en una ventana lateral del chatbot.
            ☐ https://www.sagacify.com/news/a-guide-to-chunking-strategies-for-retrieval-augmented-generation-rag
        Base Documental:
            ☐ Incluir un sistema que permita agregar documentos nuevos a la base de conocimiento del lllm 
            ☐ además incluir un flujo para eliminar documentos que estén vencidos o que hayan sido caducados de manera de eliminarlos del vector store 
        Plantilla de Prompt: 
            ☐ Validar formas de mejorar la plantilla de pront para hacer consultas 
        Historia y seguimiento de hilos de conversacion:
            ☐ Agregar seguimiento de conversacion en el contexto
            ☐ Guardado de hilos ya cerrados.
            ☐ Validar ConversationalRetrievalChain with ConversationBufferMemory en Lancgchain.
        Benchmarking y Performance de respuestas:
            ☐ Buscar plantillas de data sets para benchmark de preguntas y respuestas conocidas 
            ☐ Se podria usar Robot framework?
        Feedback y FineTunning:
            ☐ Buscar y definir el flujo para aplicar fine tuning de documentos o ocupando raft 
            ☐ Averiguar formas de hacer benchmarks de distintos modelos en una arquitectura RAG a fin de encontrar el mejor modelo del llm en términos de performance de respuesta y de tiempo 
        Integracion a Sistemas de terceros:
            ☐ Averiguar cómo incluir en streamlit sistema de conexión a single sign on 
    Documentacion Para Venta y proyectos:
        ☐ Para la fuerza de venta generar documentos de foda del modelo y solución cognicerch con respecto a otras soluciones basadas a ChatGPT U otros modelos genéricas para demostrar la ventaja de usar nuestro producto 
    Questions:
        ☐ what is the difference between customize a model with ollama o templating prompt with langchain?
    Legales:
        Ejemplo Claude AI:
            Claude may occasionally generate incorrect or misleading information, or produce offensive or biased content.
            Claude is not intended to give advice, including legal, financial, & medical advice. Don’t rely on our conversation alone without doing your own independent research.
            Anthropic may change usage limits, functionality, or policies as we learn more. You can upgrade your plan to get more access to Claude’s features.

    Empresa:
        ☐ hay que hacer un website para la empresa y el producto
    To Read:
        ☐ https://freedium.cfd/https://towardsdatascience.com/supervised-fine-tuning-sft-with-large-language-models-0c7d66a26788
        ☐ Fast.ai
            ☐ https://course.fast.ai/
            ☐ https://www.fast.ai/posts/2023-09-04-learning-jumps/
            ☐ https://freedium.cfd/https://towardsdatascience.com/language-models-gpt-and-gpt-2-8bdb9867c50a
        ☐ https://huggingface.co/docs/transformers/index
        ☐ https://freedium.cfd/https://towardsdatascience.com/fine-tune-a-mistral-7b-model-with-direct-preference-optimization-708042745aac
        ☐ https://www.linkedin.com/pulse/refining-ai-how-reward-model-shapes-base-llm-learning-patamsetti-9klpc/