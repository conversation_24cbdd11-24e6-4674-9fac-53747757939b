To Read:
    ☐ https://www.reddit.com/r/<PERSON><PERSON>hai<PERSON>/comments/1bwikyf/finetuning_for_retrievalqa_chain/
        ☐ https://www.reddit.com/r/<PERSON><PERSON>hain/s/BUCeJkrwNk
    ☐ https://cobusgreyling.medium.com/t-rag-rag-fine-tuning-entity-detection-9a5aaa01e437
        ☐ https://www.linkedin.com/in/cobusgreyling/
        ☐ https://www.linkedin.com/pulse/eight-prompt-engineering-implementations-updated-cobus-greyling?trk=public_profile_article_view
        ☐ https://www.linkedin.com/pulse/langsmith-cobus-greyling?trk=public_profile_article_view
        ☐ https://www.linkedin.com/pulse/chaining-large-language-model-prompts-cobus-greyling?trk=public_profile_article_view
    ☐ https://github.com/facebookresearch/faiss    
    ☐ https://medium.com/@seeusimong/retrieval-augmented-generation-with-pgvector-and-ollama-e342967a0ff7
    ☐ https://gitlab.com/rahasak-labs/ollama/-/blob/master/model.py?ref_type=heads
    ☐ https://github.com/ThomasJay/RAG/blob/main/app.
    RAG Solution:
        ☐ https://www.sagacify.com/news/a-guide-to-chunking-strategies-for-retrieval-augmented-generation-rag
        ☐ https://medium.com/rahasak/build-rag-application-using-a-llm-running-on-local-computer-with-ollama-and-langchain-e6513853fda0
        ☐ https://freedium.cfd/https://pub.towardsai.net/why-rag-applications-fail-in-production-a-technical-deep-dive-15cc976af52c
        ☐ https://freedium.cfd/https://pub.towardsai.net/how-to-optimize-chunk-sizes-for-rag-in-production-fae9019796b6
        ☐ https://freedium.cfd/https://bootcamp.uxdesign.cc/how-to-improve-rag-results-in-your-llm-apps-from-basics-to-advanced-************
        RAG Series:
            ☐ https://freedium.cfd/https://durgiachandan.medium.com/chunking-strategies-considerations-and-optimization-part-2-of-rag-series-a5b641f5500c
            ☐ https://freedium.cfd/https://durgiachandan.medium.com/vector-databases-and-vector-libraries-part-4-of-rag-series-e80961406e53
            ☐ https://freedium.cfd/https://durgiachandan.medium.com/improving-rag-performance-a-structured-approach-part-6-b-of-rag-series-98b807b1b0f0
    AI History:
        ☐ https://www.blog.aiport.tech/p/12-years-of-ai-review-part-1?ref=dailydoseofds.com
    Training:
        LoRa & Related:
            ☐ https://arxiv.org/abs/2308.03303?ref=dailydoseofds.com
            ☐ https://www.dailydoseofds.com/understanding-lora-derived-techniques-for-optimal-llm-fine-tuning/
            ☐ https://www.dailydoseofds.com/implementing-lora-from-scratch-for-fine-tuning-llms/
        Transfer Learning:
            ☐ https://blog.dailydoseofds.com/p/transfer-learning-fine-tuning-multitask
        Huggingfaces Autotrain:
            ☐ https://www.youtube.com/watch?v=OH_e0wOkpZc
            ☐ https://huggingface.co/autotrain
            ☐ https://www.youtube.com/watch?v=g-_lw944edU
        DPO :
            ☐ https://github.com/huggingface/trl/tree/main/examples/research_projects/stack_llama_2/scripts
    Langchain Microservice Solution:
        ☐ https://www.youtube.com/watch?v=I_4jEnDwGwI
        ☐ https://github.com/Coding-Crashkurse/Langchain-Production-Project/tree/main
        ☐ https://github.com/Coding-Crashkurse/Langchain-Production-Project
        StreamLit:
            ☐ https://dev.to/aws-builders/how-to-build-and-deploy-an-api-driven-streamlitpython-microservice-on-aws-3bkj
            ☐ https://towardsdatascience.com/the-fastest-way-to-deploy-your-ml-app-on-aws-with-zero-best-practices-df15c09eead7
            ☐ https://freedium.cfd/https://adzic-tanja.medium.com/deploying-a-streamlit-app-using-docker-aws-ecr-and-ec2-ad6c15a0b225
    LLM Based Solutions:
        DeepInfra Autogen:
            ☐ https://deepinfra.com/docs/advanced/autogen
    Templates :
        CodingMindset:
            ☐ https://github.com/codingmindset/youtube-tutorials?tab=readme-ov-file
    Github Copilot Alternatives:
        Codium:
            ☐ https://www.youtube.com/watch?v=2MChz7BwEDc
Clean Architecture:
    ☐ https://breadcrumbscollector.tech/python-the-clean-architecture-in-2021/
    ☐ https://breadcrumbscollector.tech/modular-monolith-in-python/

