Projects:
    ☐ Anything with a colon at the end of the line is a project
    ☐ Projects will show some statistics next to them @1h
      ✔ By default those statistics are the number of pending todos and the sum of their time estimates @30m
    Nested:
      ☐ You can nest projects inside each other and fold them
  
  Todos:
    You can write plain text notes/descriptions wherever you want
    New:
      ☐ Press Cmd/Ctrl+Enter to add a new todo
    Done:
      ✔ Press Alt+D to mark a todo as done
      ✔ Press it again to undo the action
    Cancelled:
      ✘ Press Alt+C to mark a todo as cancelled
      ✘ Press it again to undo the action
    Tagging:
      ☐ You can add tags using the @ symbol, like this @tag
      ☐ There are some special, customizable tags: @critical @high @low @today
    Timekeeping:
      ✔ Completed todos can show a timestamp @done(17-11-03 10:42)
      ☐ Press Alt+S to mark a todo as started @started(17-11-03 10:42)
        ✔ Now it will show the elapsed time @started(17-11-03 10:42) @done(17-11-03 20:11) @lasted(9h29m)
      ☐ You can provide time estimates for your todos @1h30m
        ☐ We are even doing some natural language processing @est(1 day and 20 minutes)
  
  Formatting:
    You can format text in a markdown-like fashion
    Bold:
      ☐ Use asterisks for *bold*
    Italic:
      ☐ Use underscores for _italic_
    Strikethrough:
      ☐ Use tildes for ~strikethrough~
    Code:
      ☐ Use backticks for `code`
  
  Archive:
    ✔ You can archive finished todos here
    ✔ Congratulations, you are now a Todo+ master!

Project: @today
  Project @today:
  Project @today :
  Xproject:
    Simple description, @tag, nested @critical
    Style *bold* _italic_ ~strikethrough~
    Not a project: but a description
    Not a project: @tag, but a description
    Contains some `code`, nested
    ---------------------------------
    –––––––––––––––––––––––––––––––––
    —————————————————————————————————
    ☐ *bold* _italic_ ~strikethrough~ `code`
    ☐ X*bold*X X_italic_X X~strikethrough~X (Smart)
    ☐ *_bold/italic_* *~bold/strikethrough~* _~italic/strikethrough~_
    ☐ *_~bold/italic/strikethrough~_*
    ☐ `code () *bold* @tag @critical`
    ☐ `@tag`half
    ☐ `foo ()` and `bar ()` and ``
    ✘ cancel
    ✘ another cancel @critical @tag aftertag
    ✘ cancelled `code`, nested
    ✘ cancel/done ✔
    ✔ done @high
    ✔ another done
    ✔ done `code`, nested
    ✔ done/cancel ✘
    ☐ @criticaluper
    ☐ @tag() @tag(foo) @tag(@critical) @tag(foo bar) @tag(`code`)
    ☐ baz @ @custom @tag(foo) @tag(@critical) @tag(foo bar)
    ☐ qux @critical @high @low @today @today
    ☐ @critical @mixed @high
    ☐ @critical(foo) @high(foo) @low(foo) @today(foo)
    ✔ *@today* _@today_ ~@today~ `@today`
    ☐ no <EMAIL> <EMAIL> <EMAIL>
    ☐ started @started(17-11-03 10:42)
    ✔ completed @started(17-11-03 10:42) @done(17-11-03 20:10) @lasted(9 hours 28 minutes 52 seconds)
    ✘ cancelled @started(17-11-03 10:42) @cancelled(17-11-03 20:10) @wasted(9 hours 28 minutes 53 seconds)
    ☐ estimate @1h @2h20m @est(10 days and 1 minute)
  
  Box:
    - Box
    ❍ Box
    ❑ Box
    ■ Box
    ⬜ Box
    □ Box
    ☐ Box
    ▪ Box
    ▫ Box
    – Box
    — Box
    ≡ Box
    → Box
    › Box
    [] Box
    [ ] Box
  
  Done:
    ✔ Done
    ✓ Done
    ☑ Done
    + Done
    [x] Done
    [X] Done
    [+] Done
  
  Cancel:
    ✘ Cancel
    x Cancel
    X Cancel
    [-] Cancel
  
  TaskPaper Compatibility:
    - Box
    - Done @done
    - Done @done(17-11-03 20:10)
    - Done @donenot
    - Cancelled @cancelled
    - Cancelled @cancelled(17-11-03 20:10)
    - Cancelled @cancellednot