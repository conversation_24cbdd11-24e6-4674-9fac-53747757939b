digraph "CogniSearch Architecture" {
	graph [bb="0,0,737,379.5",
		fontcolor="#2D3436",
		fontname="Sans-Serif",
		fontsize=15,
		label="CogniSearch Architecture",
		lheight=0.24,
		lp="368.5,12.5",
		lwidth=2.68,
		nodesep=0.60,
		pad=2.0,
		rankdir=LR,
		ranksep=0.75,
		splines=ortho
	];
	node [fixedsize=true,
		fontcolor="#2D3436",
		fontname="Sans-Serif",
		fontsize=13,
		height=1.4,
		imagescale=true,
		label="\N",
		labelloc=b,
		shape=box,
		style=rounded,
		width=1.4
	];
	edge [color="#7B8894"];
	subgraph "cluster_Web-App Layer" {
		graph [bb="147,196.5,264,371.5",
			bgcolor="#E5F5FD",
			fontname="Sans-Serif",
			fontsize=12,
			label="Web-App Layer",
			labeljust=l,
			lheight=0.18,
			lp="200.5,361",
			lwidth=1.26,
			pencolor="#AEB6BE",
			rankdir=LR,
			shape=box,
			style=rounded
		];
		"********************************"	[height=1.9028,
			image="/home/<USER>/.pyenv/versions/poc/lib/python3.12/site-packages/resources/onprem/client/client.png",
			label="Streamlit Chat",
			pos="205.5,273.5",
			shape=none,
			width=1.4028];
	}
	subgraph cluster_Backend {
		graph [bb="302,196.5,419,371.5",
			bgcolor="#E5F5FD",
			fontname="Sans-Serif",
			fontsize=12,
			label=Backend,
			labeljust=l,
			lheight=0.18,
			lp="336,361",
			lwidth=0.72,
			pencolor="#AEB6BE",
			rankdir=LR,
			shape=box,
			style=rounded
		];
		"********************************"	[height=1.9028,
			image="/home/<USER>/.pyenv/versions/poc/lib/python3.12/site-packages/resources/azure/ml/cognitive-services.png",
			label="Large Language Model API",
			pos="360.5,273.5",
			shape=none,
			width=1.4028];
	}
	subgraph cluster_VectorStore {
		graph [bb="457,196.5,574,371.5",
			bgcolor="#E5F5FD",
			fontname="Sans-Serif",
			fontsize=12,
			label=VectorStore,
			labeljust=l,
			lheight=0.18,
			lp="500.5,361",
			lwidth=0.99,
			pencolor="#AEB6BE",
			rankdir=LR,
			shape=box,
			style=rounded
		];
		"********************************"	[height=1.9028,
			image="/home/<USER>/.pyenv/versions/poc/lib/python3.12/site-packages/resources/azure/database/elastic-database-pools.png",
			label=Embeddings,
			pos="515.5,273.5",
			shape=none,
			width=1.4028];
	}
	subgraph "cluster_Storage Layer" {
		graph [bb="612,106.5,729,281.5",
			bgcolor="#E5F5FD",
			fontname="Sans-Serif",
			fontsize=12,
			label="Storage Layer",
			labeljust=l,
			lheight=0.18,
			lp="662.5,271",
			lwidth=1.18,
			pencolor="#AEB6BE",
			rankdir=LR,
			shape=box,
			style=rounded
		];
		"********************************"	[height=1.9028,
			image="/home/<USER>/.pyenv/versions/poc/lib/python3.12/site-packages/resources/generic/storage/storage.png",
			label=Documentos,
			pos="670.5,183.5",
			shape=none,
			width=1.4028];
	}
	"4bd2c64e259943c899acfa408df0ddef"	[height=1.9028,
		image="/home/<USER>/.pyenv/versions/poc/lib/python3.12/site-packages/resources/onprem/client/user.png",
		label=Usuario,
		pos="50.5,273.5",
		shape=none,
		width=1.4028];
	"4bd2c64e259943c899acfa408df0ddef" -> "********************************"	[dir=forward,
		fontcolor="#2D3436",
		fontname="Sans-Serif",
		fontsize=13,
		pos="e,154.9,273.5 101.25,273.5 101.25,273.5 144.9,273.5 144.9,273.5"];
	a1bf21c209d44dd697a00a6efa22b00b	[height=1.9028,
		image="/home/<USER>/.pyenv/versions/poc/lib/python3.12/site-packages/resources/azure/storage/data-box.png",
		label="Nuevos Documentos",
		pos="515.5,93.5",
		shape=none,
		width=1.4028];
	a1bf21c209d44dd697a00a6efa22b00b -> "********************************"	[dir=forward,
		fontcolor="#2D3436",
		fontname="Sans-Serif",
		fontsize=13,
		pos="e,619.9,138.5 566.25,138.5 566.25,138.5 609.9,138.5 609.9,138.5"];
	"********************************" -> "********************************"	[dir=forward,
		fontcolor="#2D3436",
		fontname="Sans-Serif",
		fontsize=13,
		pos="e,309.9,273.5 256.25,273.5 256.25,273.5 299.9,273.5 299.9,273.5"];
	"********************************" -> "********************************"	[dir=forward,
		fontcolor="#2D3436",
		fontname="Sans-Serif",
		fontsize=13,
		pos="e,464.9,273.5 411.25,273.5 411.25,273.5 454.9,273.5 454.9,273.5"];
	"********************************" -> "********************************"	[dir=forward,
		fontcolor="#2D3436",
		fontname="Sans-Serif",
		fontsize=13,
		pos="e,619.9,228.5 566.25,228.5 566.25,228.5 609.9,228.5 609.9,228.5"];
}
