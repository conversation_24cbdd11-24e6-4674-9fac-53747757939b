# diagram.py
from diagrams import Cluster, Diagram
from diagrams.onprem.client import User, Client
from diagrams.azure.ml import CognitiveServices
from diagrams.azure.database import ElasticDatabasePools
from diagrams.generic.storage import Storage
from diagrams.azure.storage import DataBox


with Diagram("CogniSearch Architecture",filename="CogniSearch" ,outformat=["jpg", "png", "dot", "pdf"], show=False):
    user = User("Usuario")
    raw_data = DataBox("Nuevos Documentos")
    
    with Cluster("Web-App Layer"):
        ui_front_end = Client("Streamlit Chat")

    with Cluster("Backend"):
        llm = CognitiveServices("Large Language Model API")    

    with Cluster("VectorStore"):
        vector_store = ElasticDatabasePools("Embeddings")    

    with Cluster("Storage Layer"):
        bucket = Storage("Documentos")

    user >> ui_front_end >> llm >> vector_store >> bucket
    raw_data >> bucket