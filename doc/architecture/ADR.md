---
parent: Architectural Decisions
nav_order: 1
---
# Use Python as Full Stack Language for the POC of CogniSearch

## Context and Problem Statement

In order to start the POC simple and fast a single language hopefully is needed
to simplify developmnet by layer

Front End : Streamlit -> IONIC Angular 
Backend : FastAPI & Langchain
Workflow : Langchain
Other Layers:  <https://www.lyzr.ai/how-to-build-a-state-of-the-art-sota-rag-engine/>

## Considered Options

JavaScript

## Decision Outcome

Python

### Consequences

* Good, simple stack
* Bad, In the Future it might fall short

## Pros and Cons of the Options
