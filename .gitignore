# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
.pybuilder/
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
#   For a library or package, you might want to ignore these files since the code is
#   intended to run in multiple environments; otherwise, check them in:
# .python-version

# pipenv
#   According to pypa/pipenv#598, it is recommended to include Pipfile.lock in version control.
#   However, in case of collaboration, if having platform-specific dependencies or dependencies
#   having no cross-platform support, pipenv may install dependencies that don't work, or not
#   install all needed dependencies.
#Pipfile.lock

# poetry
#   Similar to Pipfile.lock, it is generally recommended to include poetry.lock in version control.
#   This is especially recommended for binary packages to ensure reproducibility, and is more
#   commonly ignored for libraries.
#   https://python-poetry.org/docs/basic-usage/#commit-your-poetrylock-file-to-version-control
#poetry.lock

# pdm
#   Similar to Pipfile.lock, it is generally recommended to include pdm.lock in version control.
#pdm.lock
#   pdm stores project-wide configurations in .pdm.toml, but it is recommended to not include it
#   in version control.
#   https://pdm.fming.dev/#use-with-ide
.pdm.toml

# PEP 582; used by e.g. github.com/David-OConnor/pyflow and github.com/pdm-project/pdm
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
#.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# PyCharm
#  JetBrains specific template is maintained in a separate JetBrains.gitignore that can
#  be found at https://github.com/github/gitignore/blob/main/Global/JetBrains.gitignore
#  and can be added to the global gitignore or merged into this file.  For a more nuclear
#  option (not recommended) you can uncomment the following to ignore the entire idea folder.
#.idea/


# Ignore pickles
*.pkl
**/*.pkl
*.pickle
**/*.pickle

# Ignore DB bin files
**/*.bin
/src/cognisearch/db

src/cognisearch/training/modelo_final_entrenado.pt
src/cognisearch/models/template/llama-2-7b-chat.Q5_K_M.gguf
src/cognisearch/api_key.py
src/cognisearch/db/chroma.sqlite3


*~
*.sw[mnpcod]
*.log
*.lock
*.tmp
*.tmp.*
log.txt
*.sublime-project
*.sublime-workspace
*.tgz

.idea/
.vscode/
.sass-cache/
.versions/
coverage/
collection/
dist/
dist-transpiled/
node_modules/
tmp/
temp/
core/theme-builder/
core/test-components/
core/css/
$RECYCLE.BIN/

.DS_Store
Thumbs.db
UserInterfaceState.xcuserstate
.env

.package.tmp.json

src/themes/version.scss
scripts/e2e/webpackEntryPoints.json
scripts/build/e2e-generated-tsconfig.json
*.css.ts

stats.json

# demo stuff
demos/node_modules
demos/polyfills
demos/css
demos/fonts
demos/src/**/*.js
demos/src/**/*.map
demos/src/**/*.ngfactory.ts
demos/src/**/*.d.ts
demos/src/**/*.metadata.json
demos/src/**/*.css.shim.ts
prerender.html
prerender-domino.html
prerender-hydrated.html
prerender-static.html

# stencil
packages/react/css/
packages/vue/css/
core/components/
core/css/
core/hydrate/
core/loader/
core/www/
.stencil/

# playwright
core/test-results/
core/playwright-report/

# ground truths generated outside of docker should not be committed to the repo
core/**/*-snapshots/*

# new ground truths should only be generated inside of docker which will result in -linux.png screenshots
!core/**/*-snapshots/*-linux.png

# these files are going to be different per-developer environment so do not add them to the repo
core/docker-display.txt
core/docker-display-volume.txt

# angular
packages/angular/css/
packages/angular/test/build/
.angular/

# vue
packages/vue/test/build/

# react
packages/react/test/build/

# react router
packages/react-router/test/build/

.npmrc


#amplify-do-not-edit-begin
amplify/\#current-cloud-backend
amplify/.config/local-*
amplify/logs
amplify/mock-data
amplify/mock-api-resources
amplify/backend/amplify-meta.json
amplify/backend/.temp
build/
dist/
node_modules/
aws-exports.js
awsconfiguration.json
amplifyconfiguration.json
amplifyconfiguration.dart
amplify-build-config.json
amplify-gradle-config.json
amplifytools.xcconfig
.secret-*
**.sample
#amplify-do-not-edit-end

www/
src/cognisearch/Synthon/training/Doc_Types/~$SUMEN_El_Capital.docx
src/cognisearch/Synthon/SYNTHON/QA-Simplificado-(FULL-API)(SYNTHON)/training/Doc_Types/~$SUMEN_El_Capital.docx
src/cognisearch/Synthon/SYNTHON/QA-Simplificado-(FULL-API)(SYNTHON)/db/_17451-VT 50_es_compressed.pdf
src/cognisearch/Synthon/SYNTHON/QA-Simplificado-(FULL-API)(SYNTHON)/db/_17451-VT 50_es.pdf
src/cognisearch/Synthon/SYNTHON/QA-Simplificado-(FULL-API)(SYNTHON)/db/\#901-1016_WP150_en_Rev.0_compressed.pdf
src/cognisearch/Synthon/SYNTHON/QA-Simplificado-(FULL-API)(SYNTHON)/db/\#901-1016_WP150_en_Rev.0.pdf
src/cognisearch/Synthon/SYNTHON-TRUCK-DEMO/QA-Simplificado-(FULL-API)(SYNTHON2)/training/Doc_Types/~$SUMEN_El_Capital.docx
src/cognisearch/Synthon/SYNTHON-TRUCK-DEMO/QA-Simplificado-(FULL-API)(SYNTHON2)/db/\[compressed\]1972_pages_DaimlerChrysler_Jeep_Cherokee_2000-service-manual.pdf
src/cognisearch/Synthon/SYNTHON-TRUCK-DEMO/QA-Simplificado-(FULL-API)(SYNTHON2)/db/\[compressed\]1361_pages_Workshop-Manual-P38-Range-Rover.pdf
src/cognisearch/Synthon/SYNTHON-TRUCK-DEMO/QA-Simplificado-(FULL-API)(SYNTHON2)/db/1972_pages_DaimlerChrysler_Jeep_Cherokee_2000-service-manual.pdf
src/cognisearch/Synthon/SYNTHON-TRUCK-DEMO/QA-Simplificado-(FULL-API)(SYNTHON2)/db/1361_pages_Workshop-Manual-P38-Range-Rover.pdf
src/cognisearch/Synthon/training/Doc_Types/~$SUMEN_El_Capital.docx
*.docx
/src/cognisearch/Synthon/training/Doc_Types
