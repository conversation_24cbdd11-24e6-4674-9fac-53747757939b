@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Quicksand:wght@300..700&display=swap');

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body,
html {
    height: 100%;
    font-family: "Poppins", sans-serif;
}

.container {
    position: relative;
    width: 100%;
    height: 91.7%;
    display: flex;
    flex-direction: column;
}

.advertising {
    position: relative;
    flex-grow: 1;
    overflow: hidden;
}

.advertising video {
    position: absolute;
    top: 50%;
    left: 50%;
    min-width: 100%;
    min-height: 100%;
    width: auto;
    height: auto;
    transform: translate(-50%, -50%);
    object-fit: cover;
}

.textBox {
    position: absolute;
    display: flex;
    justify-content: center;
    align-items: center;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #060000;
    color: #ffffff;
    font-size: 9vw;
    mix-blend-mode: multiply;    /* exclusion */
}

.textBox h1 {
    font-family: 'Quicksand';
    font-weight: 400; /* Este es el peso 'Light' */
}

header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 80px;
    background-color: #27637f;
    z-index: 3;
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;
    padding: 0 20px;
}

.menu-item {
    position: relative;
    color: white;
    cursor: pointer;
    font-size: 16px;
}

.submenu {
    display: none;
    position: absolute;
    top: 100%;
    background-color: #008eb5;
    border-radius: 5px 5px 5px 5px;
    min-width: 200px;
}

.menu-item:hover .submenu {
    display: block;
	border-radius: 5px 5px 5px 5px;
}

.menu-item.legal .submenu {
    left: 0;
}

.menu-item.login .submenu {
    right: 0;
}

.submenu a {
    display: block;
    color: white;
    text-decoration: none;
    padding: 10px 15px;
    white-space: nowrap;
    font-size: 14px;
    transition: background-color 0.3s ease;
}

.submenu a:hover {
    background-color: #42c2e5cf;
	border-radius: 5px 5px 5px 5px;
}

/* Footer */
.site-footer {
    background-color: #26272b;
    padding: 20px;
    font-size: 15px;
    line-height: 24px;
    color: #737373;
    text-align: center;
    z-index: 10;
    position: sticky;
	flex-shrink: 0; /* Prevent footer from shrinking */
	bottom: 0;
    width: 100%;
}

.site-footer hr {
    border-top-color: #bbb;
    opacity: 0.1
}

.site-footer hr.small {
    margin: 20px 0
}

.site-footer h6 {
    color: #fff;
    font-size: 16px;
    text-transform: uppercase;
    margin-top: 5px;
    letter-spacing: 2px
}

.site-footer a {
    color: #737373;
}

.site-footer a:hover {
    color: #ffffff;
    text-decoration: none;
}

.footer-links {
    padding-left: 0;
    list-style: none
}

.footer-links li {
    display: block
}

.footer-links a {
    color: #737373
}

.footer-links a:active,
.footer-links a:focus,
.footer-links a:hover {
    color: #3366cc;
    text-decoration: none;
}

.footer-links.inline li {
    display: inline-block
}

.site-footer .social-icons {
    text-align: center
}

.site-footer .social-icons a {
    width: 40px;
    height: 40px;
    line-height: 40px;
    margin-left: 6px;
    margin-right: 0;
    border-radius: 100%;
    background-color: #33353d
}

.copyright-text {
    margin: 0
}

.fallback-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, #12c2e9, #c471ed, #f64f59);
    background-size: 400% 400%;
    animation: gradient 15s ease infinite;
    display: none;
}

.hat {
    position: absolute;
    width: 6vw;
    height: 6vw;
    top: 9.2px;
    left: 43.9%;
    transform: translateX(-50%);
    background-image: url('../img/hatRAGTech.png');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
}

@keyframes gradient {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

@media (max-width: 768px) {
    .textBox {
        font-size: 9vw;
    }

    nav {
        flex-direction: row;
        justify-content: space-between;
        padding: 0 10px;
    }

    .menu-item {
        margin: 0;
    }

    .submenu {
        position: absolute;
        width: auto;
    }
}