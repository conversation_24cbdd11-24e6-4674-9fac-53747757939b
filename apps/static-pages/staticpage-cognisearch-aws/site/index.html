<!DOCTYPE html>
<html lang="es">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CogniSearch</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="icon" type="image/x-icon" href="img/icon.ico">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
  </head>
  <body>
    <div class="container">
      <header>
        <nav>
          <div class="menu-item legal"> 
		    Legal 
		    <div class="submenu">
              <a href="https://ragtech.cl/privacy_policy.html" target="_blank">Política de privacidad</a>
              <a href="https://ragtech.cl/use_terms.html" target="_blank">Términos de Uso</a>
              <a href="https://ragtech.cl/legal_info.html" target="_blank">Información Legal</a>
              <a href="https://ragtech.cl/cookie_policy.html" target="_blank">Cookie Policy</a>
              <a href="https://ragtech.cl/cookie_settings.html" target="_blank">Cookie Settings</a>
            </div>
          </div>
          <div class="menu-item login"> Login <div class="submenu">
              <a href="#" target="_blank">Acceso Clientes</a>
            </div>
          </div>
        </nav>
      </header>
      <section class="advertising">
        <div class="fallback-bg"></div>
        <video id="bgVideo" autoplay muted loop playsinline preload="auto">
          <source src="media/tranquilidad.webm" type="video/webm">Tu navegador no soporta el elemento de video.
        </video>
        <div class="textBox">
          <span style="position: relative;">
            <div class="hat"></div>
            <div>
              <h1>CogniSearch</h1>
            </div>
		  </span>
		</div
      </section>
    </div>
    <footer class="site-footer">
      <div class="container">
        <div class="row text-center">
          <div class="col-12"> Hecho con ❤ y 🦾 por RAGTech. Copyright © 2024 Todos los derechos reservados: <a href="#">RAGTech SpA & CogniSearch®</a>. </div>
        </div>
		</div>
    </footer>
    <script>
      document.addEventListener('DOMContentLoaded', function() {
        var video = document.getElementById('bgVideo');
        var fallbackBg = document.querySelector('.fallback-bg');

        function handleVideoError(e) {
          console.error('Error en la reproducción del video:', e);
          video.style.display = 'none';
          fallbackBg.style.display = 'block';
        }
        video.addEventListener('error', handleVideoError);
        video.addEventListener('loadedmetadata', function() {
          if (video.duration < 3) {
            console.warn('El video es muy corto, duracion:', video.duration);
          }
        });
        video.addEventListener('ended', function() {
          this.currentTime = 0;
          var playPromise = this.play();
          if (playPromise !== undefined) {
            playPromise.catch(handleVideoError);
          }
        });
        // Intentar reproducir el video
        var playAttempt = setInterval(function() {
          var playPromise = video.play();
          if (playPromise !== undefined) {
            playPromise.then(_ => {
              clearInterval(playAttempt);
            }).catch(handleVideoError);
          }
        }, 1000);
      });
    </script>
  </body>
</html><!-- TI-A-MO-LA-TITA -->