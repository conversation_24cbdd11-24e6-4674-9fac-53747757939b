# Documentation

Sync local folder to s3 bucket

```shell
cd site
```

```shell
─░▒▓ ~/d/g/juanmherrerav/megamind-poc/apps/static-pages/staticpage-ragtech-aws/site  on main *3 ?3 ▓▒░────────────────────────────────░▒▓ ✔  at 17:54:55 ▓▒░─╮
╰─ ll                   ─╯
total 84K
-rw-r--r-- 1 <USER> <GROUP>  13K Oct 21 22:04 cookie_policy.html
-rw-r--r-- 1 <USER> <GROUP> 2.9K Oct 21 21:38 cookie_settings.html
drwxr-xr-x 2 <USER> <GROUP> 4.0K Oct 25 17:51 css
drwxr-xr-x 2 <USER> <GROUP> 4.0K Oct 25 17:51 fonts
drwxr-xr-x 2 <USER> <GROUP> 4.0K Oct 25 17:51 img
-rw-r--r-- 1 <USER> <GROUP>  13K Oct 21 22:37 index.html
drwxr-xr-x 2 <USER> <GROUP> 4.0K Oct 25 17:52 js
-rw-r--r-- 1 <USER> <GROUP> 7.5K Oct 21 22:08 legal_info.html
drwxr-xr-x 2 j<PERSON>rera jherrera 4.0K Oct 25 17:52 media
-rw-r--r-- 1 <USER> <GROUP> 8.3K Oct 21 22:08 privacy_policy.html
-rw-r--r-- 1 <USER> <GROUP> 6.1K Oct 21 22:08 use_terms.html

╭─░▒▓ ~/d/g/juanmherrerav/megamind-poc/apps/static-pages/staticpage-ragtech-aws/site  on main *3 ?3 ▓▒░────────────────────────────────░▒▓ ✔  at 17:54:56 ▓▒░─╮
╰─ aws s3 ls                                                                                                    ─╯
2024-10-03 18:16:10 cognisearch.cl
2024-10-14 21:42:37 eks-app-storage-bucket
2024-10-06 21:17:22 ragtech.cl
2024-10-03 18:28:52 www.cognisearch.cl
2024-10-06 21:30:28 www.ragtech.cl

╭─░▒▓ ~/d/g/juanmherrerav/megamind-poc/apps/static-pages/staticpage-ragtech-aws/site  on main *3 ?3 ▓▒░────────────────────────────────░▒▓ ✔  at 17:55:08 ▓▒░─╮
╰─ aws s3 ls www.ragtech.cl                   ─╯
                           PRE css/
                           PRE fonts/
                           PRE img/
                           PRE js/
2024-10-06 21:25:59      14320 index.html

╭─░▒▓ ~/d/g/juanmherrerav/megamind-poc/apps/static-pages/staticpage-ragtech-aws/site  on main *3 ?3 ▓▒░────────────────────────────────░▒▓ ✔  at 17:55:23 ▓▒░─╮
╰─ ls -larth                                  ─╯
total 92K
drwxr-xr-x 3 <USER> <GROUP> 4.0K Oct 13 22:46 ..
-rw-r--r-- 1 <USER> <GROUP> 2.9K Oct 21 21:38 cookie_settings.html
-rw-r--r-- 1 <USER> <GROUP>  13K Oct 21 22:04 cookie_policy.html
-rw-r--r-- 1 <USER> <GROUP> 7.5K Oct 21 22:08 legal_info.html
-rw-r--r-- 1 <USER> <GROUP> 8.3K Oct 21 22:08 privacy_policy.html
-rw-r--r-- 1 <USER> <GROUP> 6.1K Oct 21 22:08 use_terms.html
-rw-r--r-- 1 <USER> <GROUP>  13K Oct 21 22:37 index.html
drwxr-xr-x 7 <USER> <GROUP> 4.0K Oct 25 17:50 .
drwxr-xr-x 2 <USER> <GROUP> 4.0K Oct 25 17:51 css
drwxr-xr-x 2 <USER> <GROUP> 4.0K Oct 25 17:51 fonts
drwxr-xr-x 2 <USER> <GROUP> 4.0K Oct 25 17:51 img
drwxr-xr-x 2 <USER> <GROUP> 4.0K Oct 25 17:52 media
drwxr-xr-x 2 <USER> <GROUP> 4.0K Oct 25 17:52 js

╭─░▒▓ ~/d/g/juanmherrerav/megamind-poc/apps/static-pages/staticpage-ragtech-aws/site  on main *3 ?3 ▓▒░────────────────────────────────░▒▓ ✔  at 17:57:07 ▓▒░─╮
╰─ aws s3 sync . s3://www.ragtech.cl                   ─╯
upload: ./cookie_settings.html to s3://www.ragtech.cl/cookie_settings.html
upload: ./cookie_policy.html to s3://www.ragtech.cl/cookie_policy.html
upload: img/bajar.gif to s3://www.ragtech.cl/img/bajar.gif       
upload: fonts/DesignSystemB-500R.ttf to s3://www.ragtech.cl/fonts/DesignSystemB-500R.ttf
upload: fonts/DesignSystemB-500R.woff2 to s3://www.ragtech.cl/fonts/DesignSystemB-500R.woff2
upload: img/depth-1.png to s3://www.ragtech.cl/img/depth-1.png   
upload: fonts/DesignSystemB-500R.woff to s3://www.ragtech.cl/fonts/DesignSystemB-500R.woff
upload: css/style.css to s3://www.ragtech.cl/css/style.css        
upload: img/depth-2.png to s3://www.ragtech.cl/img/depth-2.png     
upload: img/depth-3.png to s3://www.ragtech.cl/img/depth-3.png     
upload: ./legal_info.html to s3://www.ragtech.cl/legal_info.html   
upload: img/logo.png to s3://www.ragtech.cl/img/logo.png           
upload: js/cookie_manager.js to s3://www.ragtech.cl/js/cookie_manager.js
upload: ./index.html to s3://www.ragtech.cl/index.html            
upload: img/logo_ghost.png to s3://www.ragtech.cl/img/logo_ghost.png
upload: img/img_parallax3.webp to s3://www.ragtech.cl/img/img_parallax3.webp
upload: img/background.jpg to s3://www.ragtech.cl/img/background.jpg
upload: ./use_terms.html to s3://www.ragtech.cl/use_terms.html  
upload: ./privacy_policy.html to s3://www.ragtech.cl/privacy_policy.html
upload: img/icon.ico to s3://www.ragtech.cl/img/icon.ico        
upload: img/img_parallax.webp to s3://www.ragtech.cl/img/img_parallax.webp
upload: media/tranquilidad.webm to s3://www.ragtech.cl/media/tranquilidad.webm

```


Links :

[AWS CLI S3 commands](https://docs.aws.amazon.com/cli/v1/userguide/cli-services-s3-commands.html) 

Run content validation through Cloudfront Distribution

Note: if you want to do it via aws command line interface you can do the following command

aws cloudfront create-invalidation --distribution-id <your distribution id> --paths "/*"

The /* will invalidate everything, replace that with specific files if you only updated a few.

To find the list of cloud front distribution id's you can do this command aws cloudfront list-distributions

Look at these two links for more info on those 2 commands:

https://docs.aws.amazon.com/cli/latest/reference/cloudfront/create-invalidation.html

https://docs.aws.amazon.com/cli/latest/reference/cloudfront/list-distributions.html

[AWS cloudfront not updating on update of files in S3](https://stackoverflow.com/questions/30154461/aws-cloudfront-not-updating-on-update-of-files-in-s3) 