# Documentation

Sync local folder to s3 bucket

```shell
╭─░▒▓ ~/d/g/juanmherrerav/megamind-poc/apps/static-pages/staticpage-ragtech-aws/site  on main *3 ?3 ▓▒░────────────────────────────────────────────░▒▓ ✔  took 9s  at 17:57:59 ▓▒░─╮
╰─ cd ../../staticpage-cognisearch-aws/site                                                                                                                                       ─╯

╭─░▒▓ ~/d/g/j/megamind-poc/apps/static-pages/staticpage-cognisearch-aws/site  on main *3 ?4 ▓▒░─────────────────────────────────────────────────────────────░▒▓ ✔  at 18:01:13 ▓▒░─╮
╰─ ls -larth                                                                                                                                                                      ─╯
total 32K
-rw-r--r-- 1 <USER> <GROUP> 3.4K Oct 21 22:19 index.html
drwxr-xr-x 7 <USER> <GROUP> 4.0K Oct 25 17:43 .
drwxr-xr-x 2 <USER> <GROUP> 4.0K Oct 25 17:44 fonts
drwxr-xr-x 2 <USER> <GROUP> 4.0K Oct 25 17:44 css
drwxr-xr-x 2 <USER> <GROUP> 4.0K Oct 25 17:44 img
drwxr-xr-x 2 <USER> <GROUP> 4.0K Oct 25 17:44 js
drwxr-xr-x 2 <USER> <GROUP> 4.0K Oct 25 17:44 media
drwxr-xr-x 3 <USER> <GROUP> 4.0K Oct 25 17:58 ..

╭─░▒▓ ~/d/g/j/megamind-poc/apps/static-pages/staticpage-cognisearch-aws/site  on main *3 ?4 ▓▒░─────────────────────────────────────────────────────────────░▒▓ ✔  at 18:01:17 ▓▒░─╮
╰─ aws s3 ls www.cognisearch.cl                                                                                                                                                   ─╯
                           PRE css/
                           PRE fonts/
                           PRE img/
                           PRE js/
                           PRE media/
2024-10-03 18:20:03       3216 index.html

╭─░▒▓ ~/d/g/j/megamind-poc/apps/static-pages/staticpage-cognisearch-aws/site  on main *3 ?4 ▓▒░─────────────────────────────────────────────────────────────░▒▓ ✔  at 18:01:29 ▓▒░─╮
╰─ aws s3 sync . s3://www.cognisearch.cl                                                                                                                                          ─╯
upload: css/style.css to s3://www.cognisearch.cl/css/style.css
upload: ./index.html to s3://www.cognisearch.cl/index.html    
upload: img/logoRAGTech.png to s3://www.cognisearch.cl/img/logoRAGTech.png
upload: img/hatRAGTech.png to s3://www.cognisearch.cl/img/hatRAGTech.png
upload: img/icon.ico to s3://www.cognisearch.cl/img/icon.ico      
upload: media/tranquilidad.webm to s3://www.cognisearch.cl/media/tranquilidad.webm
upload: media/tranquilidad_orden_progreso.webm to s3://www.cognisearch.cl/media/tranquilidad_orden_progreso.webm
```


Links :

[AWS CLI S3 commands](https://docs.aws.amazon.com/cli/v1/userguide/cli-services-s3-commands.html) 

Run content validation through Cloudfront Distribution

Note: if you want to do it via aws command line interface you can do the following command

aws cloudfront create-invalidation --distribution-id <your distribution id> --paths "/*"

The /* will invalidate everything, replace that with specific files if you only updated a few.

To find the list of cloud front distribution id's you can do this command aws cloudfront list-distributions

Look at these two links for more info on those 2 commands:

https://docs.aws.amazon.com/cli/latest/reference/cloudfront/create-invalidation.html

https://docs.aws.amazon.com/cli/latest/reference/cloudfront/list-distributions.html

[AWS cloudfront not updating on update of files in S3](https://stackoverflow.com/questions/30154461/aws-cloudfront-not-updating-on-update-of-files-in-s3) 