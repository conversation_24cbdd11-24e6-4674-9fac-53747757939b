@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;900&display=swap');


body, html {
  height: 100%;
  margin: 0;
  font-family: 'Poppins', sans-serif;
  -webkit-font-smoothing: antialiased;
  overflow-x: hidden;
  width: 100%;
  max-width: 100%;
}


h2 {
  letter-spacing: 2px;
  text-transform: uppercase;
  font-size: 1.4em;
  font-family: "Montserrat", sans-serif;
  text-align: center;
  color: #7abcc3;
  font-weight: 500;
  margin-bottom: 20px;
}

h3 {
  letter-spacing: 3px;
  text-transform: uppercase;
  font: 20px "Montserrat", sans-serif;
  color: #7abcc3;
  font-weight: 500;
  text-align: center;
}


p.hero-description {
    font-size: 1.5rem;
    line-height: 2rem;
    color: #fff;
}

.btn-custom {
    background-color: #f7f1d7 !important;
    text-decoration: none !important;
    transition: background-color .3s;
}

.header-container {
    position: relative;
    width: 100%;
    height: 10%;
    display: flex;
    flex-direction: column;
	z-index: 9999;
}

header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 10%;
    background-color: #27637f;
    z-index: 100;
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;
    padding: 0 20px;
}

.menu-item {
    position: relative;
    color: white;
    cursor: pointer;
    font-size: 16px;
	position: sticky;
}

.submenu {
    display: none;
    position: absolute;
    top: 100%;
    background-color: #008eb5;
    border-radius: 5px 5px 5px 5px;
    min-width: 200px;
}

.menu-item:hover .submenu {
    display: block;
	border-radius: 5px 5px 5px 5px;
}

.menu-item.legal .submenu {
    left: 0;
}

.menu-item.login .submenu {
    right: 0;
}

.submenu a {
    display: block;
    color: white;
    text-decoration: none;
    padding: 10px 15px;
    white-space: nowrap;
    font-size: 14px;
    transition: background-color 0.3s ease;
}

.submenu a:hover {
    background-color: #42c2e5cf;
	color: #ffffff;
    font-weight: 400;
	border-radius: 5px 5px 5px 5px;
}

/* Sección de video */
.advertising {
    position: relative;
    width: 100%;
    height: 100vh;
    overflow: hidden;
    background: #000;
}

.advertising video {
    position: absolute;
    top: 50%;
    left: 50%;
    min-width: 100%;
    min-height: 100%;
    width: auto;
    height: auto;
    transform: translate(-50%, -50%);
    object-fit: cover;
	mix-blend-mode: exclusion;    /* difference */
}

/* Variaciones de efectos que puedes usar alternadamente */
.video-dramatic {
    filter: contrast(1.9) brightness(0.3) saturate(0.9);
}

.video-vintage {
    filter: sepia(0.4) contrast(1.1) brightness(0.3) saturate(2.3);
}

.hat {
    position: absolute;
    width: 308px;
    max-width: 20em;
    aspect-ratio: 1 / 1;
    top: -179px;
    left: 50%;
    transform: translateX(-50%);
    background-image: url('../img/logo.png');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
}

/* Estilos existentes para .textBox */
.textBox {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 2;
    text-align: center;
    width: 100%;
    padding: 0 20px; /* Añadido para evitar que el texto toque los bordes en pantallas pequeñas */
    box-sizing: border-box; /* Asegura que el padding no aumente el ancho total */
	padding-top: 60px; /* Adjust as needed to accommodate the logo */
}

/* Estilos mejorados para el contenido de .textBox */
p.title-legacy {
    font: 12px "Segoe UI", sans-serif;
    color: #cf1717;
    font-size: clamp(1rem, 4vw, 5em); /* Tamaño de fuente responsive */
    font-weight: 400;
    opacity: 1;
    margin-bottom: 0.5em; /* Espacio entre los títulos */
}

p.title-description {
    font-size: clamp(1rem, 3vw, 1.5rem); /* Tamaño de fuente responsive */
    line-height: 1.5;
    color: #fff;
    max-width: 800px; /* Limita el ancho máximo para mejorar la legibilidad */
    margin: 0 auto; /* Centra el texto si es más estrecho que el contenedor */
}

.version-label {
    font: 12px "Segoe UI", sans-serif;
    text-align: left;
    color: #fff;
    padding-left: 10px;
}

/* Imágenes de fondo paralax */
.bgimg-1, .bgimg-2, .bgimg-3, .bgimg-4 {
    position: relative;
    opacity: 0.65;
    background-attachment: fixed;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    width: 100vw;
    margin-left: calc(-50vw + 50%);
}

.bgimg-1 {
    background-image: url("../img/img_parallax.webp");
    min-height: 400px;
}

.bgimg-2 {
    background-image: url("../img/img_parallax2.webp");
    min-height: 400px;
}

.bgimg-3 {
    background-image: url("../img/img_parallax3.webp");
    min-height: 400px;
}

.bgimg-4 {
    background-image: url("../img/img_parallax4.webp");
    min-height: 400px;
}

.caption {
    position: absolute;
    left: 0;
    top: 50%;
    width: 100%;
    text-align: center;
    color: #000;
}

.caption span.border {
    background-color: #834949db;
    color: #fff;
    padding: 9px;
    font-size: 25px;
    letter-spacing: 4px;
	line-height: 47px;
	margin: 10px;
}

/* Features container */
.features-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    gap: 20px;
    padding: 0 15px;
    box-sizing: border-box;
    width: 100%;
    max-width: 100%;
}

.list-container {
    flex: 1 1 300px;
    min-width: 250px;
    background-color: #3a3f48;
    padding: 20px;
    border: 3px solid;
    border-radius: 10px;
    border-color: #27637f;
    margin-bottom: 20px;
    box-sizing: border-box;
}

.list-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 10px;
}

.list-icon {
    color: #7abcc3;
    margin-right: 10px;
}

/* Notebook styles */
.notebook-page {
    background-color: #fffada;
    padding: 4px 20px;
    position: relative;
    max-width: fit-content;
    margin: 1px auto;
    border-radius: 5px;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
}

.notebook-content {
    background-image: 
        linear-gradient(#e6e6e6 1px, transparent 1px),
        linear-gradient(90deg, #e6e6e6 1px, transparent 1px);
    background-size: 100% 30px, 30px 100%;
    line-height: 30px;
    font-family: 'Courier New', monospace;
    color: #333;
    padding: 0 0 0 50px;
    text-align: justify;
}

.notebook-page::before {
    content: '';
    position: absolute;
    top: 0;
    left: 40px;
    height: 100%;
    width: 2px;
    background-color: #ff9999;
}

.notebook-page::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 30px;
    height: 100%;
    background-color: #ff9999;
    border-right: 2px solid #ff6666;
}

/* Tooltip styles */
.tooltip {
    position: relative;
    display: inline-block;
    cursor: pointer;
    font-weight: 600;
    color: #ffffff;
}

.tooltip .tooltiptext {
    visibility: hidden;
    width: max-content;
    max-width: 200px;
    background-color: #ffffd3;
    color: #252525;
    text-align: center;
    border-radius: 6px;
    padding: 10px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    transform: translateX(-50%);
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 11px;
    border: 1px solid #d9b582;
    font-weight: 400;
    line-height: normal;
}

.tooltip:hover .tooltiptext {
    visibility: visible;
    opacity: 1;
}

.tooltip .tooltiptext::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: #c9c9c9 transparent transparent transparent;
}

/* Estilos generales para enlaces */
a {
    color: inherit;
    text-decoration: none;
    transition: all 0.3s ease;
}

/* Estilos para el estado hover de los enlaces */
a:hover {
    color: inherit;
    text-decoration: none;
}

/* Estilos específicos para los enlaces del footer */
.footer-container {
    position: relative;
    width: 100%;
    height: 91.7%;
    display: flex;
    flex-direction: column;
}

footer a, footer a:hover {
    color: #737373;
    text-decoration: none;
}

/* Footer styles */
.site-footer {
    background-color: #26272b;
    padding: 20px;
    font-size: 15px;
    line-height: 24px;
    color: #737373;
    text-align: center;
}

.site-footer hr {
    border-top-color: #bbb;
    opacity: 0.1;
}

.site-footer a {
    color: #737373;
}

.site-footer a:hover {
    color: #ffffff;
    text-decoration: none;
}

/* Estilos para los íconos sociales */
.linkedin {
    display: inline-block;
    width: 50px;
    height: 50px;
    background-color: #005e8f; /* Color de fondo de LinkedIn */
    border-radius: 50%; /* Hacerlo circular */
    text-align: center;
    line-height: 50px; /* Centramos el icono verticalmente */
    color: white; /* Color del icono */
    font-size: 24px; /* Tamaño del icono */
    transition: background-color 0.3s ease; /* Transición para el hover */
	margin-top: 10px;
}

.linkedin:hover {
    background-color: #005582; /* Color más oscuro al pasar el mouse */
}


/* Pantallas grandes */
@media (max-width: 1700px) {
	.hat {
        position: absolute;
        width: 356px;
        max-width: 30em;
        aspect-ratio: 1 / 1;
        top: -145px;
        left: 50%;
        transform: translateX(-50%);
        background-image: url(../img/logo.png);
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;
    }
	
	p.title-legacy {
    font: 12px "Segoe UI", sans-serif;
    color: #cf1717;
    font-size: clamp(1rem, 2.2vw, 5em); /* Tamaño de fuente responsive */
    font-weight: 400;
    opacity: 1;
    margin-bottom: 0.5em; /* Espacio entre los títulos */
    }

    p.title-description {
        font-size: clamp(1rem, 3vw, 1.5rem); /* Tamaño de fuente responsive */
        line-height: 1.5;
        color: #fff;
        max-width: 800px; /* Limita el ancho máximo para mejorar la legibilidad */
        margin: 0 auto; /* Centra el texto si es más estrecho que el contenedor */
    }
}

/* Pantallas grandes */
@media (max-width: 1400px) {
	.hat {
        position: absolute;
        width: 356px;
        max-width: 30em;
        aspect-ratio: 1 / 1;
        top: -145px;
        left: 50%;
        transform: translateX(-50%);
        background-image: url(../img/logo.png);
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;
    }
	
	p.title-legacy {
    font: 12px "Segoe UI", sans-serif;
    color: #cf1717;
    font-size: clamp(1rem, 3vw, 5em); /* Tamaño de fuente responsive */
    font-weight: 400;
    opacity: 1;
    margin-bottom: 0.5em; /* Espacio entre los títulos */
    }

    p.title-description {
        font-size: clamp(1rem, 3vw, 1.5rem); /* Tamaño de fuente responsive */
        line-height: 1.5;
        color: #fff;
        max-width: 800px; /* Limita el ancho máximo para mejorar la legibilidad */
        margin: 0 auto; /* Centra el texto si es más estrecho que el contenedor */
    }
}

/* Laptops y desktops */
@media (max-width: 1200px) {
	.hat {
        position: absolute;
        width: 356px;
        max-width: 30em;
        aspect-ratio: 1 / 1;
        top: -145px;
        left: 50%;
        transform: translateX(-50%);
        background-image: url(../img/logo.png);
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;
    }
	
	p.title-legacy {
    font: 12px "Segoe UI", sans-serif;
    color: #cf1717;
    font-size: clamp(1rem, 3vw, 5em); /* Tamaño de fuente responsive */
    font-weight: 400;
    opacity: 1;
    margin-bottom: 0.5em; /* Espacio entre los títulos */
    }

    p.title-description {
        font-size: clamp(1rem, 3vw, 1.5rem); /* Tamaño de fuente responsive */
        line-height: 1.5;
        color: #fff;
        max-width: 800px; /* Limita el ancho máximo para mejorar la legibilidad */
        margin: 0 auto; /* Centra el texto si es más estrecho que el contenedor */
    }
}

/* Tablets y laptops pequeñas */
@media (max-width: 1024px) {
	.hat {
        position: absolute;
        width: 320px;
        max-width: 20em;
        aspect-ratio: 1 / 1;
        top: -124px;
        left: 50%;
        transform: translateX(-50%);
        background-image: url(../img/logo.png);
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;
    }
	
	p.title-legacy {
    font: 12px "Segoe UI", sans-serif;
    color: #cf1717;
    font-size: clamp(1rem, 3.5vw, 4em); /* Tamaño de fuente responsive */
    font-weight: 400;
    opacity: 1;
    margin-bottom: 0.5em; /* Espacio entre los títulos */
    }

    p.title-description {
        font-size: clamp(1rem, 3vw, 1.5rem); /* Tamaño de fuente responsive */
        line-height: 1.5;
        color: #fff;
        max-width: 800px; /* Limita el ancho máximo para mejorar la legibilidad */
        margin: 0 auto; /* Centra el texto si es más estrecho que el contenedor */
    }
}

/* Tablets pequeñas y móviles grandes */
@media (max-width: 768px) {
    .textBox {
        padding: 0 15px; /* Reduce el padding en pantallas más pequeñas */
    }

    .features-container {
        flex-direction: column;
        padding: 20px 15px;
    }
    
    .list-container {
        flex: 1 1 100%;
        max-width: 100%;
        margin-left: 0;
        margin-right: 0;
    }
    
    .caption span.border {
        font-size: 21px;
        margin: 5px;
        letter-spacing: 2px;
		margin: 10px;
    }
    
    .tooltip .tooltiptext {
        width: auto;
        max-width: 150px;
        font-size: 10px;
    }
	
	.hat {
        position: absolute;
        width: 308px;
        max-width: 20em;
        aspect-ratio: 1 / 1;
        top: -179px;
        left: 50%;
        transform: translateX(-50%);
        background-image: url(../img/logo.png);
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;
    }
	
	p.title-legacy {
    font: 12px "Segoe UI", sans-serif;
    color: #cf1717;
    font-size: clamp(1rem, 4vw, 5em); /* Tamaño de fuente responsive */
    font-weight: 400;
    opacity: 1;
    margin-bottom: 0.5em; /* Espacio entre los títulos */
    }

    p.title-description {
        font-size: clamp(1rem, 3vw, 1.5rem); /* Tamaño de fuente responsive */
        line-height: 1.5;
        color: #fff;
        max-width: 800px; /* Limita el ancho máximo para mejorar la legibilidad */
        margin: 0 auto; /* Centra el texto si es más estrecho que el contenedor */
    }
}

/* Móviles */
@media (max-width: 480px) {
    .caption span.border {
        font-size: 21px;
        margin: 5px;
        letter-spacing: 2px;
		margin: 10px;
    }
	
    p.title {
        margin-bottom: 0.3em; /* Reduce el espacio entre elementos en pantallas muy pequeñas */
		font-size: 5em;
    }

    p.title-legacy, p.title-description {
        margin-bottom: 0.3em;
    }
	
	.hat {
        position: absolute;
        width: 308px;
        max-width: 20em;
        aspect-ratio: 1 / 1;
        top: -179px;
        left: 50%;
        transform: translateX(-50%);
        background-image: url(../img/logo.png);
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;
    }
	
	p.title-legacy {
    font: 12px "Segoe UI", sans-serif;
    color: #cf1717;
    font-size: clamp(1rem, 6vw, 5em); /* Tamaño de fuente responsive */
    font-weight: 400;
    opacity: 1;
    margin-bottom: 0.5em; /* Espacio entre los títulos */
    }

    p.title-description {
        font-size: clamp(1rem, 3vw, 1.5rem); /* Tamaño de fuente responsive */
        line-height: 1.5;
        color: #fff;
        max-width: 800px; /* Limita el ancho máximo para mejorar la legibilidad */
        margin: 0 auto; /* Centra el texto si es más estrecho que el contenedor */
    }
}

/* Additional helper classes */
.absolute {
    position: absolute;
}

.lazyloaded {
    opacity: 1;
    transition: all 200ms, opacity 400ms;
    transition-delay: 0ms;
}

/* Estilo general para el formulario de cookies */
main {
    max-width: 600px;
    margin: 40px auto;
    padding: 20px;
    background-color: #f9f9f9;
    border-radius: 8px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    font-family: 'Poppins', sans-serif;
}

/* Encabezado del formulario */
main h2 {
    font-size: 1.8rem;
    margin-bottom: 20px;
    color: #27637f;
    text-align: center;
    letter-spacing: 1.5px;
}

/* Descripción debajo del título */
main p {
    font-size: 1rem;
    margin-bottom: 20px;
    color: #333;
    text-align: center;
}

/* Estilos para el formulario */
form {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

/* Estilo de cada div que contiene un checkbox y su etiqueta */
form div {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #eaf7f9;
    padding: 10px;
    border-radius: 5px;
    border: 1px solid #cce7eb;
}

/* Estilos para los labels */
form label {
    font-size: 1rem;
    color: #27637f;
    margin-left: 10px;
    flex: 1;
}

/* Estilo para los checkboxes */
form input[type="checkbox"] {
    width: 20px;
    height: 20px;
    accent-color: #27637f;
}

/* Estilo para el botón de envío */
form button[type="submit"] {
    margin-top: 20px;
    padding: 10px 20px;
    background-color: #27637f;
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 1rem;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

/* Efecto hover en el botón */
form button[type="submit"]:hover {
    background-color: #21809e;
}
