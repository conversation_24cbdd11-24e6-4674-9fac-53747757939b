document.addEventListener("DOMContentLoaded", function() {
    var tooltipElements = document.querySelectorAll('.tooltip');
    
    tooltipElements.forEach(function(element) {
        var tooltipText = element.querySelector('.tooltiptext');
        
        // Mostrar tooltip
        element.addEventListener('mouseenter', function() {
            tooltipText.style.visibility = 'visible';
            tooltipText.style.opacity = '1';
        });
        
        // Ocultar tooltip
        element.addEventListener('mouseleave', function(e) {
            // Verificar si el mouse no se mueve al tooltiptext
            if (!e.relatedTarget || !element.contains(e.relatedTarget)) {
                tooltipText.style.visibility = 'hidden';
                tooltipText.style.opacity = '0';
            }
        });

        // Hacer clickeable el contenido del tooltip
        tooltipText.addEventListener('click', function(e) {
            e.stopPropagation(); // Evitar que el clic se propague
            var link = this.getAttribute('data-link');
            if (link) {
                window.open(link, '_blank');
            }
        });

        // Prevenir que los clics en el tooltip cierren el tooltip
        tooltipText.addEventListener('mousedown', function(e) {
            e.preventDefault();
        });

        // Mantener el tooltip abierto cuando el mouse está sobre él
        tooltipText.addEventListener('mouseenter', function() {
            this.style.visibility = 'visible';
            this.style.opacity = '1';
        });

        // Cerrar el tooltip cuando el mouse sale de él
        tooltipText.addEventListener('mouseleave', function() {
            this.style.visibility = 'hidden';
            this.style.opacity = '0';
        });
    });

    // Evitar que los clics en el documento cierren los tooltips abiertos
    document.addEventListener('click', function(e) {
        tooltipElements.forEach(function(element) {
            if (!element.contains(e.target)) {
                var tooltipText = element.querySelector('.tooltiptext');
                tooltipText.style.visibility = 'hidden';
                tooltipText.style.opacity = '0';
            }
        });
    }, true);
});