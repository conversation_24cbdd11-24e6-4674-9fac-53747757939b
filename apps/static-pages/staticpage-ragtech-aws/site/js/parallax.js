document.addEventListener("DOMContentLoaded", function() {
  const parallaxElem = document.getElementById("parallax");
  const bgElems = document.querySelectorAll('.bgimg-1, .bgimg-2, .bgimg-3, .bgimg-4');
  
  if (!parallaxElem) {
    console.warn("Parallax element not found. Make sure there's an element with id 'parallax' in your HTML.");
    return;
  }

  let ticking = false;
  const depths = [0.06, 0.04, 0.02, 0];

  function updateParallax(x, y) {
    const _w = window.innerWidth / 2;
    const _h = window.innerHeight / 2;
    
    const positions = depths.map(depth => 
      `${50 - (x - _w) * depth}% ${50 - (y - _h) * depth}%`
    );

    parallaxElem.style.backgroundPosition = positions.join(', ');
  }

  function requestTick(e, touch = false) {
    if (!ticking) {
      requestAnimationFrame(() => {
        const x = touch ? e.touches[0].clientX : e.clientX;
        const y = touch ? e.touches[0].clientY : e.clientY;
        updateParallax(x, y);
        ticking = false;
      });
      ticking = true;
    }
  }

  function handleMouseMove(e) {
    if (window.scrollY < window.innerHeight) {
      requestTick(e);
    }
  }

  function handleTouchMove(e) {
    if (e.touches.length === 1 && window.scrollY < window.innerHeight) {
      e.preventDefault();
      requestTick(e, true);
    }
  }

  function handleScroll() {
    if (window.scrollY >= window.innerHeight) {
      parallaxElem.style.backgroundPosition = 'center center';
    } else {
      updateParallax(window.innerWidth / 2, window.innerHeight / 2);
    }

    bgElems.forEach(elem => {
      const scrollPosition = window.pageYOffset;
      elem.style.backgroundPositionY = -(scrollPosition * 0.5) + 'px';
    });
  }

  // Throttled window resize handler
  let resizeTimeout;
  function handleResize() {
    clearTimeout(resizeTimeout);
    resizeTimeout = setTimeout(() => {
      updateParallax(window.innerWidth / 2, window.innerHeight / 2);
    }, 250);
  }

  // Add event listeners
  document.addEventListener("mousemove", handleMouseMove, { passive: true });
  document.addEventListener("touchmove", handleTouchMove, { passive: false });
  window.addEventListener("scroll", handleScroll);
  window.addEventListener("resize", handleResize);

  // Initial position
  updateParallax(window.innerWidth / 2, window.innerHeight / 2);
});