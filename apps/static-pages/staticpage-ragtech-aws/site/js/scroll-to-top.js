document.addEventListener('DOMContentLoaded', function() {
  console.log('DOM fully loaded');
  
  const scrollTopBtn = document.getElementById('scrollTopBtn');
  
  if (!scrollTopBtn) {
    console.error('Scroll to top button not found!');
    return;
  }
  
  console.log('Scroll to top button found:', scrollTopBtn);

  function updateButtonVisibility() {
    const scrollPosition = window.pageYOffset || document.documentElement.scrollTop;
    console.log('Current scroll position:', scrollPosition);
    
    if (scrollPosition > 300) {
      scrollTopBtn.classList.add('show');
      console.log('Added "show" class. Button classes:', scrollTopBtn.className);
      console.log('Button opacity:', window.getComputedStyle(scrollTopBtn).opacity);
      console.log('Button visibility:', window.getComputedStyle(scrollTopBtn).visibility);
    } else {
      scrollTopBtn.classList.remove('show');
      console.log('Removed "show" class. Button classes:', scrollTopBtn.className);
      console.log('Button opacity:', window.getComputedStyle(scrollTopBtn).opacity);
      console.log('Button visibility:', window.getComputedStyle(scrollTopBtn).visibility);
    }
  }

  window.addEventListener('scroll', function() {
    console.log('Scroll event detected');
    updateButtonVisibility();
  });

  scrollTopBtn.addEventListener('click', function() {
    console.log('Button clicked, scrolling to top');
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  });

  // Verificar el estado inicial del botón
  updateButtonVisibility();

  // Imprimir los estilos actuales del botón
  const buttonStyles = window.getComputedStyle(scrollTopBtn);
  console.log('Initial button styles:', {
    position: buttonStyles.position,
    bottom: buttonStyles.bottom,
    right: buttonStyles.right,
    zIndex: buttonStyles.zIndex,
    opacity: buttonStyles.opacity,
    visibility: buttonStyles.visibility
  });
});