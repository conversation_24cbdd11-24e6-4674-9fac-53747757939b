// Función para obtener el valor de una cookie por su nombre
function getCookie(name) {
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) return parts.pop().split(';').shift();
}

// Función para establecer una cookie
function setCookie(name, value, days) {
    let expires = "";
    if (days) {
        const date = new Date();
        date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
        expires = `; expires=${date.toUTCString()}`;
    }
    document.cookie = `${name}=${value || ""}${expires}; path=/`;
}

// Función para borrar una cookie
function deleteCookie(name) {
    document.cookie = `${name}=; Max-Age=-99999999; path=/`;
}

// Función para guardar las preferencias de cookies
function savePreferences() {
    const analyticsChecked = document.getElementById('analytics').checked;
    const marketingChecked = document.getElementById('marketing').checked;

    if (analyticsChecked) {
        setCookie('analyticsCookies', 'enabled', 365); // Habilita cookies analíticas por un año
    } else {
        deleteCookie('analyticsCookies'); // Deshabilita cookies analíticas
    }

    if (marketingChecked) {
        setCookie('marketingCookies', 'enabled', 365); // Habilita cookies de marketing por un año
    } else {
        deleteCookie('marketingCookies'); // Deshabilita cookies de marketing
    }

    alert('Preferencias de cookies guardadas');
}

// Función para cargar preferencias de cookies al cargar la página
function loadPreferences() {
    const analyticsChecked = getCookie('analyticsCookies') === 'enabled';
    const marketingChecked = getCookie('marketingCookies') === 'enabled';

    document.getElementById('analytics').checked = analyticsChecked;
    document.getElementById('marketing').checked = marketingChecked;
}

// Ejecuta la función al cargar la página
window.onload = function() {
    loadPreferences();

    // Maneja la sumisión del formulario de preferencias
    document.getElementById('cookie-preferences').addEventListener('submit', function(event) {
        event.preventDefault(); // Evita el comportamiento por defecto del formulario
        savePreferences();
    });
}
