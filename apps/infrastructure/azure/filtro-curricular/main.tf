terraform {
  required_version = ">= 1.0"
  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "~> 3.0"
    }
    random = {
      source  = "hashicorp/random"
      version = "~> 3.1"
    }
  }
}

provider "azurerm" {
  # Explicitly disable Azure CLI authentication to force Service Principal authentication
  use_cli = false

  features {
    resource_group {
      prevent_deletion_if_contains_resources = false
    }
    key_vault {
      purge_soft_delete_on_destroy    = true
      recover_soft_deleted_key_vaults = true
    }
  }
}

# Random suffix for unique resource names
resource "random_string" "suffix" {
  length  = 6
  special = false
  upper   = false
}

# Local values for consistent naming that follows Azure naming conventions
locals {
  # Remove hyphens and convert to lowercase for resources that don't allow them
  project_name_clean = replace(lower(var.project_name), "-", "")

  # Shortened project name for resources with length limits
  project_name_short = substr(local.project_name_clean, 0, 8)

  # Common naming patterns
  resource_prefix = "${local.project_name_clean}${var.environment}"
  resource_prefix_short = "${local.project_name_short}${var.environment}"
}

# Resource Group
resource "azurerm_resource_group" "main" {
  name     = "${var.project_name}-rg-${var.environment}"
  location = var.location

  tags = var.tags
}

# Log Analytics Workspace for monitoring (deployed in supported region)
resource "azurerm_log_analytics_workspace" "main" {
  name                = "${var.project_name}-logs-${var.environment}"
  location            = var.monitoring_location
  resource_group_name = azurerm_resource_group.main.name
  sku                 = "PerGB2018"
  retention_in_days   = 30

  tags = merge(var.tags, {
    MonitoringRegion = var.monitoring_location
    Note            = "Deployed in ${var.monitoring_location} due to regional availability"
  })
}

# Application Insights (deployed in same region as Log Analytics)
resource "azurerm_application_insights" "main" {
  name                = "${var.project_name}-insights-${var.environment}"
  location            = var.monitoring_location
  resource_group_name = azurerm_resource_group.main.name
  workspace_id        = azurerm_log_analytics_workspace.main.id
  application_type    = "web"

  tags = merge(var.tags, {
    MonitoringRegion = var.monitoring_location
    Note            = "Deployed in ${var.monitoring_location} due to regional availability"
  })
}

# Key Vault for secrets management
resource "azurerm_key_vault" "main" {
  name                = "${local.project_name_short}-kv-${var.environment}-${random_string.suffix.result}"
  location            = azurerm_resource_group.main.location
  resource_group_name = azurerm_resource_group.main.name
  tenant_id           = data.azurerm_client_config.current.tenant_id
  sku_name            = "standard"

  # Enable soft delete and purge protection for production environments
  soft_delete_retention_days = var.environment == "prod" ? 90 : 7
  purge_protection_enabled   = var.environment == "prod" ? true : false

  # Network access rules for enhanced security
  network_acls {
    default_action = var.environment == "prod" ? "Deny" : "Allow"
    bypass         = "AzureServices"

    # Allow access from App Service subnet in production
    virtual_network_subnet_ids = var.environment == "prod" && var.enable_vnet ? [azurerm_subnet.app_service[0].id] : []
  }

  access_policy {
    tenant_id = data.azurerm_client_config.current.tenant_id
    object_id = data.azurerm_client_config.current.object_id

    secret_permissions = [
      "Get",
      "List",
      "Set",
      "Delete",
      "Recover",
      "Backup",
      "Restore"
    ]
  }

  tags = merge(var.tags, {
    Environment = var.environment
    Purpose     = "secrets-management"
  })
}

# Get current Azure client configuration
data "azurerm_client_config" "current" {}

# Key Vault Secrets
resource "azurerm_key_vault_secret" "openai_api_key" {
  name         = "openai-api-key"
  value        = var.openai_api_key
  key_vault_id = azurerm_key_vault.main.id

  depends_on = [azurerm_key_vault.main]
}

resource "azurerm_key_vault_secret" "openai_token" {
  name         = "openai-token"
  value        = var.openai_token
  key_vault_id = azurerm_key_vault.main.id

  depends_on = [azurerm_key_vault.main]
}

resource "azurerm_key_vault_secret" "assistant_id_juridico" {
  name         = "assistant-id-juridico"
  value        = var.assistant_id_juridico
  key_vault_id = azurerm_key_vault.main.id

  depends_on = [azurerm_key_vault.main]
}

resource "azurerm_key_vault_secret" "assistant_id_calidad" {
  name         = "assistant-id-calidad"
  value        = var.assistant_id_calidad
  key_vault_id = azurerm_key_vault.main.id

  depends_on = [azurerm_key_vault.main]
}

# Key Vault access policy for backend app service
resource "azurerm_key_vault_access_policy" "backend_app" {
  key_vault_id = azurerm_key_vault.main.id
  tenant_id    = data.azurerm_client_config.current.tenant_id
  object_id    = azurerm_linux_web_app.backend.identity[0].principal_id

  secret_permissions = [
    "Get",
    "List"
  ]

  depends_on = [azurerm_linux_web_app.backend]
}

# Key Vault access policy for frontend app service
resource "azurerm_key_vault_access_policy" "frontend_app" {
  key_vault_id = azurerm_key_vault.main.id
  tenant_id    = data.azurerm_client_config.current.tenant_id
  object_id    = azurerm_linux_web_app.frontend.identity[0].principal_id

  secret_permissions = [
    "Get",
    "List"
  ]

  depends_on = [azurerm_linux_web_app.frontend]
}
