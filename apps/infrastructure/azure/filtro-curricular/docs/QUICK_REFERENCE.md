# Quick Reference Guide - Environment Variables & Deployment

## 🚀 Quick Commands

### Deploy to Environment
```bash
# Development
./scripts/deploy-environment.sh dev

# Staging  
./scripts/deploy-environment.sh staging

# Production (requires confirmation)
./scripts/deploy-environment.sh prod

# Plan only (no deployment)
./scripts/deploy-environment.sh dev plan

# Skip secret management
./scripts/deploy-environment.sh dev deploy --skip-secrets
```

### Manage Secrets
```bash
# Interactive secret update
./scripts/manage-secrets.sh update

# Update from file
./scripts/manage-secrets.sh update-from-file secrets/secrets-dev.json

# List all secrets
./scripts/manage-secrets.sh list

# Get secret info
./scripts/manage-secrets.sh info openai-api-key
```

### Environment-Specific Operations
```bash
# Set environment for scripts
export ENVIRONMENT=staging
export RESOURCE_GROUP=filtro-curricular-rg-staging

# Then run any script
./scripts/manage-secrets.sh list
```

## 📁 File Structure

```
apps/infrastructure/azure/filtro-curricular/
├── environments/           # Environment-specific configurations
│   ├── dev.tfvars         # Development settings
│   ├── staging.tfvars     # Staging settings
│   └── prod.tfvars        # Production settings
├── modules/
│   └── app-config/        # App configuration module
│       ├── main.tf        # Main configuration logic
│       └── variables.tf   # Module variables
├── scripts/
│   ├── deploy-environment.sh  # Main deployment script
│   └── manage-secrets.sh      # Secret management script
├── secrets/
│   ├── secrets-template.json  # Template for secrets
│   └── secrets-dev.json      # Dev secrets (create as needed)
└── docs/
    ├── ENVIRONMENT_VARIABLES_GUIDE.md  # Detailed guide
    └── QUICK_REFERENCE.md              # This file
```

## 🔧 Environment Configurations

### Development (dev.tfvars)
- **SKU**: Basic (B1)
- **Scaling**: 1-2 instances
- **Security**: Relaxed
- **Networking**: No VNet
- **Monitoring**: Basic

### Staging (staging.tfvars)
- **SKU**: Standard (S1)
- **Scaling**: 1-3 instances
- **Security**: Enhanced
- **Networking**: VNet enabled
- **Monitoring**: Full

### Production (prod.tfvars)
- **SKU**: Premium (P1v3)
- **Scaling**: 2-10 instances
- **Security**: Maximum
- **Networking**: VNet + App Gateway
- **Monitoring**: Full + Alerts

## 🔑 Secret Management

### Required Secrets
```json
{
  "openai_api_key": "sk-proj-...",
  "openai_token": "sk-...",
  "assistant_id_juridico": "asst_...",
  "assistant_id_calidad": "asst_..."
}
```

### Key Vault Naming Convention
```
filtro-kv-{environment}-{random-suffix}
```

### App Setting References
```
GPT_API_KEY = @Microsoft.KeyVault(VaultName=filtro-kv-dev-abc123;SecretName=openai-api-key)
```

## 🌐 GitHub Actions

### Workflow Triggers
- **Push to main**: Deploys to staging
- **Push to dev**: Deploys to dev
- **Manual dispatch**: Deploy to any environment

### Environment Secrets (GitHub)
Set these in GitHub repository settings > Environments:

**For each environment (dev, staging, prod):**
- `OPENAI_API_KEY`
- `OPENAI_TOKEN`
- `ASSISTANT_ID_JURIDICO`
- `ASSISTANT_ID_CALIDAD`

### Manual Deployment
```bash
# Go to GitHub Actions
# Select "Filtro Curricular - Infrastructure Deployment"
# Click "Run workflow"
# Choose environment and options
```

## 🔍 Troubleshooting

### Check App Service Settings
```bash
az webapp config appsettings list \
  --name filtro-curricular-be-dev \
  --resource-group filtro-curricular-rg-dev \
  --query "[?contains(name, 'GPT') || contains(name, 'OPENAI')]"
```

### Verify Key Vault Access
```bash
# List secrets
az keyvault secret list --vault-name filtro-kv-dev-abc123

# Test secret access
az keyvault secret show --vault-name filtro-kv-dev-abc123 --name openai-api-key
```

### Check App Service Logs
```bash
az webapp log tail \
  --name filtro-curricular-be-dev \
  --resource-group filtro-curricular-rg-dev
```

### Common Issues

1. **"Secret not found"**
   - Check secret exists: `./scripts/manage-secrets.sh list`
   - Verify Key Vault name in app settings

2. **"Access denied"**
   - Check managed identity is enabled
   - Verify Key Vault access policy

3. **"Wrong environment secrets"**
   - Check GitHub environment secrets are set
   - Verify environment name matches

## 📊 Monitoring URLs

### Development
- **Backend**: https://filtro-curricular-be-dev.azurewebsites.net
- **Frontend**: https://filtro-curricular-fe-dev.azurewebsites.net
- **Health**: https://filtro-curricular-be-dev.azurewebsites.net/status

### Staging
- **Backend**: https://filtro-curricular-be-staging.azurewebsites.net
- **Frontend**: https://filtro-curricular-fe-staging.azurewebsites.net

### Production
- **Backend**: https://filtro-curricular-be-prod.azurewebsites.net
- **Frontend**: https://filtro-curricular-fe-prod.azurewebsites.net

## 🎯 Best Practices

### 1. Secret Management
- ✅ Never commit secrets to git
- ✅ Use environment-specific secret files
- ✅ Rotate secrets regularly
- ✅ Use managed identity for access

### 2. Deployment
- ✅ Always test in dev first
- ✅ Use staging for final validation
- ✅ Require approval for production
- ✅ Monitor after deployment

### 3. Environment Isolation
- ✅ Separate Key Vaults per environment
- ✅ Different access policies
- ✅ Environment-specific configurations
- ✅ Isolated resource groups

### 4. Monitoring
- ✅ Check health endpoints after deployment
- ✅ Monitor application logs
- ✅ Set up alerts for production
- ✅ Regular security reviews

## 🆘 Emergency Procedures

### Rollback Deployment
```bash
# Redeploy previous version
./scripts/deploy-environment.sh prod deploy --force

# Or use GitHub Actions to redeploy previous commit
```

### Emergency Secret Rotation
```bash
# Update secrets immediately
export ENVIRONMENT=prod
./scripts/manage-secrets.sh update

# Restart app services to pick up new secrets
az webapp restart --name filtro-curricular-be-prod --resource-group filtro-curricular-rg-prod
```

### Contact Information
- **Infrastructure Team**: [Your team contact]
- **On-call**: [Emergency contact]
- **Documentation**: [Link to full docs]
