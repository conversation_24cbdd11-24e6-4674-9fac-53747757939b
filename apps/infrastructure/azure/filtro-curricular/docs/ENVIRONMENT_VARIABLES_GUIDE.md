# Environment Variables Management Guide

This guide explains the best practices for managing environment variables from Azure Key Vault when deploying to Azure Web App Service, with scalable solutions for multiple environments.

## 🏗️ Current Architecture

Your current setup includes:
- ✅ Azure Key Vault integration with App Service
- ✅ System-assigned managed identity for App Services
- ✅ Key Vault references in app settings using `@Microsoft.KeyVault()` syntax
- ✅ Environment-specific deployments (dev, staging, uat, prod)
- ✅ Scripts for managing secrets

## 🚀 Scalable Options for Environment Variable Management

### **Option 1: Environment-Specific Key Vaults (Recommended)**

**Best for:** Production environments requiring complete isolation and security

**Benefits:**
- Complete isolation between environments
- Environment-specific access controls
- Easier compliance and auditing
- No risk of cross-environment secret leakage
- Granular security policies per environment

**Implementation:**
```bash
# Each environment gets its own Key Vault
filtro-kv-dev-xxxxxx      # Development
filtro-kv-staging-xxxxxx  # Staging
filtro-kv-prod-xxxxxx     # Production
```

**How it works:**
1. Each environment has its own Key Vault
2. App Services use managed identity to access their environment's Key Vault
3. Secrets are environment-specific and isolated
4. Different access policies and security levels per environment

**Usage:**
```bash
# Deploy to specific environment
./scripts/deploy-environment.sh dev
./scripts/deploy-environment.sh staging
./scripts/deploy-environment.sh prod
```

### **Option 2: Single Key Vault with Environment Prefixes**

**Best for:** Smaller teams or cost-conscious deployments

**Benefits:**
- Single Key Vault to manage
- Lower cost (fewer Key Vault instances)
- Centralized secret management
- Easier for small teams

**Implementation:**
```bash
# Secrets with environment prefixes
dev-openai-api-key
staging-openai-api-key
prod-openai-api-key
```

**How it works:**
1. One Key Vault contains all environment secrets
2. Secrets are prefixed with environment name
3. App Services access environment-specific secrets
4. Access policies control which apps can access which secrets

### **Option 3: Hybrid Approach with Shared and Environment-Specific Secrets**

**Best for:** Complex applications with both shared and environment-specific configurations

**Benefits:**
- Shared secrets for common resources
- Environment-specific secrets for sensitive data
- Flexible and scalable
- Cost-effective

**Implementation:**
```bash
# Shared Key Vault for common secrets
filtro-kv-shared-xxxxxx

# Environment-specific Key Vaults for sensitive secrets
filtro-kv-dev-xxxxxx
filtro-kv-prod-xxxxxx
```

## 📋 Implementation Details

### Current Implementation (Option 1)

Your infrastructure already implements Option 1 with these components:

#### 1. Environment-Specific Configuration Files
```
environments/
├── dev.tfvars      # Development configuration
├── staging.tfvars  # Staging configuration
└── prod.tfvars     # Production configuration
```

#### 2. App Configuration Module
The `modules/app-config` module handles:
- Environment-specific app settings
- Key Vault references
- Predefined secret configurations
- Custom app settings per application

#### 3. Automated Secret Management
```bash
# Interactive secret management
./scripts/manage-secrets.sh update

# Environment-specific deployment
./scripts/deploy-environment.sh dev
./scripts/deploy-environment.sh prod
```

### Key Vault References in App Settings

Your App Services automatically resolve secrets using this syntax:
```terraform
app_settings = {
  GPT_API_KEY = "@Microsoft.KeyVault(VaultName=${key_vault_name};SecretName=openai-api-key)"
  OPENAI_TOKEN = "@Microsoft.KeyVault(VaultName=${key_vault_name};SecretName=openai-token)"
}
```

### Environment-Specific Configurations

Each environment has different settings:

**Development:**
- Basic SKUs for cost optimization
- Debug logging enabled
- Relaxed security settings
- Local development support

**Staging:**
- Standard SKUs for testing
- Production-like configuration
- Enhanced monitoring
- VNet integration

**Production:**
- Premium SKUs for performance
- Maximum security settings
- Full monitoring and alerting
- Network restrictions

## 🔧 Usage Examples

### Deploy to Development
```bash
# Using the deployment script
./scripts/deploy-environment.sh dev

# Or manually with Terraform
terraform plan -var-file="environments/dev.tfvars"
terraform apply -var-file="environments/dev.tfvars"
```

### Manage Secrets for Specific Environment
```bash
# Set environment and deploy
export ENVIRONMENT=staging
./scripts/manage-secrets.sh update

# Or use environment-specific secrets file
./scripts/manage-secrets.sh update-from-file secrets/secrets-staging.json
```

### GitHub Actions Integration

Your workflows automatically:
1. Determine the target environment based on branch/input
2. Use environment-specific configurations
3. Inject secrets from GitHub environment secrets
4. Deploy with proper isolation

## 🔒 Security Best Practices

### 1. Managed Identity
- ✅ System-assigned managed identity for App Services
- ✅ No stored credentials in application code
- ✅ Automatic credential rotation

### 2. Access Policies
- ✅ Least privilege access (Get, List only for apps)
- ✅ Environment-specific access controls
- ✅ Separate policies for deployment vs runtime

### 3. Network Security
- ✅ VNet integration for production
- ✅ Key Vault network restrictions
- ✅ Private endpoints for sensitive environments

### 4. Secret Management
- ✅ Secrets never stored in code or logs
- ✅ Environment-specific secret isolation
- ✅ Automated secret rotation capabilities

## 📊 Monitoring and Troubleshooting

### Verify Key Vault Integration
```bash
# Check if secrets exist
az keyvault secret list --vault-name filtro-kv-dev-xxxxxx

# Verify app service can access secrets
az webapp config appsettings list \
  --name filtro-curricular-be-dev \
  --resource-group filtro-curricular-rg-dev \
  --query "[?contains(name, 'GPT') || contains(name, 'OPENAI')]"
```

### Common Issues and Solutions

1. **Secret not found**: Verify secret exists in correct Key Vault
2. **Access denied**: Check managed identity has proper access policy
3. **Wrong environment**: Ensure app is pointing to correct Key Vault
4. **Network issues**: Verify VNet configuration and firewall rules

## 🎯 Recommendations

**For your current setup, I recommend continuing with Option 1** because:

1. ✅ **Already implemented** - Your infrastructure uses this pattern
2. ✅ **Production-ready** - Provides maximum security and isolation
3. ✅ **Scalable** - Easy to add new environments
4. ✅ **Compliant** - Meets enterprise security requirements
5. ✅ **Maintainable** - Clear separation of concerns

**Next steps:**
1. Create environment-specific secret files for staging and production
2. Set up GitHub environment secrets for each environment
3. Test the deployment pipeline with the new configuration
4. Document the secret management process for your team

This approach gives you the best balance of security, scalability, and maintainability for your multi-environment deployment strategy.
