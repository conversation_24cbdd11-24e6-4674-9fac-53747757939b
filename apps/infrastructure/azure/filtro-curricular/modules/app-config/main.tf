# App Configuration Module
# This module manages environment variables and Key Vault references for App Services

terraform {
  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "~> 3.0"
    }
  }
}

# Local values for environment-specific configurations
locals {
  # Base environment variables that are common across all environments
  base_app_settings = {
    # Docker settings
    WEBSITES_ENABLE_APP_SERVICE_STORAGE = "false"
    WEBSITES_PORT                       = var.app_port
    
    # Application Insights
    APPINSIGHTS_INSTRUMENTATIONKEY        = var.app_insights_key
    APPLICATIONINSIGHTS_CONNECTION_STRING = var.app_insights_connection_string
    
    # Environment identification
    ENVIRONMENT = var.environment
    APP_NAME    = var.app_name
  }

  # Environment-specific configurations
  environment_configs = {
    dev = {
      LOG_LEVEL           = "DEBUG"
      ENABLE_DEBUG_MODE   = "true"
      CACHE_TTL_SECONDS   = "300"
      MAX_RETRY_ATTEMPTS  = "3"
      REQUEST_TIMEOUT     = "30"
    }
    staging = {
      LOG_LEVEL           = "INFO"
      ENABLE_DEBUG_MODE   = "false"
      CACHE_TTL_SECONDS   = "600"
      MAX_RETRY_ATTEMPTS  = "5"
      REQUEST_TIMEOUT     = "60"
    }
    prod = {
      LOG_LEVEL           = "WARNING"
      ENABLE_DEBUG_MODE   = "false"
      CACHE_TTL_SECONDS   = "1800"
      MAX_RETRY_ATTEMPTS  = "5"
      REQUEST_TIMEOUT     = "120"
    }
  }

  # Merge predefined secrets configurations
  openai_secrets = var.enable_openai_secrets ? {
    "openai-api-key" = {
      app_setting_name = "GPT_API_KEY"
      description      = "OpenAI API Key for GPT models"
      required         = true
    }
    "openai-token" = {
      app_setting_name = "OPENAI_TOKEN"
      description      = "OpenAI Token for assistant API"
      required         = true
    }
    "assistant-id-juridico" = {
      app_setting_name = "ASSISTANT_ID_JURIDICO"
      description      = "OpenAI Assistant ID for Juridico domain"
      required         = true
    }
    "assistant-id-calidad" = {
      app_setting_name = "ASSISTANT_ID_CALIDAD"
      description      = "OpenAI Assistant ID for Calidad domain"
      required         = true
    }
  } : {}

  storage_secrets = var.enable_storage_secrets ? {
    "storage-connection-string" = {
      app_setting_name = "STORAGE_CONNECTION_STRING"
      description      = "Azure Storage connection string"
      required         = true
    }
    "storage-account-key" = {
      app_setting_name = "STORAGE_ACCOUNT_KEY"
      description      = "Azure Storage account key"
      required         = true
    }
  } : {}

  # Merge all secret configurations
  merged_secrets_config = merge(
    local.openai_secrets,
    local.storage_secrets,
    var.secrets_config
  )

  # Key Vault references for secrets
  keyvault_references = {
    for secret_name, config in local.merged_secrets_config :
    config.app_setting_name => "@Microsoft.KeyVault(VaultName=${var.key_vault_name};SecretName=${secret_name})"
  }

  # Merge all configurations
  final_app_settings = merge(
    local.base_app_settings,
    local.environment_configs[var.environment],
    local.keyvault_references,
    var.custom_app_settings
  )
}

# Output the final app settings
output "app_settings" {
  description = "Complete app settings configuration for the App Service"
  value       = local.final_app_settings
  sensitive   = true
}

# Output environment-specific settings for debugging
output "environment_config" {
  description = "Environment-specific configuration values"
  value       = local.environment_configs[var.environment]
}

# Output Key Vault references for verification
output "keyvault_references" {
  description = "Key Vault references being used"
  value       = local.keyvault_references
  sensitive   = true
}
