# Variables for App Configuration Module

variable "environment" {
  description = "Environment name (dev, staging, prod)"
  type        = string
  validation {
    condition     = contains(["dev", "staging", "uat", "prod"], var.environment)
    error_message = "Environment must be one of: dev, staging, uat, prod."
  }
}

variable "app_name" {
  description = "Name of the application"
  type        = string
}

variable "app_port" {
  description = "Port the application runs on"
  type        = number
  default     = 5000
}

variable "key_vault_name" {
  description = "Name of the Azure Key Vault"
  type        = string
}

variable "app_insights_key" {
  description = "Application Insights instrumentation key"
  type        = string
}

variable "app_insights_connection_string" {
  description = "Application Insights connection string"
  type        = string
}

variable "secrets_config" {
  description = "Configuration for secrets stored in Key Vault"
  type = map(object({
    app_setting_name = string
    description      = string
    required         = bool
  }))
  default = {}
}

variable "custom_app_settings" {
  description = "Custom application settings specific to this app"
  type        = map(string)
  default     = {}
}

# Predefined secrets configurations for common scenarios
variable "enable_openai_secrets" {
  description = "Enable OpenAI-related secrets"
  type        = bool
  default     = false
}

variable "enable_database_secrets" {
  description = "Enable database-related secrets"
  type        = bool
  default     = false
}

variable "enable_storage_secrets" {
  description = "Enable storage-related secrets"
  type        = bool
  default     = false
}
