# Secrets to set


## Service Principal created successfully

## Save this output securely (you'll need it for GitHub secrets):

{
  "clientId": "fa81931f-6772-4881-9736-235161dca6af",
  "clientSecret": "****************************************",
  "subscriptionId": "2ee03089-129c-4624-97c9-990d968e6141",
  "tenantId": "bd5360f9-4c71-4f42-8309-9de054b4b337",
  "activeDirectoryEndpointUrl": "https://login.microsoftonline.com",
  "resourceManagerEndpointUrl": "https://management.azure.com/",
  "activeDirectoryGraphResourceId": "https://graph.windows.net/",
  "sqlManagementEndpointUrl": "https://management.core.windows.net:8443/",
  "galleryEndpointUrl": "https://gallery.azure.com/",
  "managementEndpointUrl": "https://management.core.windows.net/"
}



Individual values:
Client ID: fa81931f-6772-4881-9736-235161dca6af
Tenant ID: bd5360f9-4c71-4f42-8309-9de054b4b337
Client Secret: [HIDDEN - see full output above]

> echo $CLIENT_SECRET
****************************************

> az ad sp list --display-name "$SP_NAME" --output table
DisplayName                          Id                                    AppId                                 CreatedDateTime
-----------------------------------  ------------------------------------  ------------------------------------  --------------------
sp-filtro-curricular-github-actions  b7a2e0d8-b1c8-40b7-9254-032ba13e22f8  fa81931f-6772-4881-9736-235161dca6af  2025-06-07T17:08:12Z
 ~ ...................................................................................................................... took 4s  at 13:16:53
> SP_OBJECT_ID=$(az ad sp list --display-name "$SP_NAME" --query "[0].id" --output tsv)
echo "Service Principal Object ID: $SP_OBJECT_ID"
Service Principal Object ID: b7a2e0d8-b1c8-40b7-9254-032ba13e22f8


> # Set variables (use the same from previous steps)
SP_NAME="sp-filtro-curricular-github-actions"
SUBSCRIPTION_ID=$(az account show --query id --output tsv)

# Get Service Principal Object ID
SP_OBJECT_ID=$(az ad sp list --display-name "$SP_NAME" --query "[0].id" --output tsv)
echo "Service Principal Object ID: $SP_OBJECT_ID"
Service Principal Object ID: b7a2e0d8-b1c8-40b7-9254-032ba13e22f8



> # Assign Contributor role
echo "Assigning Contributor role..."
az role assignment create \
  --assignee "$SP_OBJECT_ID" \
  --role "Contributor" \
  --scope "/subscriptions/$SUBSCRIPTION_ID"

# Assign Key Vault Administrator role
echo "Assigning Key Vault Administrator role..."
az role assignment create \
  --assignee "$SP_OBJECT_ID" \
  --role "Key Vault Administrator" \
  --scope "/subscriptions/$SUBSCRIPTION_ID"

# Assign User Access Administrator role
echo "Assigning User Access Administrator role..."
az role assignment create \
  --assignee "$SP_OBJECT_ID" \
  --role "User Access Administrator" \
  --scope "/subscriptions/$SUBSCRIPTION_ID"

echo "All role assignments completed successfully "

Assigning Contributor role...
{
  "condition": null,
  "conditionVersion": null,
  "createdBy": "d963b873-bdf5-4a08-8e1e-60df365df4c1",
  "createdOn": "2025-06-07T17:08:17.502701+00:00",
  "delegatedManagedIdentityResourceId": null,
  "description": null,
  "id": "/subscriptions/2ee03089-129c-4624-97c9-990d968e6141/providers/Microsoft.Authorization/roleAssignments/2f612b32-996a-4711-acb1-892f5d208d65",
  "name": "2f612b32-996a-4711-acb1-892f5d208d65",
  "principalId": "b7a2e0d8-b1c8-40b7-9254-032ba13e22f8",
  "principalName": "fa81931f-6772-4881-9736-235161dca6af",
  "principalType": "ServicePrincipal",
  "roleDefinitionId": "/subscriptions/2ee03089-129c-4624-97c9-990d968e6141/providers/Microsoft.Authorization/roleDefinitions/b24988ac-6180-42a0-ab88-20f7382dd24c",
  "roleDefinitionName": "Contributor",
  "scope": "/subscriptions/2ee03089-129c-4624-97c9-990d968e6141",
  "type": "Microsoft.Authorization/roleAssignments",
  "updatedBy": "d963b873-bdf5-4a08-8e1e-60df365df4c1",
  "updatedOn": "2025-06-07T17:08:17.502701+00:00"
}
Assigning Key Vault Administrator role...
{
  "condition": null,
  "conditionVersion": null,
  "createdBy": null,
  "createdOn": "2025-06-07T17:24:06.739150+00:00",
  "delegatedManagedIdentityResourceId": null,
  "description": null,
  "id": "/subscriptions/2ee03089-129c-4624-97c9-990d968e6141/providers/Microsoft.Authorization/roleAssignments/025cef70-65bd-496e-b5a5-c4afee3946eb",
  "name": "025cef70-65bd-496e-b5a5-c4afee3946eb",
  "principalId": "b7a2e0d8-b1c8-40b7-9254-032ba13e22f8",
  "principalType": "ServicePrincipal",
  "roleDefinitionId": "/subscriptions/2ee03089-129c-4624-97c9-990d968e6141/providers/Microsoft.Authorization/roleDefinitions/00482a5a-887f-4fb3-b363-3b7fe8e74483",
  "scope": "/subscriptions/2ee03089-129c-4624-97c9-990d968e6141",
  "type": "Microsoft.Authorization/roleAssignments",
  "updatedBy": "d963b873-bdf5-4a08-8e1e-60df365df4c1",
  "updatedOn": "2025-06-07T17:24:09.601642+00:00"
}
Assigning User Access Administrator role...
{
  "condition": null,
  "conditionVersion": null,
  "createdBy": null,
  "createdOn": "2025-06-07T17:24:19.408660+00:00",
  "delegatedManagedIdentityResourceId": null,
  "description": null,
  "id": "/subscriptions/2ee03089-129c-4624-97c9-990d968e6141/providers/Microsoft.Authorization/roleAssignments/99588c07-1e01-4742-bd42-0c518a18d0e0",
  "name": "99588c07-1e01-4742-bd42-0c518a18d0e0",
  "principalId": "b7a2e0d8-b1c8-40b7-9254-032ba13e22f8",
  "principalType": "ServicePrincipal",
  "roleDefinitionId": "/subscriptions/2ee03089-129c-4624-97c9-990d968e6141/providers/Microsoft.Authorization/roleDefinitions/18d7d88d-d35e-4fb5-a5c3-7773c20a72d9",
  "scope": "/subscriptions/2ee03089-129c-4624-97c9-990d968e6141",
  "type": "Microsoft.Authorization/roleAssignments",
  "updatedBy": "d963b873-bdf5-4a08-8e1e-60df365df4c1",
  "updatedOn": "2025-06-07T17:24:20.557853+00:00"
}

> echo "Verifying role assignments for: $SP_NAME"
az role assignment list \
  --assignee "$SP_OBJECT_ID" \
  --scope "/subscriptions/$SUBSCRIPTION_ID" \
  --output table
Verifying role assignments for: sp-filtro-curricular-github-actions
Principal                             Role                       Scope
------------------------------------  -------------------------  ---------------------------------------------------
fa81931f-6772-4881-9736-235161dca6af  Contributor                /subscriptions/2ee03089-129c-4624-97c9-990d968e6141
fa81931f-6772-4881-9736-235161dca6af  Key Vault Administrator    /subscriptions/2ee03089-129c-4624-97c9-990d968e6141
fa81931f-6772-4881-9736-235161dca6af  User Access Administrator  /subscriptions/2ee03089-129c-4624-97c9-990d968e6141


> echo "Creating resource group: $RESOURCE_GROUP_NAME"
az group create \
  --name "$RESOURCE_GROUP_NAME" \
  --location "$LOCATION"
Creating resource group: filtro-curricular-terraform-state-rg
{
  "id": "/subscriptions/2ee03089-129c-4624-97c9-990d968e6141/resourceGroups/filtro-curricular-terraform-state-rg",
  "location": "chilecentral",
  "managedBy": null,
  "name": "filtro-curricular-terraform-state-rg",
  "properties": {
    "provisioningState": "Succeeded"
  },
  "tags": null,
  "type": "Microsoft.Resources/resourceGroups"
}

> echo "Creating storage account: $STORAGE_ACCOUNT_NAME"
az storage account create \
  --name "$STORAGE_ACCOUNT_NAME" \
  --resource-group "$RESOURCE_GROUP_NAME" \
  --location "$LOCATION" \
  --sku "Standard_LRS" \
  --kind "StorageV2" \
  --access-tier "Hot"
Creating storage account: filtrocurriculartfstate
/opt/az/lib/python3.12/site-packages/azure/multiapi/storagev2/fileshare/__init__.py:1: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  __import__('pkg_resources').declare_namespace(__name__)
{
  "accessTier": "Hot",
  "accountMigrationInProgress": null,
  "allowBlobPublicAccess": false,
  "allowCrossTenantReplication": false,
  "allowSharedKeyAccess": null,
  "allowedCopyScope": null,
  "azureFilesIdentityBasedAuthentication": null,
  "blobRestoreStatus": null,
  "creationTime": "2025-06-07T19:00:57.102487+00:00",
  "customDomain": null,
  "defaultToOAuthAuthentication": null,
  "dnsEndpointType": null,
  "enableExtendedGroups": null,
  "enableHttpsTrafficOnly": true,
  "enableNfsV3": null,
  "encryption": {
    "encryptionIdentity": null,
    "keySource": "Microsoft.Storage",
    "keyVaultProperties": null,
    "requireInfrastructureEncryption": null,
    "services": {
      "blob": {
        "enabled": true,
        "keyType": "Account",
        "lastEnabledTime": "2025-06-07T19:00:57.149363+00:00"
      },
      "file": {
        "enabled": true,
        "keyType": "Account",
        "lastEnabledTime": "2025-06-07T19:00:57.149363+00:00"
      },
      "queue": null,
      "table": null
    }
  },
  "extendedLocation": null,
  "failoverInProgress": null,
  "geoReplicationStats": null,
  "id": "/subscriptions/2ee03089-129c-4624-97c9-990d968e6141/resourceGroups/filtro-curricular-terraform-state-rg/providers/Microsoft.Storage/storageAccounts/filtrocurriculartfstate",
  "identity": null,
  "immutableStorageWithVersioning": null,
  "isHnsEnabled": null,
  "isLocalUserEnabled": null,
  "isSftpEnabled": null,
  "isSkuConversionBlocked": null,
  "keyCreationTime": {
    "key1": "2025-06-07T19:00:57.133737+00:00",
    "key2": "2025-06-07T19:00:57.133737+00:00"
  },
  "keyPolicy": null,
  "kind": "StorageV2",
  "largeFileSharesState": null,
  "lastGeoFailoverTime": null,
  "location": "chilecentral",
  "minimumTlsVersion": "TLS1_0",
  "name": "filtrocurriculartfstate",
  "networkRuleSet": {
    "bypass": "AzureServices",
    "defaultAction": "Allow",
    "ipRules": [],
    "ipv6Rules": [],
    "resourceAccessRules": null,
    "virtualNetworkRules": []
  },
  "primaryEndpoints": {
    "blob": "https://filtrocurriculartfstate.blob.core.windows.net/",
    "dfs": "https://filtrocurriculartfstate.dfs.core.windows.net/",
    "file": "https://filtrocurriculartfstate.file.core.windows.net/",
    "internetEndpoints": null,
    "microsoftEndpoints": null,
    "queue": "https://filtrocurriculartfstate.queue.core.windows.net/",
    "table": "https://filtrocurriculartfstate.table.core.windows.net/",
    "web": "https://filtrocurriculartfstate.z47.web.core.windows.net/"
  },
  "primaryLocation": "chilecentral",
  "privateEndpointConnections": [],
  "provisioningState": "Succeeded",
  "publicNetworkAccess": null,
  "resourceGroup": "filtro-curricular-terraform-state-rg",
  "routingPreference": null,
  "sasPolicy": null,
  "secondaryEndpoints": null,
  "secondaryLocation": null,
  "sku": {
    "name": "Standard_LRS",
    "tier": "Standard"
  },
  "statusOfPrimary": "available",
  "statusOfSecondary": null,
  "storageAccountSkuConversionStatus": null,
  "tags": {},
  "type": "Microsoft.Storage/storageAccounts"
}


> # Get storage account key
STORAGE_KEY=$(az storage account keys list \
  --resource-group "$RESOURCE_GROUP_NAME" \
  --account-name "$STORAGE_ACCOUNT_NAME" \
  --query "[0].value" \
  --output tsv)
/opt/az/lib/python3.12/site-packages/azure/multiapi/storagev2/fileshare/__init__.py:1: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  __import__('pkg_resources').declare_namespace(__name__)
 ~ ...................................................................................................................... took 7s  at 15:04:26
> echo $STORAGE_KEY
****************************************************************************************


echo "Creating container: $CONTAINER_NAME"
az storage container create \
  --name "$CONTAINER_NAME" \
  --account-name "$STORAGE_ACCOUNT_NAME" \
  --account-key "$STORAGE_KEY" \
  --public-access off
Creating container: terraform-state
/opt/az/lib/python3.12/site-packages/azure/multiapi/storagev2/fileshare/__init__.py:1: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  __import__('pkg_resources').declare_namespace(__name__)
{
  "created": true
}

 ~ ............................................................................................................................... at 15:05:01
> echo ""
echo "✅ Terraform state backend created successfully!"
echo "Storage Account Name: $STORAGE_ACCOUNT_NAME"
echo "Container Name: $CONTAINER_NAME"
echo "Resource Group: $RESOURCE_GROUP_NAME"
dquote> "

✅ Terraform state backend created successfully
echo Storage Account Name: filtrocurriculartfstate
echo Container Name: terraform-state
echo Resource Group: filtro-curricular-terraform-state-rg


> # Get Service Principal Object ID (from previous steps)
SP_NAME="sp-filtro-curricular-github-actions"
SP_OBJECT_ID=$(az ad sp list --display-name "$SP_NAME" --query "[0].id" --output tsv)

# Assign Storage Blob Data Contributor role to Service Principal
echo "Assigning Storage Blob Data Contributor role to Service Principal..."
az role assignment create \
  --assignee "$SP_OBJECT_ID" \
  --role "Storage Blob Data Contributor" \
  --scope "/subscriptions/$(az account show --query id --output tsv)/resourceGroups/$RESOURCE_GROUP_NAME/providers/Microsoft.Storage/storageAccounts/$STORAGE_ACCOUNT_NAME"
Assigning Storage Blob Data Contributor role to Service Principal...
{
  "condition": null,
  "conditionVersion": null,
  "createdBy": null,
  "createdOn": "2025-06-07T19:06:44.841389+00:00",
  "delegatedManagedIdentityResourceId": null,
  "description": null,
  "id": "/subscriptions/2ee03089-129c-4624-97c9-990d968e6141/resourceGroups/filtro-curricular-terraform-state-rg/providers/Microsoft.Storage/storageAccounts/filtrocurriculartfstate/providers/Microsoft.Authorization/roleAssignments/********-119c-4929-9f9e-b6dcdfb1bfa0",
  "name": "********-119c-4929-9f9e-b6dcdfb1bfa0",
  "principalId": "b7a2e0d8-b1c8-40b7-9254-032ba13e22f8",
  "principalType": "ServicePrincipal",
  "resourceGroup": "filtro-curricular-terraform-state-rg",
  "roleDefinitionId": "/subscriptions/2ee03089-129c-4624-97c9-990d968e6141/providers/Microsoft.Authorization/roleDefinitions/ba92f5b4-2d11-453d-a403-e96b0029c9fe",
  "scope": "/subscriptions/2ee03089-129c-4624-97c9-990d968e6141/resourceGroups/filtro-curricular-terraform-state-rg/providers/Microsoft.Storage/storageAccounts/filtrocurriculartfstate",
  "type": "Microsoft.Authorization/roleAssignments",
  "updatedBy": "d963b873-bdf5-4a08-8e1e-60df365df4c1",
  "updatedOn": "2025-06-07T19:06:46.011446+00:00"
}


> # Verify the role assignment
echo "Verifying storage account permissions..."
az role assignment list \
  --assignee "$SP_OBJECT_ID" \
  --scope "/subscriptions/$(az account show --query id --output tsv)/resourceGroups/$RESOURCE_GROUP_NAME/providers/Microsoft.Storage/storageAccounts/$STORAGE_ACCOUNT_NAME" \
  --output table

echo ""
echo "✅ Storage account permissions configured successfully!"
echo ""
echo "📝 Save these values for your backend.tf configuration:"
echo "Resource Group: $RESOURCE_GROUP_NAME"
echo "Storage Account: $STORAGE_ACCOUNT_NAME"
echo "Container: $CONTAINER_NAME"
dquote> "
Verifying storage account permissions...
Principal                             Role                           Scope
------------------------------------  -----------------------------  ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------
fa81931f-6772-4881-9736-235161dca6af  Storage Blob Data Contributor  /subscriptions/2ee03089-129c-4624-97c9-990d968e6141/resourceGroups/filtro-curricular-terraform-state-rg/providers/Microsoft.Storage/storageAccounts/filtrocurriculartfstate

✅ Storage account permissions configured successfully
echo
echo 📝 Save these values for your backend.tf configuration:
echo Resource Group: filtro-curricular-terraform-state-rg
echo Storage Account: filtrocurriculartfstate
echo Container: terraform-state

> terraform init
Initializing the backend...

Successfully configured the backend "azurerm"! Terraform will automatically
use this backend unless the backend configuration changes.
Initializing provider plugins...
- Finding hashicorp/azurerm versions matching "~> 3.0"...
- Finding hashicorp/random versions matching "~> 3.1"...
- Installing hashicorp/azurerm v3.117.1...
- Installed hashicorp/azurerm v3.117.1 (signed by HashiCorp)
- Installing hashicorp/random v3.7.2...
- Installed hashicorp/random v3.7.2 (signed by HashiCorp)
Terraform has created a lock file .terraform.lock.hcl to record the provider
selections it made above. Include this file in your version control repository
so that Terraform can guarantee to make the same selections by default when
you run "terraform init" in the future.

Terraform has been successfully initialized!

You may now begin working with Terraform. Try running "terraform plan" to see
any changes that are required for your infrastructure. All Terraform commands
should now work.

If you ever set or change modules or backend configuration for Terraform,
rerun this command to reinitialize your working directory. If you forget, other
commands will detect it and remind you to do so if necessary.
 ~/dev/github/jherrera-rgt/ragtech/apps/infrastructure/azure/filtro-curricular  on feature/filtro-curricular-azure ?2 .............. took 55s  at 16:24:57 
> az login --service-principal \
  --username "fa81931f-6772-4881-9736-235161dca6af" \
  --password "****************************************" \
  --tenant "bd5360f9-4c71-4f42-8309-9de054b4b337"
[
  {
    "cloudName": "AzureCloud",
    "homeTenantId": "bd5360f9-4c71-4f42-8309-9de054b4b337",
    "id": "2ee03089-129c-4624-97c9-990d968e6141",
    "isDefault": true,
    "managedByTenants": [],
    "name": "Patrocinio de Microsoft Azure",
    "state": "Enabled",
    "tenantId": "bd5360f9-4c71-4f42-8309-9de054b4b337",
    "user": {
      "name": "fa81931f-6772-4881-9736-235161dca6af",
      "type": "servicePrincipal"
    }
  }
]
 ~/dev/github/jherrera-rgt/ragtech/apps/infrastructure/azure/filtro-curricular  on feature/filtro-curricular-azure ?2 ............... took 3s  at 16:26:23 
> az account show --output table
EnvironmentName    HomeTenantId                          IsDefault    Name                           State    TenantId
-----------------  ------------------------------------  -----------  -----------------------------  -------  ------------------------------------
AzureCloud         bd5360f9-4c71-4f42-8309-9de054b4b337  True         Patrocinio de Microsoft Azure  Enabled  bd5360f9-4c71-4f42-8309-9de054b4b337
 ~/dev/github/jherrera-rgt/ragtech/apps/infrastructure/azure/filtro-curricular  on feature/filtro-curricular-azure ?2 ........................ at 16:26:41 
> az account list
[
  {
    "cloudName": "AzureCloud",
    "homeTenantId": "bd5360f9-4c71-4f42-8309-9de054b4b337",
    "id": "2ee03089-129c-4624-97c9-990d968e6141",
    "isDefault": true,
    "managedByTenants": [],
    "name": "Patrocinio de Microsoft Azure",
    "state": "Enabled",
    "tenantId": "bd5360f9-4c71-4f42-8309-9de054b4b337",
    "user": {
      "name": "fa81931f-6772-4881-9736-235161dca6af",
      "type": "servicePrincipal"
    }
  }
]
 ~/dev/github/jherrera-rgt/ragtech/apps/infrastructure/azure/filtro-curricular  on feature/filtro-curricular-azure ?2 ........................ at 16:26:49 
> az account list --output table
Name                           CloudName    SubscriptionId                        TenantId                              State    IsDefault
-----------------------------  -----------  ------------------------------------  ------------------------------------  -------  -----------
Patrocinio de Microsoft Azure  AzureCloud   2ee03089-129c-4624-97c9-990d968e6141  bd5360f9-4c71-4f42-8309-9de054b4b337  Enabled  True
 ~/dev/github/jherrera-rgt/ragtech/apps/infrastructure/azure/filtro-curricular  on feature/filtro-curricular-azure ?2 ........................ at 16:26:53 
> az group create --name "test-sp-permissions" --location "Chile Central"
{
  "id": "/subscriptions/2ee03089-129c-4624-97c9-990d968e6141/resourceGroups/test-sp-permissions",
  "location": "chilecentral",
  "managedBy": null,
  "name": "test-sp-permissions",
  "properties": {
    "provisioningState": "Succeeded"
  },
  "tags": null,
  "type": "Microsoft.Resources/resourceGroups"
}