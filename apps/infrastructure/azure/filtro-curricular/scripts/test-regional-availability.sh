#!/bin/bash

# Test script to validate Azure regional availability solution
# This script helps verify that the monitoring region strategy is correctly configured

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Print functions
print_header() {
    echo -e "\n${BLUE}=== $1 ===${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Test monitoring location variable
test_monitoring_variable() {
    print_header "Testing Monitoring Location Variable"
    
    if grep -q "monitoring_location" variables.tf; then
        print_success "monitoring_location variable found in variables.tf"
        
        # Extract default value
        DEFAULT_MONITORING_LOCATION=$(grep -A 5 'variable "monitoring_location"' variables.tf | grep 'default' | sed 's/.*= *"\([^"]*\)".*/\1/')
        if [ -n "$DEFAULT_MONITORING_LOCATION" ]; then
            print_success "Default monitoring location: $DEFAULT_MONITORING_LOCATION"
        else
            print_error "No default monitoring location found"
        fi
        
        # Check validation
        if grep -A 20 'variable "monitoring_location"' variables.tf | grep -q "validation"; then
            print_success "Monitoring location validation found"
        else
            print_warning "No validation found for monitoring_location variable"
        fi
    else
        print_error "monitoring_location variable not found in variables.tf"
    fi
}

# Test terraform.tfvars configuration
test_tfvars_config() {
    print_header "Testing terraform.tfvars Configuration"
    
    if [ -f "terraform.tfvars" ]; then
        print_success "terraform.tfvars file found"
        
        # Check for monitoring_location
        if grep -q "monitoring_location" terraform.tfvars; then
            MONITORING_LOCATION=$(grep "monitoring_location" terraform.tfvars | sed 's/.*= *"\([^"]*\)".*/\1/')
            print_success "monitoring_location configured: $MONITORING_LOCATION"
            
            # Check if it's a valid region for Log Analytics
            VALID_REGIONS=("eastus" "westeurope" "southeastasia" "australiasoutheast" "westcentralus" "japaneast" "uksouth" "centralindia" "canadacentral" "westus2")
            if [[ " ${VALID_REGIONS[@]} " =~ " ${MONITORING_LOCATION} " ]]; then
                print_success "monitoring_location is a valid Log Analytics region"
            else
                print_warning "monitoring_location may not support Log Analytics Workspace"
            fi
        else
            print_error "monitoring_location not configured in terraform.tfvars"
        fi
        
        # Check main location
        if grep -q "location" terraform.tfvars; then
            MAIN_LOCATION=$(grep "^location" terraform.tfvars | sed 's/.*= *"\([^"]*\)".*/\1/')
            print_info "Main location configured: $MAIN_LOCATION"
        fi
    else
        print_warning "terraform.tfvars file not found"
    fi
}

# Test main.tf monitoring resources
test_monitoring_resources() {
    print_header "Testing Monitoring Resources Configuration"
    
    if [ -f "main.tf" ]; then
        print_success "main.tf file found"
        
        # Check Log Analytics Workspace location
        if grep -A 10 'resource "azurerm_log_analytics_workspace"' main.tf | grep -q "var.monitoring_location"; then
            print_success "Log Analytics Workspace uses var.monitoring_location"
        else
            print_error "Log Analytics Workspace not configured to use monitoring_location"
        fi
        
        # Check Application Insights location
        if grep -A 10 'resource "azurerm_application_insights"' main.tf | grep -q "var.monitoring_location"; then
            print_success "Application Insights uses var.monitoring_location"
        else
            print_error "Application Insights not configured to use monitoring_location"
        fi
        
        # Check workspace dependency
        if grep -A 10 'resource "azurerm_application_insights"' main.tf | grep -q "azurerm_log_analytics_workspace.main.id"; then
            print_success "Application Insights correctly references Log Analytics Workspace"
        else
            print_error "Application Insights workspace dependency not found"
        fi
        
        # Check for monitoring region tags
        if grep -A 15 'resource "azurerm_log_analytics_workspace"' main.tf | grep -q "MonitoringRegion"; then
            print_success "Monitoring region tags found in Log Analytics Workspace"
        else
            print_warning "No monitoring region tags found (optional but recommended)"
        fi
    else
        print_error "main.tf file not found"
    fi
}

# Test GitHub Actions workflow
test_github_workflow() {
    print_header "Testing GitHub Actions Workflow Configuration"
    
    WORKFLOW_FILE="../../../../.github/workflows/filtro-curricular-infrastructure.yml"
    
    if [ -f "$WORKFLOW_FILE" ]; then
        print_success "GitHub Actions workflow file found"
        
        # Check for monitoring location variable
        if grep -q "AZURE_MONITORING_LOCATION" "$WORKFLOW_FILE"; then
            print_success "AZURE_MONITORING_LOCATION variable found in workflow"
        else
            print_error "AZURE_MONITORING_LOCATION variable not found in workflow"
        fi
        
        # Check for monitoring_location in terraform.tfvars generation
        if grep -A 20 "cat > terraform.tfvars" "$WORKFLOW_FILE" | grep -q "monitoring_location"; then
            print_success "monitoring_location included in terraform.tfvars generation"
        else
            print_error "monitoring_location not included in terraform.tfvars generation"
        fi
    else
        print_warning "GitHub Actions workflow file not found at $WORKFLOW_FILE"
    fi
}

# Test regional availability logic
test_regional_logic() {
    print_header "Testing Regional Availability Logic"
    
    # Simulate different location scenarios
    MAIN_LOCATION="chilecental"
    MONITORING_LOCATION="eastus"
    
    print_info "Testing scenario: Main location = $MAIN_LOCATION, Monitoring location = $MONITORING_LOCATION"
    
    # Check if main location supports Log Analytics
    LOG_ANALYTICS_REGIONS=("eastus" "westeurope" "southeastasia" "australiasoutheast" "westcentralus" "japaneast" "uksouth" "centralindia" "canadacentral" "westus2" "australiacentral" "australiaeast" "francecentral" "koreacentral" "northeurope" "centralus" "eastasia" "eastus2" "southcentralus" "northcentralus" "westus" "ukwest" "southafricanorth" "brazilsouth")
    
    if [[ " ${LOG_ANALYTICS_REGIONS[@]} " =~ " ${MAIN_LOCATION} " ]]; then
        print_info "Main location ($MAIN_LOCATION) supports Log Analytics Workspace"
        print_warning "Consider using main location for monitoring to reduce cross-region latency"
    else
        print_info "Main location ($MAIN_LOCATION) does not support Log Analytics Workspace"
        print_success "Separate monitoring region strategy is appropriate"
    fi
    
    if [[ " ${LOG_ANALYTICS_REGIONS[@]} " =~ " ${MONITORING_LOCATION} " ]]; then
        print_success "Monitoring location ($MONITORING_LOCATION) supports Log Analytics Workspace"
    else
        print_error "Monitoring location ($MONITORING_LOCATION) does not support Log Analytics Workspace"
    fi
}

# Test Terraform validation
test_terraform_validation() {
    print_header "Testing Terraform Configuration Validation"
    
    if command -v terraform &> /dev/null; then
        print_info "Running terraform validate..."
        if terraform validate >/dev/null 2>&1; then
            print_success "Terraform configuration is valid"
        else
            print_error "Terraform validation failed"
            print_info "Run 'terraform validate' for detailed error information"
        fi
    else
        print_warning "Terraform not available, skipping validation test"
    fi
}

# Test cross-region monitoring considerations
test_cross_region_considerations() {
    print_header "Testing Cross-Region Monitoring Considerations"
    
    MAIN_LOCATION="chilecental"
    MONITORING_LOCATION="eastus"
    
    print_info "Analyzing cross-region monitoring setup:"
    print_info "  Main resources: $MAIN_LOCATION"
    print_info "  Monitoring resources: $MONITORING_LOCATION"
    
    # Calculate approximate distance/latency (simplified)
    case "$MAIN_LOCATION" in
        "chilecental")
            case "$MONITORING_LOCATION" in
                "eastus")
                    print_info "  Estimated latency: ~150-200ms (Chile to US East)"
                    print_success "Acceptable latency for telemetry data"
                    ;;
                "westeurope")
                    print_info "  Estimated latency: ~200-250ms (Chile to West Europe)"
                    print_success "Acceptable latency for telemetry data"
                    ;;
                "southeastasia")
                    print_info "  Estimated latency: ~300-400ms (Chile to Southeast Asia)"
                    print_warning "Higher latency, consider closer region if available"
                    ;;
                *)
                    print_info "  Latency varies by region"
                    ;;
            esac
            ;;
    esac
    
    print_info "Cross-region monitoring benefits:"
    print_success "  ✓ Full Log Analytics and Application Insights functionality"
    print_success "  ✓ No additional costs for cross-region monitoring"
    print_success "  ✓ Standard Azure pattern for regional availability"
    
    print_info "Considerations:"
    print_warning "  • Telemetry data stored in different region than application data"
    print_warning "  • Review data residency requirements for compliance"
    print_warning "  • Minimal data egress costs for telemetry transmission"
}

# Main execution
main() {
    print_header "Azure Regional Availability Solution Validation"
    print_info "This script validates the monitoring region strategy implementation"
    
    test_monitoring_variable
    test_tfvars_config
    test_monitoring_resources
    test_github_workflow
    test_regional_logic
    test_terraform_validation
    test_cross_region_considerations
    
    print_header "Validation Summary"
    print_info "Review the results above to ensure the regional availability solution is correctly configured"
    print_info "For detailed information, see REGIONAL_AVAILABILITY_SOLUTIONS.md"
}

# Run the tests
main "$@"
