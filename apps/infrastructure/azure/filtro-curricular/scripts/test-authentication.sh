#!/bin/bash

# Test script to validate Azure authentication configuration
# This script helps verify that the authentication fix is working correctly

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Print functions
print_header() {
    echo -e "\n${BLUE}=== $1 ===${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if required tools are installed
check_prerequisites() {
    print_header "Checking Prerequisites"
    
    # Check if jq is installed
    if command -v jq &> /dev/null; then
        print_success "jq is installed"
    else
        print_error "jq is not installed. Please install jq to run this test."
        exit 1
    fi
    
    # Check if terraform is installed
    if command -v terraform &> /dev/null; then
        print_success "Terraform is installed: $(terraform version | head -n1)"
    else
        print_warning "Terraform is not installed. Some tests will be skipped."
    fi
    
    # Check if az CLI is installed
    if command -v az &> /dev/null; then
        print_success "Azure CLI is installed: $(az version --query '"azure-cli"' -o tsv)"
    else
        print_warning "Azure CLI is not installed. Some tests will be skipped."
    fi
}

# Test Azure credentials JSON format
test_credentials_format() {
    print_header "Testing Azure Credentials Format"
    
    if [ -f "AZURE_CREDENTIALS.json" ]; then
        print_info "Found AZURE_CREDENTIALS.json file"
        
        # Test if it's valid JSON
        if jq empty AZURE_CREDENTIALS.json 2>/dev/null; then
            print_success "AZURE_CREDENTIALS.json is valid JSON"
            
            # Check required fields
            required_fields=("clientId" "clientSecret" "subscriptionId" "tenantId")
            for field in "${required_fields[@]}"; do
                if jq -e ".$field" AZURE_CREDENTIALS.json >/dev/null 2>&1; then
                    value=$(jq -r ".$field" AZURE_CREDENTIALS.json)
                    if [ "$value" != "null" ] && [ -n "$value" ]; then
                        print_success "$field is present and not empty"
                    else
                        print_error "$field is present but empty"
                    fi
                else
                    print_error "$field is missing from AZURE_CREDENTIALS.json"
                fi
            done
        else
            print_error "AZURE_CREDENTIALS.json is not valid JSON"
        fi
    else
        print_warning "AZURE_CREDENTIALS.json not found in current directory"
        print_info "This file should contain your Service Principal credentials"
    fi
}

# Test ARM environment variables extraction
test_arm_variables() {
    print_header "Testing ARM Environment Variables Extraction"
    
    if [ -f "AZURE_CREDENTIALS.json" ]; then
        print_info "Extracting ARM variables from AZURE_CREDENTIALS.json..."
        
        # Extract variables (same as in GitHub Actions)
        ARM_CLIENT_ID=$(jq -r '.clientId' AZURE_CREDENTIALS.json)
        ARM_CLIENT_SECRET=$(jq -r '.clientSecret' AZURE_CREDENTIALS.json)
        ARM_SUBSCRIPTION_ID=$(jq -r '.subscriptionId' AZURE_CREDENTIALS.json)
        ARM_TENANT_ID=$(jq -r '.tenantId' AZURE_CREDENTIALS.json)
        
        # Validate extracted values
        if [ "$ARM_CLIENT_ID" != "null" ] && [ -n "$ARM_CLIENT_ID" ]; then
            print_success "ARM_CLIENT_ID extracted: ${ARM_CLIENT_ID:0:8}..."
        else
            print_error "Failed to extract ARM_CLIENT_ID"
        fi
        
        if [ "$ARM_CLIENT_SECRET" != "null" ] && [ -n "$ARM_CLIENT_SECRET" ]; then
            print_success "ARM_CLIENT_SECRET extracted: ${ARM_CLIENT_SECRET:0:8}..."
        else
            print_error "Failed to extract ARM_CLIENT_SECRET"
        fi
        
        if [ "$ARM_SUBSCRIPTION_ID" != "null" ] && [ -n "$ARM_SUBSCRIPTION_ID" ]; then
            print_success "ARM_SUBSCRIPTION_ID extracted: $ARM_SUBSCRIPTION_ID"
        else
            print_error "Failed to extract ARM_SUBSCRIPTION_ID"
        fi
        
        if [ "$ARM_TENANT_ID" != "null" ] && [ -n "$ARM_TENANT_ID" ]; then
            print_success "ARM_TENANT_ID extracted: $ARM_TENANT_ID"
        else
            print_error "Failed to extract ARM_TENANT_ID"
        fi
        
        # Export for potential Terraform test
        export ARM_CLIENT_ID
        export ARM_CLIENT_SECRET
        export ARM_SUBSCRIPTION_ID
        export ARM_TENANT_ID
        export ARM_USE_CLI=false
        
        print_success "ARM environment variables set for testing"
    else
        print_warning "Cannot test ARM variables without AZURE_CREDENTIALS.json"
    fi
}

# Test Azure CLI authentication (if available)
test_azure_cli_auth() {
    print_header "Testing Azure CLI Authentication"
    
    if command -v az &> /dev/null && [ -f "AZURE_CREDENTIALS.json" ]; then
        print_info "Testing Service Principal login with Azure CLI..."
        
        CLIENT_ID=$(jq -r '.clientId' AZURE_CREDENTIALS.json)
        CLIENT_SECRET=$(jq -r '.clientSecret' AZURE_CREDENTIALS.json)
        TENANT_ID=$(jq -r '.tenantId' AZURE_CREDENTIALS.json)
        
        # Test login (will logout after test)
        if az login --service-principal \
            --username "$CLIENT_ID" \
            --password "$CLIENT_SECRET" \
            --tenant "$TENANT_ID" >/dev/null 2>&1; then
            print_success "Service Principal authentication successful"
            
            # Get current account info
            ACCOUNT_INFO=$(az account show 2>/dev/null)
            if [ $? -eq 0 ]; then
                SUBSCRIPTION_NAME=$(echo "$ACCOUNT_INFO" | jq -r '.name')
                print_success "Connected to subscription: $SUBSCRIPTION_NAME"
            fi
            
            # Logout to clean up
            az logout >/dev/null 2>&1
        else
            print_error "Service Principal authentication failed"
            print_info "Check your credentials in AZURE_CREDENTIALS.json"
        fi
    else
        print_warning "Skipping Azure CLI test (az CLI not available or credentials missing)"
    fi
}

# Test Terraform configuration
test_terraform_config() {
    print_header "Testing Terraform Configuration"
    
    if command -v terraform &> /dev/null; then
        print_info "Checking Terraform configuration..."
        
        # Check if main.tf exists
        if [ -f "main.tf" ]; then
            print_success "main.tf found"
            
            # Check if provider has use_cli = false
            if grep -q "use_cli.*=.*false" main.tf; then
                print_success "Azure provider configured with use_cli = false"
            else
                print_warning "Azure provider may not have use_cli = false configured"
                print_info "This should be added to prevent Azure CLI authentication"
            fi
            
            # Test terraform validate (if ARM variables are set)
            if [ -n "$ARM_CLIENT_ID" ]; then
                print_info "Testing terraform validate..."
                if terraform validate >/dev/null 2>&1; then
                    print_success "Terraform configuration is valid"
                else
                    print_warning "Terraform validation failed (this may be expected without full setup)"
                fi
            else
                print_info "Skipping terraform validate (ARM variables not set)"
            fi
        else
            print_warning "main.tf not found in current directory"
        fi
    else
        print_warning "Skipping Terraform tests (terraform not available)"
    fi
}

# Test GitHub Actions workflow configuration
test_github_workflow() {
    print_header "Testing GitHub Actions Workflow Configuration"
    
    WORKFLOW_FILE="../../../../.github/workflows/filtro-curricular-infrastructure.yml"
    
    if [ -f "$WORKFLOW_FILE" ]; then
        print_success "GitHub Actions workflow file found"
        
        # Check for ARM environment variables setup
        if grep -q "ARM_CLIENT_ID" "$WORKFLOW_FILE"; then
            print_success "ARM_CLIENT_ID configuration found in workflow"
        else
            print_error "ARM_CLIENT_ID configuration missing from workflow"
        fi
        
        if grep -q "ARM_USE_CLI=false" "$WORKFLOW_FILE"; then
            print_success "ARM_USE_CLI=false configuration found in workflow"
        else
            print_error "ARM_USE_CLI=false configuration missing from workflow"
        fi
        
        if grep -q "jq -r '.clientId'" "$WORKFLOW_FILE"; then
            print_success "JSON credential extraction found in workflow"
        else
            print_error "JSON credential extraction missing from workflow"
        fi
    else
        print_warning "GitHub Actions workflow file not found at $WORKFLOW_FILE"
    fi
}

# Main execution
main() {
    print_header "Azure Authentication Configuration Test"
    print_info "This script validates the authentication fix for Terraform Azure provider"
    
    check_prerequisites
    test_credentials_format
    test_arm_variables
    test_azure_cli_auth
    test_terraform_config
    test_github_workflow
    
    print_header "Test Summary"
    print_info "Review the results above to ensure your authentication configuration is correct"
    print_info "For detailed troubleshooting, see AUTHENTICATION_FIX_GUIDE.md"
}

# Run the tests
main "$@"
