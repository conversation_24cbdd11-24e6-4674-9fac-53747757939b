#!/bin/bash

# Environment-Specific Deployment Script for Filtro Curricular
# This script handles deployment to different environments with proper secret management

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

# Function to validate environment
validate_environment() {
    local env=$1
    case $env in
        dev|staging|uat|prod)
            return 0
            ;;
        *)
            print_error "Invalid environment: $env"
            print_error "Valid environments: dev, staging, uat, prod"
            return 1
            ;;
    esac
}

# Function to check prerequisites
check_prerequisites() {
    print_header "Checking Prerequisites"
    
    # Check if Azure CLI is installed and logged in
    if ! command -v az &> /dev/null; then
        print_error "Azure CLI is not installed. Please install it first."
        exit 1
    fi
    
    # Check if logged in to Azure
    if ! az account show &> /dev/null; then
        print_error "Not logged in to Azure. Please run 'az login' first."
        exit 1
    fi
    
    # Check if Terraform is installed
    if ! command -v terraform &> /dev/null; then
        print_error "Terraform is not installed. Please install it first."
        exit 1
    fi
    
    print_status "✅ All prerequisites met"
}

# Function to set environment variables based on environment
set_environment_variables() {
    local env=$1
    
    print_header "Setting Environment Variables for $env"
    
    export TF_VAR_environment=$env
    export RESOURCE_GROUP="filtro-curricular-rg-${env}"
    
    # Environment-specific configurations
    case $env in
        dev)
            export TF_VAR_app_service_plan_sku="B1"
            export TF_VAR_acr_sku="Basic"
            export TF_VAR_enable_vnet="false"
            export TF_VAR_enable_app_gateway="false"
            ;;
        staging)
            export TF_VAR_app_service_plan_sku="S1"
            export TF_VAR_acr_sku="Standard"
            export TF_VAR_enable_vnet="true"
            export TF_VAR_enable_app_gateway="false"
            ;;
        prod)
            export TF_VAR_app_service_plan_sku="P1v3"
            export TF_VAR_acr_sku="Premium"
            export TF_VAR_enable_vnet="true"
            export TF_VAR_enable_app_gateway="true"
            ;;
    esac
    
    print_status "Environment variables set for $env"
}

# Function to deploy infrastructure
deploy_infrastructure() {
    local env=$1
    local action=${2:-apply}
    
    print_header "Deploying Infrastructure for $env"
    
    # Use environment-specific tfvars file
    local tfvars_file="environments/${env}.tfvars"
    
    if [ ! -f "$tfvars_file" ]; then
        print_error "Environment configuration file not found: $tfvars_file"
        exit 1
    fi
    
    print_status "Using configuration file: $tfvars_file"
    
    # Initialize Terraform
    print_status "Initializing Terraform..."
    terraform init
    
    # Plan deployment
    print_status "Planning deployment..."
    terraform plan -var-file="$tfvars_file" -out="tfplan-${env}"
    
    if [ "$action" = "plan" ]; then
        print_status "Plan completed. Review the plan above."
        return 0
    fi
    
    # Apply deployment
    print_status "Applying deployment..."
    terraform apply "tfplan-${env}"
    
    print_status "✅ Infrastructure deployment completed for $env"
}

# Function to manage secrets for environment
manage_secrets() {
    local env=$1
    
    print_header "Managing Secrets for $env"
    
    # Set environment for secrets script
    export ENVIRONMENT=$env
    export RESOURCE_GROUP="filtro-curricular-rg-${env}"
    
    # Check if secrets file exists for this environment
    local secrets_file="secrets/secrets-${env}.json"
    
    if [ -f "$secrets_file" ]; then
        print_status "Found environment-specific secrets file: $secrets_file"
        ./scripts/manage-secrets.sh update-from-file "$secrets_file"
    else
        print_warning "No environment-specific secrets file found: $secrets_file"
        print_status "Using interactive secret management..."
        ./scripts/manage-secrets.sh update
    fi
}

# Function to show usage
show_usage() {
    echo "Usage: $0 <environment> [action] [options]"
    echo
    echo "Environments:"
    echo "  dev      - Development environment"
    echo "  staging  - Staging environment"
    echo "  uat      - User Acceptance Testing environment"
    echo "  prod     - Production environment"
    echo
    echo "Actions:"
    echo "  deploy   - Deploy infrastructure (default)"
    echo "  plan     - Plan deployment without applying"
    echo "  secrets  - Manage secrets only"
    echo "  destroy  - Destroy infrastructure (use with caution)"
    echo
    echo "Options:"
    echo "  --skip-secrets    Skip secret management"
    echo "  --force          Force deployment without confirmation"
    echo
    echo "Examples:"
    echo "  $0 dev deploy"
    echo "  $0 staging plan"
    echo "  $0 prod secrets"
    echo "  $0 dev deploy --skip-secrets"
}

# Main function
main() {
    local environment=${1:-}
    local action=${2:-deploy}
    local skip_secrets=false
    local force=false
    
    # Parse options
    shift 2 2>/dev/null || shift $# 2>/dev/null
    while [[ $# -gt 0 ]]; do
        case $1 in
            --skip-secrets)
                skip_secrets=true
                shift
                ;;
            --force)
                force=true
                shift
                ;;
            *)
                print_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done
    
    # Validate inputs
    if [ -z "$environment" ]; then
        print_error "Environment is required"
        show_usage
        exit 1
    fi
    
    if ! validate_environment "$environment"; then
        exit 1
    fi
    
    # Confirmation for production
    if [ "$environment" = "prod" ] && [ "$force" = false ]; then
        print_warning "You are about to deploy to PRODUCTION environment!"
        read -p "Are you sure you want to continue? (yes/no): " confirm
        if [ "$confirm" != "yes" ]; then
            print_status "Deployment cancelled"
            exit 0
        fi
    fi
    
    # Execute based on action
    case $action in
        deploy|plan)
            check_prerequisites
            set_environment_variables "$environment"
            deploy_infrastructure "$environment" "$action"
            
            if [ "$action" = "deploy" ] && [ "$skip_secrets" = false ]; then
                manage_secrets "$environment"
            fi
            ;;
        secrets)
            check_prerequisites
            set_environment_variables "$environment"
            manage_secrets "$environment"
            ;;
        destroy)
            print_warning "This will DESTROY all infrastructure for $environment!"
            if [ "$force" = false ]; then
                read -p "Type 'destroy' to confirm: " confirm
                if [ "$confirm" != "destroy" ]; then
                    print_status "Destruction cancelled"
                    exit 0
                fi
            fi
            
            check_prerequisites
            set_environment_variables "$environment"
            terraform destroy -var-file="environments/${environment}.tfvars"
            ;;
        *)
            print_error "Unknown action: $action"
            show_usage
            exit 1
            ;;
    esac
    
    print_status "✅ Operation completed successfully for $environment"
}

# Run main function
main "$@"
