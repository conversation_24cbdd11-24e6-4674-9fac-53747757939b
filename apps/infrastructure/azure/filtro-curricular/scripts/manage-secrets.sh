#!/bin/bash

# Azure Key Vault Secrets Management Script
# This script manages secrets for the Filtro Curricular application

set -e


# Configuration
RESOURCE_GROUP="filtro-curricular-rg-dev"
ENVIRONMENT="dev"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

# Function to get Key Vault name
get_keyvault_name() {
    print_status "Finding Key Vault in resource group: $RESOURCE_GROUP"
    
    KEY_VAULT_NAME=$(az keyvault list \
        --resource-group $RESOURCE_GROUP \
        --query "[0].name" \
        --output tsv 2>/dev/null)
    
    if [ -z "$KEY_VAULT_NAME" ] || [ "$KEY_VAULT_NAME" = "null" ]; then
        print_error "No Key Vault found in resource group $RESOURCE_GROUP"
        print_error "Make sure Terraform has been applied and the infrastructure exists"
        exit 1
    fi
    
    print_status "Found Key Vault: $KEY_VAULT_NAME"
    echo $KEY_VAULT_NAME
}

# Function to check if user is logged in to Azure
check_azure_login() {
    print_status "Checking Azure CLI authentication..."
    
    if ! az account show &>/dev/null; then
        print_error "Not logged in to Azure CLI"
        print_status "Please run: az login"
        exit 1
    fi
    
    SUBSCRIPTION=$(az account show --query "name" --output tsv)
    print_status "Logged in to subscription: $SUBSCRIPTION"
}

# Function to validate secret format
validate_openai_key() {
    local key=$1
    if [[ ! $key =~ ^sk-[a-zA-Z0-9_-]+$ ]]; then
        print_warning "OpenAI API key should start with 'sk-' and contain only alphanumeric characters, hyphens, and underscores"
        return 1
    fi
    return 0
}

validate_assistant_id() {
    local id=$1
    if [[ ! $id =~ ^asst_[a-zA-Z0-9_-]+$ ]]; then
        print_warning "Assistant ID should start with 'asst_' and contain only alphanumeric characters, hyphens, and underscores"
        return 1
    fi
    return 0
}

# Function to set a secret with validation
set_secret() {
    local vault_name=$1
    local secret_name=$2
    local secret_value=$3
    local description=$4

    if [ -z "$secret_value" ]; then
        print_error "Secret value cannot be empty for $secret_name"
        return 1
    fi

    print_status "Setting secret: $secret_name ($description)"

    # Store the complete command in a variable for transparency and debugging
    local keyvault_command="az keyvault secret set --vault-name \"$vault_name\" --name \"$secret_name\" --value \"[REDACTED]\" --description \"$description\" --output none"

    # Echo the command for debugging (with redacted value for security)
    print_status "Executing command: $keyvault_command"

    # Execute the actual command and capture the exit code
    az keyvault secret set \
        --vault-name "$vault_name" \
        --name "$secret_name" \
        --value "$secret_value" \
        --description "$description" \
        --output none

    # Capture the exit code in a variable
    local exit_code=$?
    echo "
    # Use the captured exit code to determine success/failure
    if [ $exit_code -eq 0 ]; then
        print_status "✅ Successfully set secret: $secret_name"
    else
        print_error "❌ Failed to set secret: $secret_name (exit code: $exit_code)"
        return 1
    fi
}

# Function to list all secrets
list_secrets() {
    local vault_name=$1
    
    print_header "Current Secrets in Key Vault: $vault_name"
    
    az keyvault secret list \
        --vault-name "$vault_name" \
        --query "[].{Name:name, Enabled:attributes.enabled, Updated:attributes.updated}" \
        --output table
}

# Function to show secret metadata (without value)
show_secret_info() {
    local vault_name=$1
    local secret_name=$2
    
    print_status "Secret information for: $secret_name"
    
    az keyvault secret show \
        --vault-name "$vault_name" \
        --name "$secret_name" \
        --query "{Name:name, Enabled:attributes.enabled, Created:attributes.created, Updated:attributes.updated, ContentType:contentType}" \
        --output table
}

# Function to update OpenAI secrets interactively
update_openai_secrets() {
    local vault_name=$1
    
    print_header "Updating OpenAI Secrets"
    print_warning "You will be prompted to enter each secret value"
    print_warning "Secret values will not be displayed on screen"
    echo
    
    # OpenAI API Key
    while true; do
        echo -n "Enter OpenAI API Key (starts with sk-): "
        read -s openai_api_key
        echo
        if validate_openai_key "$openai_api_key"; then
            break
        fi
        print_error "Invalid format. Please try again."
    done
    
    # OpenAI Token
    while true; do
        echo -n "Enter OpenAI Token (starts with sk-): "
        read -s openai_token
        echo
        if validate_openai_key "$openai_token"; then
            break
        fi
        print_error "Invalid format. Please try again."
    done
    
    # Assistant ID Juridico
    while true; do
        echo -n "Enter Assistant ID for Juridico (starts with asst_): "
        read -s assistant_id_juridico
        echo
        if validate_assistant_id "$assistant_id_juridico"; then
            break
        fi
        print_error "Invalid format. Please try again."
    done
    
    # Assistant ID Calidad
    while true; do
        echo -n "Enter Assistant ID for Calidad (starts with asst_): "
        read -s assistant_id_calidad
        echo
        if validate_assistant_id "$assistant_id_calidad"; then
            break
        fi
        print_error "Invalid format. Please try again."
    done
    
    echo
    print_status "Updating secrets in Key Vault..."
    
    # Set all secrets
    set_secret "$vault_name" "openai-api-key" "$openai_api_key" "OpenAI API Key for GPT models"
    set_secret "$vault_name" "openai-token" "$openai_token" "OpenAI Token for assistant API"
    set_secret "$vault_name" "assistant-id-juridico" "$assistant_id_juridico" "OpenAI Assistant ID for Juridico domain"
    set_secret "$vault_name" "assistant-id-calidad" "$assistant_id_calidad" "OpenAI Assistant ID for Calidad domain"
    
    print_status "✅ All OpenAI secrets updated successfully!"
}

# Function to update secrets from file
update_secrets_from_file() {
    local vault_name=$1
    local secrets_file=$2
    
    if [ ! -f "$secrets_file" ]; then
        print_error "Secrets file not found: $secrets_file"
        exit 1
    fi
    
    print_header "Updating Secrets from File: $secrets_file"
    
    # Read and validate JSON
    if ! jq empty "$secrets_file" 2>/dev/null; then
        print_error "Invalid JSON format in secrets file"
        exit 1
    fi
    
    # Extract values
    openai_api_key=$(jq -r '.openai_api_key // empty' "$secrets_file")
    openai_token=$(jq -r '.openai_token // empty' "$secrets_file")
    assistant_id_juridico=$(jq -r '.assistant_id_juridico // empty' "$secrets_file")
    assistant_id_calidad=$(jq -r '.assistant_id_calidad // empty' "$secrets_file")
    
    # Validate and set secrets
    if [ -n "$openai_api_key" ] && validate_openai_key "$openai_api_key"; then
        set_secret "$vault_name" "openai-api-key" "$openai_api_key" "OpenAI API Key for GPT models"
    fi
    
    if [ -n "$openai_token" ] && validate_openai_key "$openai_token"; then
        set_secret "$vault_name" "openai-token" "$openai_token" "OpenAI Token for assistant API"
    fi
    
    if [ -n "$assistant_id_juridico" ] && validate_assistant_id "$assistant_id_juridico"; then
        set_secret "$vault_name" "assistant-id-juridico" "$assistant_id_juridico" "OpenAI Assistant ID for Juridico domain"
    fi
    
    if [ -n "$assistant_id_calidad" ] && validate_assistant_id "$assistant_id_calidad"; then
        set_secret "$vault_name" "assistant-id-calidad" "$assistant_id_calidad" "OpenAI Assistant ID for Calidad domain"
    fi
    
    print_status "✅ Secrets updated from file successfully!"
}

# Main script logic
main() {
    print_header "Azure Key Vault Secrets Management"
    
    # Check prerequisites
    check_azure_login
    
    # Get Key Vault name
    KEY_VAULT_NAME=$(get_keyvault_name)
    
    # Parse command line arguments
    case "${1:-}" in
        "list")
            list_secrets "$KEY_VAULT_NAME"
            ;;
        "update")
            update_openai_secrets "$KEY_VAULT_NAME"
            ;;
        "update-from-file")
            if [ -z "$2" ]; then
                print_error "Usage: $0 update-from-file <secrets-file.json>"
                exit 1
            fi
            update_secrets_from_file "$KEY_VAULT_NAME" "$2"
            ;;
        "info")
            if [ -z "$2" ]; then
                print_error "Usage: $0 info <secret-name>"
                exit 1
            fi
            show_secret_info "$KEY_VAULT_NAME" "$2"
            ;;
        "help"|"--help"|"-h"|"")
            echo "Usage: $0 <command> [options]"
            echo
            echo "Commands:"
            echo "  list                     List all secrets in Key Vault"
            echo "  update                   Interactively update OpenAI secrets"
            echo "  update-from-file <file>  Update secrets from JSON file"
            echo "  info <secret-name>       Show information about a specific secret"
            echo "  help                     Show this help message"
            echo
            echo "Examples:"
            echo "  $0 list"
            echo "  $0 update"
            echo "  $0 update-from-file secrets.json"
            echo "  $0 info openai-api-key"
            ;;
        *)
            print_error "Unknown command: $1"
            print_status "Run '$0 help' for usage information"
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
