#!/bin/bash

# Test script to validate Azure resource naming conventions
# This script helps verify that all resource names comply with Azure naming rules

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Print functions
print_header() {
    echo -e "\n${BLUE}=== $1 ===${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Test Azure Container Registry naming
test_acr_naming() {
    print_header "Testing Azure Container Registry Naming"
    
    # Simulate the naming pattern from acr.tf
    PROJECT_NAME="filtro-curricular"
    ENVIRONMENT="dev"
    SUFFIX="kt9kai"
    
    # Clean project name (remove hyphens, lowercase)
    PROJECT_NAME_CLEAN=$(echo "$PROJECT_NAME" | tr '[:upper:]' '[:lower:]' | sed 's/-//g')
    ACR_NAME="${PROJECT_NAME_CLEAN}acr${ENVIRONMENT}${SUFFIX}"
    
    print_info "Generated ACR name: $ACR_NAME"
    
    # Test ACR naming rules
    # - 5-50 characters
    # - Alphanumeric characters only
    # - Must start with a letter
    
    if [[ ${#ACR_NAME} -ge 5 && ${#ACR_NAME} -le 50 ]]; then
        print_success "ACR name length is valid (${#ACR_NAME} characters)"
    else
        print_error "ACR name length is invalid (${#ACR_NAME} characters, must be 5-50)"
    fi
    
    if [[ $ACR_NAME =~ ^[a-zA-Z][a-zA-Z0-9]*$ ]]; then
        print_success "ACR name contains only alphanumeric characters and starts with letter"
    else
        print_error "ACR name contains invalid characters or doesn't start with letter"
    fi
    
    if [[ $ACR_NAME =~ ^[a-z0-9]*$ ]]; then
        print_success "ACR name is lowercase alphanumeric only"
    else
        print_error "ACR name contains uppercase letters or special characters"
    fi
}

# Test Storage Account naming
test_storage_naming() {
    print_header "Testing Storage Account Naming"
    
    # Simulate the naming pattern from storage.tf
    PROJECT_NAME="filtro-curricular"
    ENVIRONMENT="dev"
    SUFFIX="kt9kai"
    
    # Clean project name (remove hyphens, lowercase) and shorten for storage
    PROJECT_NAME_CLEAN=$(echo "$PROJECT_NAME" | tr '[:upper:]' '[:lower:]' | sed 's/-//g')
    PROJECT_NAME_SHORT=$(echo "$PROJECT_NAME_CLEAN" | cut -c1-8)
    STORAGE_NAME="${PROJECT_NAME_SHORT}st${ENVIRONMENT}${SUFFIX}"
    
    print_info "Generated Storage Account name: $STORAGE_NAME"
    
    # Test Storage Account naming rules
    # - 3-24 characters
    # - Lowercase letters and numbers only
    # - Must be globally unique
    
    if [[ ${#STORAGE_NAME} -ge 3 && ${#STORAGE_NAME} -le 24 ]]; then
        print_success "Storage Account name length is valid (${#STORAGE_NAME} characters)"
    else
        print_error "Storage Account name length is invalid (${#STORAGE_NAME} characters, must be 3-24)"
    fi
    
    if [[ $STORAGE_NAME =~ ^[a-z0-9]*$ ]]; then
        print_success "Storage Account name contains only lowercase letters and numbers"
    else
        print_error "Storage Account name contains invalid characters (must be lowercase letters and numbers only)"
    fi
}

# Test Key Vault naming
test_keyvault_naming() {
    print_header "Testing Key Vault Naming"
    
    # Simulate the naming pattern from main.tf
    PROJECT_NAME="filtro-curricular"
    ENVIRONMENT="dev"
    SUFFIX="kt9kai"
    
    # Shortened project name for Key Vault (first 8 characters after cleaning)
    PROJECT_NAME_CLEAN=$(echo "$PROJECT_NAME" | tr '[:upper:]' '[:lower:]' | sed 's/-//g')
    PROJECT_NAME_SHORT=$(echo "$PROJECT_NAME_CLEAN" | cut -c1-8)
    KV_NAME="${PROJECT_NAME_SHORT}-kv-${ENVIRONMENT}-${SUFFIX}"
    
    print_info "Generated Key Vault name: $KV_NAME"
    
    # Test Key Vault naming rules
    # - 3-24 characters
    # - Alphanumeric and hyphens only
    # - Must start with letter
    # - Must end with letter or number
    # - Cannot have consecutive hyphens
    
    if [[ ${#KV_NAME} -ge 3 && ${#KV_NAME} -le 24 ]]; then
        print_success "Key Vault name length is valid (${#KV_NAME} characters)"
    else
        print_error "Key Vault name length is invalid (${#KV_NAME} characters, must be 3-24)"
    fi
    
    if [[ $KV_NAME =~ ^[a-zA-Z][a-zA-Z0-9-]*[a-zA-Z0-9]$ ]]; then
        print_success "Key Vault name starts with letter and ends with letter/number"
    else
        print_error "Key Vault name must start with letter and end with letter or number"
    fi
    
    if [[ $KV_NAME =~ ^[a-zA-Z0-9-]*$ ]]; then
        print_success "Key Vault name contains only alphanumeric characters and hyphens"
    else
        print_error "Key Vault name contains invalid characters"
    fi
    
    if [[ ! $KV_NAME =~ -- ]]; then
        print_success "Key Vault name doesn't contain consecutive hyphens"
    else
        print_error "Key Vault name contains consecutive hyphens"
    fi
}

# Test Azure location
test_location() {
    print_header "Testing Azure Location"
    
    LOCATION="chilecentral"
    
    print_info "Using location: $LOCATION"
    
    # List of valid Azure regions (subset)
    VALID_REGIONS=(
        "chilecentral" "eastus" "eastus2" "westus" "westus2" "westus3"
        "centralus" "northcentralus" "southcentralus" "westcentralus"
        "canadacentral" "canadaeast" "brazilsouth" "northeurope"
        "westeurope" "uksouth" "ukwest" "francecentral" "germanywestcentral"
        "norwayeast" "switzerlandnorth" "swedencentral" "eastasia"
        "southeastasia" "japaneast" "japanwest" "koreacentral"
        "australiaeast" "australiasoutheast" "centralindia" "southindia"
    )
    
    if [[ " ${VALID_REGIONS[@]} " =~ " ${LOCATION} " ]]; then
        print_success "Location '$LOCATION' is a valid Azure region"
    else
        print_error "Location '$LOCATION' is not a valid Azure region"
        print_info "Valid regions include: eastus, westus2, chilecentral, etc."
    fi
}

# Test resource group naming
test_resource_group_naming() {
    print_header "Testing Resource Group Naming"
    
    PROJECT_NAME="filtro-curricular"
    ENVIRONMENT="dev"
    
    RG_NAME="${PROJECT_NAME}-rg-${ENVIRONMENT}"
    
    print_info "Generated Resource Group name: $RG_NAME"
    
    # Test Resource Group naming rules
    # - 1-90 characters
    # - Alphanumeric, underscore, parentheses, hyphen, period (except at end)
    # - Unicode characters that belong to letter or number categories are allowed
    
    if [[ ${#RG_NAME} -ge 1 && ${#RG_NAME} -le 90 ]]; then
        print_success "Resource Group name length is valid (${#RG_NAME} characters)"
    else
        print_error "Resource Group name length is invalid (${#RG_NAME} characters, must be 1-90)"
    fi
    
    if [[ $RG_NAME =~ ^[a-zA-Z0-9._()-]*[a-zA-Z0-9_()-]$ ]]; then
        print_success "Resource Group name follows naming conventions"
    else
        print_error "Resource Group name contains invalid characters or ends with period"
    fi
}

# Test all naming patterns with Terraform locals simulation
test_terraform_locals() {
    print_header "Testing Terraform Locals Simulation"
    
    PROJECT_NAME="filtro-curricular"
    ENVIRONMENT="dev"
    SUFFIX="kt9kai"
    
    # Simulate Terraform locals
    PROJECT_NAME_CLEAN=$(echo "$PROJECT_NAME" | tr '[:upper:]' '[:lower:]' | sed 's/-//g')
    PROJECT_NAME_SHORT=$(echo "$PROJECT_NAME_CLEAN" | cut -c1-8)
    
    print_info "Original project_name: $PROJECT_NAME"
    print_info "project_name_clean: $PROJECT_NAME_CLEAN"
    print_info "project_name_short: $PROJECT_NAME_SHORT"
    
    # Test all resource names
    ACR_NAME="${PROJECT_NAME_CLEAN}acr${ENVIRONMENT}${SUFFIX}"
    STORAGE_NAME="${PROJECT_NAME_SHORT}st${ENVIRONMENT}${SUFFIX}"
    KV_NAME="${PROJECT_NAME_SHORT}-kv-${ENVIRONMENT}-${SUFFIX}"
    RG_NAME="${PROJECT_NAME}-rg-${ENVIRONMENT}"
    
    print_info "ACR name: $ACR_NAME (${#ACR_NAME} chars)"
    print_info "Storage name: $STORAGE_NAME (${#STORAGE_NAME} chars)"
    print_info "Key Vault name: $KV_NAME (${#KV_NAME} chars)"
    print_info "Resource Group name: $RG_NAME (${#RG_NAME} chars)"
    
    # Validate all names are within limits
    if [[ ${#ACR_NAME} -le 50 && ${#STORAGE_NAME} -le 24 && ${#KV_NAME} -le 24 && ${#RG_NAME} -le 90 ]]; then
        print_success "All resource names are within Azure length limits"
    else
        print_error "Some resource names exceed Azure length limits"
    fi
}

# Main execution
main() {
    print_header "Azure Resource Naming Convention Validation"
    print_info "This script validates that Terraform-generated resource names comply with Azure naming rules"
    
    test_location
    test_acr_naming
    test_storage_naming
    test_keyvault_naming
    test_resource_group_naming
    test_terraform_locals
    
    print_header "Validation Summary"
    print_info "Review the results above to ensure all naming conventions are correct"
    print_info "For detailed Azure naming rules, see: https://docs.microsoft.com/en-us/azure/azure-resource-manager/management/resource-name-rules"
}

# Run the tests
main "$@"
