#!/bin/bash

# Deployment Verification Script for Filtro Curricular
# This script verifies that all components are properly deployed and configured

set -e

# Configuration
RESOURCE_GROUP="filtro-curricular-rg-dev"
ENVIRONMENT="dev"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_failure() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to check if user is logged in to Azure
check_azure_login() {
    print_status "Checking Azure CLI authentication..."
    
    if ! az account show &>/dev/null; then
        print_error "Not logged in to Azure CLI"
        print_status "Please run: az login"
        exit 1
    fi
    
    SUBSCRIPTION=$(az account show --query "name" --output tsv)
    print_status "Logged in to subscription: $SUBSCRIPTION"
}

# Function to get resource names
get_resource_names() {
    print_status "Getting resource names from Azure..."
    
    # Get App Service names
    BACKEND_APP_NAME=$(az webapp list \
        --resource-group $RESOURCE_GROUP \
        --query "[?contains(name, 'be-')].name" \
        --output tsv)
    
    FRONTEND_APP_NAME=$(az webapp list \
        --resource-group $RESOURCE_GROUP \
        --query "[?contains(name, 'fe-')].name" \
        --output tsv)
    
    # Get Key Vault name
    KEY_VAULT_NAME=$(az keyvault list \
        --resource-group $RESOURCE_GROUP \
        --query "[0].name" \
        --output tsv)
    
    # Get Container Registry name
    ACR_NAME=$(az acr list \
        --resource-group $RESOURCE_GROUP \
        --query "[0].name" \
        --output tsv)
    
    if [ -z "$BACKEND_APP_NAME" ] || [ -z "$KEY_VAULT_NAME" ]; then
        print_error "Could not find required resources in $RESOURCE_GROUP"
        print_error "Make sure Terraform has been applied successfully"
        exit 1
    fi
    
    print_status "Found resources:"
    echo "  Backend App: $BACKEND_APP_NAME"
    echo "  Frontend App: $FRONTEND_APP_NAME"
    echo "  Key Vault: $KEY_VAULT_NAME"
    echo "  Container Registry: $ACR_NAME"
}

# Function to verify Key Vault secrets
verify_keyvault_secrets() {
    print_header "Verifying Key Vault Secrets"
    
    REQUIRED_SECRETS=("openai-api-key" "openai-token" "assistant-id-juridico" "assistant-id-calidad")
    MISSING_SECRETS=()
    
    for secret in "${REQUIRED_SECRETS[@]}"; do
        if az keyvault secret show --vault-name "$KEY_VAULT_NAME" --name "$secret" --query "name" -o tsv &>/dev/null; then
            print_success "Secret exists: $secret"
            
            # Check if it's not a placeholder
            SECRET_VALUE=$(az keyvault secret show --vault-name "$KEY_VAULT_NAME" --name "$secret" --query "value" -o tsv)
            if [[ "$SECRET_VALUE" == *"your-"* ]] || [[ "$SECRET_VALUE" == *"-here"* ]]; then
                print_warning "Secret '$secret' contains placeholder value"
                MISSING_SECRETS+=("$secret")
            fi
        else
            print_failure "Missing secret: $secret"
            MISSING_SECRETS+=("$secret")
        fi
    done
    
    if [ ${#MISSING_SECRETS[@]} -gt 0 ]; then
        print_warning "Missing or placeholder secrets found: ${MISSING_SECRETS[*]}"
        print_status "Run: ./scripts/manage-secrets.sh update"
        return 1
    else
        print_success "All required secrets are properly configured"
        return 0
    fi
}

# Function to verify App Service configuration
verify_app_service_config() {
    print_header "Verifying App Service Configuration"
    
    print_status "Checking backend app service settings..."
    
    # Check Key Vault references
    KEYVAULT_REFS=$(az webapp config appsettings list \
        --name "$BACKEND_APP_NAME" \
        --resource-group "$RESOURCE_GROUP" \
        --query "[?contains(value, '@Microsoft.KeyVault')].{Name:name, Value:value}" \
        --output json)
    
    if [ "$(echo "$KEYVAULT_REFS" | jq length)" -gt 0 ]; then
        print_success "Key Vault references configured:"
        echo "$KEYVAULT_REFS" | jq -r '.[] | "  \(.Name): \(.Value)"'
    else
        print_failure "No Key Vault references found in app settings"
        return 1
    fi
    
    # Check app service status
    APP_STATE=$(az webapp show \
        --name "$BACKEND_APP_NAME" \
        --resource-group "$RESOURCE_GROUP" \
        --query "state" \
        --output tsv)
    
    if [ "$APP_STATE" = "Running" ]; then
        print_success "Backend app service is running"
    else
        print_failure "Backend app service state: $APP_STATE"
        return 1
    fi
}

# Function to test health endpoints
test_health_endpoints() {
    print_header "Testing Health Endpoints"
    
    BACKEND_URL="https://${BACKEND_APP_NAME}.azurewebsites.net"
    
    print_status "Testing backend health endpoint: $BACKEND_URL/status"
    
    # Test /status endpoint
    if response=$(curl -s -w "HTTP_CODE:%{http_code}" "$BACKEND_URL/status" 2>/dev/null); then
        http_code=$(echo "$response" | grep -o "HTTP_CODE:[0-9]*" | cut -d: -f2)
        response_body=$(echo "$response" | sed 's/HTTP_CODE:[0-9]*$//')
        
        if [ "$http_code" = "200" ]; then
            print_success "Status endpoint responding (HTTP $http_code)"
            echo "Response: $response_body"
        else
            print_failure "Status endpoint returned HTTP $http_code"
            echo "Response: $response_body"
            return 1
        fi
    else
        print_failure "Could not connect to status endpoint"
        return 1
    fi
    
    # Test /health endpoint
    print_status "Testing backend health endpoint: $BACKEND_URL/health"
    
    if response=$(curl -s -w "HTTP_CODE:%{http_code}" "$BACKEND_URL/health" 2>/dev/null); then
        http_code=$(echo "$response" | grep -o "HTTP_CODE:[0-9]*" | cut -d: -f2)
        response_body=$(echo "$response" | sed 's/HTTP_CODE:[0-9]*$//')
        
        if [ "$http_code" = "200" ]; then
            print_success "Health endpoint responding (HTTP $http_code)"
            echo "Response: $response_body"
        else
            print_warning "Health endpoint returned HTTP $http_code"
            echo "Response: $response_body"
        fi
    else
        print_warning "Could not connect to health endpoint"
    fi
}

# Function to check application logs
check_application_logs() {
    print_header "Checking Application Logs"
    
    print_status "Fetching recent application logs..."
    
    # Get recent logs
    LOGS=$(az webapp log tail \
        --name "$BACKEND_APP_NAME" \
        --resource-group "$RESOURCE_GROUP" \
        --provider application \
        --max-events 20 2>/dev/null || echo "Could not fetch logs")
    
    if [[ "$LOGS" == *"Could not fetch logs"* ]]; then
        print_warning "Could not fetch application logs"
        print_status "Try: az webapp log tail --name $BACKEND_APP_NAME --resource-group $RESOURCE_GROUP"
    else
        echo "Recent logs:"
        echo "$LOGS"
        
        # Check for specific patterns
        if echo "$LOGS" | grep -q "OpenAI.*inicializ"; then
            print_success "OpenAI clients appear to be initializing"
        elif echo "$LOGS" | grep -q "mock"; then
            print_warning "Application is using mock OpenAI clients (credentials may be missing)"
        fi
        
        if echo "$LOGS" | grep -q "ERROR\|Error\|error"; then
            print_warning "Errors found in logs - check above output"
        fi
    fi
}

# Function to verify container registry
verify_container_registry() {
    print_header "Verifying Container Registry"
    
    if [ -n "$ACR_NAME" ]; then
        print_status "Checking container images in registry: $ACR_NAME"
        
        # List repositories
        REPOS=$(az acr repository list --name "$ACR_NAME" --output tsv 2>/dev/null || echo "")
        
        if [ -n "$REPOS" ]; then
            print_success "Container repositories found:"
            echo "$REPOS" | while read repo; do
                echo "  - $repo"
                # Get latest tag
                LATEST_TAG=$(az acr repository show-tags --name "$ACR_NAME" --repository "$repo" --orderby time_desc --top 1 --output tsv 2>/dev/null || echo "no-tags")
                echo "    Latest tag: $LATEST_TAG"
            done
        else
            print_warning "No container repositories found"
        fi
    else
        print_warning "Container registry not found"
    fi
}

# Function to run troubleshooting
run_troubleshooting() {
    print_header "Troubleshooting Guide"
    
    echo "If you're experiencing issues, try these steps:"
    echo
    echo "1. 🔐 Update OpenAI secrets:"
    echo "   ./scripts/manage-secrets.sh update"
    echo
    echo "2. 🔄 Restart the app service:"
    echo "   az webapp restart --name $BACKEND_APP_NAME --resource-group $RESOURCE_GROUP"
    echo
    echo "3. 📋 Check detailed logs:"
    echo "   az webapp log tail --name $BACKEND_APP_NAME --resource-group $RESOURCE_GROUP"
    echo
    echo "4. 🔍 Check Key Vault access:"
    echo "   az webapp identity show --name $BACKEND_APP_NAME --resource-group $RESOURCE_GROUP"
    echo
    echo "5. 🧪 Test endpoints manually:"
    echo "   curl -v https://${BACKEND_APP_NAME}.azurewebsites.net/status"
    echo "   curl -v https://${BACKEND_APP_NAME}.azurewebsites.net/health"
    echo
    echo "6. 📊 Check Application Insights:"
    echo "   Go to Azure Portal → Application Insights → filtro-curricular-insights-dev"
}

# Main verification function
main() {
    print_header "Filtro Curricular Deployment Verification"
    
    # Check prerequisites
    check_azure_login
    
    # Get resource names
    get_resource_names
    
    # Run verification steps
    VERIFICATION_PASSED=true
    
    if ! verify_keyvault_secrets; then
        VERIFICATION_PASSED=false
    fi
    
    if ! verify_app_service_config; then
        VERIFICATION_PASSED=false
    fi
    
    if ! test_health_endpoints; then
        VERIFICATION_PASSED=false
    fi
    
    # Always run these (informational)
    check_application_logs
    verify_container_registry
    
    # Summary
    echo
    print_header "Verification Summary"
    
    if [ "$VERIFICATION_PASSED" = true ]; then
        print_success "All critical verifications passed!"
        print_status "Your Filtro Curricular deployment appears to be working correctly."
    else
        print_failure "Some verifications failed."
        print_status "See troubleshooting guide below."
        echo
        run_troubleshooting
        exit 1
    fi
}

# Parse command line arguments
case "${1:-}" in
    "secrets")
        check_azure_login
        get_resource_names
        verify_keyvault_secrets
        ;;
    "health")
        check_azure_login
        get_resource_names
        test_health_endpoints
        ;;
    "logs")
        check_azure_login
        get_resource_names
        check_application_logs
        ;;
    "troubleshoot")
        check_azure_login
        get_resource_names
        run_troubleshooting
        ;;
    "help"|"--help"|"-h")
        echo "Usage: $0 [command]"
        echo
        echo "Commands:"
        echo "  (no command)  Run full verification"
        echo "  secrets       Check Key Vault secrets only"
        echo "  health        Test health endpoints only"
        echo "  logs          Check application logs only"
        echo "  troubleshoot  Show troubleshooting guide"
        echo "  help          Show this help message"
        ;;
    "")
        main
        ;;
    *)
        print_error "Unknown command: $1"
        print_status "Run '$0 help' for usage information"
        exit 1
        ;;
esac
