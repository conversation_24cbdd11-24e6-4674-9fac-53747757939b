#!/bin/bash

# Filtro Curricular Azure Deployment Script
# This script automates the deployment of applications to Azure

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    if ! command_exists terraform; then
        print_error "Terraform is not installed. Please install Terraform >= 1.0"
        exit 1
    fi
    
    if ! command_exists az; then
        print_error "Azure CLI is not installed. Please install Azure CLI"
        exit 1
    fi
    
    if ! command_exists docker; then
        print_error "Docker is not installed. Please install Docker"
        exit 1
    fi
    
    # Check if logged into Azure
    if ! az account show >/dev/null 2>&1; then
        print_error "Not logged into Azure. Please run 'az login'"
        exit 1
    fi
    
    print_success "All prerequisites met"
}

# Function to deploy infrastructure
deploy_infrastructure() {
    print_status "Deploying infrastructure with Terraform..."
    
    if [ ! -f "terraform.tfvars" ]; then
        print_error "terraform.tfvars file not found. Please copy from terraform.tfvars.example and configure"
        exit 1
    fi
    
    # Initialize Terraform
    print_status "Initializing Terraform..."
    terraform init
    
    # Plan deployment
    print_status "Planning Terraform deployment..."
    terraform plan -out=tfplan
    
    # Apply deployment
    print_status "Applying Terraform deployment..."
    terraform apply tfplan
    
    print_success "Infrastructure deployed successfully"
}

# Function to build and push Docker images
build_and_push_images() {
    print_status "Building and pushing Docker images..."
    
    # Get ACR details from Terraform output
    ACR_NAME=$(terraform output -raw container_registry_name)
    ACR_LOGIN_SERVER=$(terraform output -raw container_registry_login_server)
    
    print_status "Logging into Azure Container Registry: $ACR_NAME"
    az acr login --name "$ACR_NAME"
    
    # Build and push backend image
    print_status "Building backend image..."
    cd ../../../filtro-curricular/filtro-curricular-be-api
    
    BACKEND_IMAGE="$ACR_LOGIN_SERVER/filtro-curricular-be-api:latest"
    docker build -t "$BACKEND_IMAGE" .
    
    print_status "Pushing backend image..."
    docker push "$BACKEND_IMAGE"
    
    # Build and push frontend image
    print_status "Building frontend image..."
    cd ../filtro-curricular-fe-web
    
    FRONTEND_IMAGE="$ACR_LOGIN_SERVER/filtro-curricular-fe-web:latest"
    docker build -t "$FRONTEND_IMAGE" .
    
    print_status "Pushing frontend image..."
    docker push "$FRONTEND_IMAGE"
    
    # Return to infrastructure directory
    cd ../../infrastructure/azure/filtro-curricular
    
    print_success "Docker images built and pushed successfully"
}

# Function to restart App Services
restart_app_services() {
    print_status "Restarting App Services..."
    
    # Get resource details from Terraform output
    RESOURCE_GROUP=$(terraform output -raw resource_group_name)
    BACKEND_APP=$(terraform output -raw backend_app_service_name)
    FRONTEND_APP=$(terraform output -raw frontend_app_service_name)
    
    print_status "Restarting backend app service: $BACKEND_APP"
    az webapp restart --name "$BACKEND_APP" --resource-group "$RESOURCE_GROUP"
    
    print_status "Restarting frontend app service: $FRONTEND_APP"
    az webapp restart --name "$FRONTEND_APP" --resource-group "$RESOURCE_GROUP"
    
    print_success "App Services restarted successfully"
}

# Function to show deployment information
show_deployment_info() {
    print_status "Deployment Information:"
    echo ""
    
    echo "Resource Group: $(terraform output -raw resource_group_name)"
    echo "Container Registry: $(terraform output -raw container_registry_name)"
    echo "Backend URL: $(terraform output -raw backend_app_service_url)"
    echo "Frontend URL: $(terraform output -raw frontend_app_service_url)"
    echo ""
    
    print_success "Deployment completed successfully!"
    print_status "You can now access your applications at the URLs above"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --infrastructure-only    Deploy only infrastructure (skip Docker build/push)"
    echo "  --images-only           Build and push Docker images only (skip infrastructure)"
    echo "  --help                  Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                      Full deployment (infrastructure + images)"
    echo "  $0 --infrastructure-only Deploy only infrastructure"
    echo "  $0 --images-only        Build and push images only"
}

# Main deployment function
main() {
    local infrastructure_only=false
    local images_only=false
    
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --infrastructure-only)
                infrastructure_only=true
                shift
                ;;
            --images-only)
                images_only=true
                shift
                ;;
            --help)
                show_usage
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done
    
    # Check for conflicting options
    if [ "$infrastructure_only" = true ] && [ "$images_only" = true ]; then
        print_error "Cannot use --infrastructure-only and --images-only together"
        exit 1
    fi
    
    print_status "Starting Filtro Curricular Azure deployment..."
    
    check_prerequisites
    
    if [ "$images_only" = false ]; then
        deploy_infrastructure
    fi
    
    if [ "$infrastructure_only" = false ]; then
        build_and_push_images
        restart_app_services
    fi
    
    if [ "$images_only" = false ]; then
        show_deployment_info
    else
        print_success "Docker images updated successfully!"
    fi
}

# Run main function with all arguments
main "$@"
