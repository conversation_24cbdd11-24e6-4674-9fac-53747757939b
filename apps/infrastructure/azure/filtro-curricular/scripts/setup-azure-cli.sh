#!/bin/bash

# 🚀 Filtro Curricular Azure Setup - Complete CLI Automation Script
# This script automates the entire Azure setup process using Azure CLI

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo ""
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE} $1${NC}"
    echo -e "${BLUE}========================================${NC}"
    echo ""
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
check_prerequisites() {
    print_header "Checking Prerequisites"
    
    if ! command_exists az; then
        print_error "Azure CLI is not installed. Please install Azure CLI first."
        echo "Installation instructions: https://docs.microsoft.com/en-us/cli/azure/install-azure-cli"
        exit 1
    fi
    
    if ! command_exists jq; then
        print_warning "jq is not installed. Installing jq for JSON processing..."
        # Try to install jq based on OS
        if [[ "$OSTYPE" == "linux-gnu"* ]]; then
            sudo apt-get update && sudo apt-get install -y jq
        elif [[ "$OSTYPE" == "darwin"* ]]; then
            brew install jq
        else
            print_error "Please install jq manually: https://stedolan.github.io/jq/download/"
            exit 1
        fi
    fi
    
    # Check if logged into Azure
    if ! az account show >/dev/null 2>&1; then
        print_error "Not logged into Azure. Please run 'az login' first."
        exit 1
    fi
    
    print_success "All prerequisites met"
}

# Set up variables
setup_variables() {
    print_header "Setting Up Variables"
    
    # Get current subscription
    SUBSCRIPTION_ID=$(az account show --query id --output tsv)
    TENANT_ID=$(az account show --query tenantId --output tsv)
    
    # Set project variables
    SP_NAME="sp-filtro-curricular-github-actions"
    RESOURCE_GROUP_NAME="filtro-curricular-terraform-state-rg"
    STORAGE_ACCOUNT_NAME="filtrocurriculartfstate$(date +%s)"
    LOCATION="chilecentral"
    CONTAINER_NAME="terraform-state"
    
    print_status "Subscription ID: $SUBSCRIPTION_ID"
    print_status "Tenant ID: $TENANT_ID"
    print_status "Service Principal Name: $SP_NAME"
    print_status "Storage Account Name: $STORAGE_ACCOUNT_NAME"
}

# Register required resource providers
register_providers() {
    print_header "Registering Required Resource Providers"
    
    REQUIRED_PROVIDERS=(
        "Microsoft.Web"
        "Microsoft.ContainerRegistry"
        "Microsoft.KeyVault"
        "Microsoft.Storage"
        "Microsoft.Insights"
    )
    
    for provider in "${REQUIRED_PROVIDERS[@]}"; do
        print_status "Registering $provider..."
        az provider register --namespace "$provider"
    done
    
    print_status "Waiting for provider registration to complete..."
    sleep 10
    
    # Verify registration
    for provider in "${REQUIRED_PROVIDERS[@]}"; do
        state=$(az provider show --namespace "$provider" --query "registrationState" --output tsv)
        if [ "$state" = "Registered" ]; then
            print_success "$provider: Registered"
        else
            print_warning "$provider: $state (may take a few minutes)"
        fi
    done
}

# Create Service Principal
create_service_principal() {
    print_header "Creating Service Principal"
    
    # Check if Service Principal already exists
    if az ad sp list --display-name "$SP_NAME" --query "[0].id" --output tsv >/dev/null 2>&1; then
        print_warning "Service Principal '$SP_NAME' already exists. Skipping creation."
        SP_OBJECT_ID=$(az ad sp list --display-name "$SP_NAME" --query "[0].id" --output tsv)
        CLIENT_ID=$(az ad sp list --display-name "$SP_NAME" --query "[0].appId" --output tsv)
    else
        print_status "Creating Service Principal: $SP_NAME"
        
        # Create Service Principal with Contributor role
        SP_OUTPUT=$(az ad sp create-for-rbac \
            --name "$SP_NAME" \
            --role "Contributor" \
            --scopes "/subscriptions/$SUBSCRIPTION_ID" \
            --sdk-auth)
        
        # Extract values
        CLIENT_ID=$(echo "$SP_OUTPUT" | jq -r '.clientId')
        CLIENT_SECRET=$(echo "$SP_OUTPUT" | jq -r '.clientSecret')
        SP_OBJECT_ID=$(az ad sp list --display-name "$SP_NAME" --query "[0].id" --output tsv)
        
        print_success "Service Principal created successfully!"
        
        # Store the full output for GitHub secrets
        echo "$SP_OUTPUT" > sp_credentials.json
        print_status "Service Principal credentials saved to: sp_credentials.json"
    fi
}

# Assign additional roles
assign_roles() {
    print_header "Assigning Additional Roles"
    
    # Additional roles needed
    ADDITIONAL_ROLES=("Key Vault Administrator" "User Access Administrator")
    
    for role in "${ADDITIONAL_ROLES[@]}"; do
        print_status "Assigning '$role' role..."
        
        # Check if role is already assigned
        existing=$(az role assignment list \
            --assignee "$SP_OBJECT_ID" \
            --scope "/subscriptions/$SUBSCRIPTION_ID" \
            --query "[?roleDefinitionName=='$role']" \
            --output tsv)
        
        if [ -n "$existing" ]; then
            print_warning "$role already assigned"
        else
            az role assignment create \
                --assignee "$SP_OBJECT_ID" \
                --role "$role" \
                --scope "/subscriptions/$SUBSCRIPTION_ID"
            print_success "$role assigned successfully"
        fi
    done
}

# Create storage account for Terraform state
create_storage_account() {
    print_header "Creating Terraform State Storage Account"
    
    # Check if resource group exists
    if az group show --name "$RESOURCE_GROUP_NAME" >/dev/null 2>&1; then
        print_warning "Resource group '$RESOURCE_GROUP_NAME' already exists"
    else
        print_status "Creating resource group: $RESOURCE_GROUP_NAME"
        az group create \
            --name "$RESOURCE_GROUP_NAME" \
            --location "$LOCATION"
        print_success "Resource group created"
    fi
    
    # Check if storage account exists
    if az storage account show --name "$STORAGE_ACCOUNT_NAME" --resource-group "$RESOURCE_GROUP_NAME" >/dev/null 2>&1; then
        print_warning "Storage account '$STORAGE_ACCOUNT_NAME' already exists"
    else
        print_status "Creating storage account: $STORAGE_ACCOUNT_NAME"
        az storage account create \
            --name "$STORAGE_ACCOUNT_NAME" \
            --resource-group "$RESOURCE_GROUP_NAME" \
            --location "$LOCATION" \
            --sku "Standard_LRS" \
            --kind "StorageV2" \
            --access-tier "Hot"
        print_success "Storage account created"
    fi
    
    # Get storage account key
    STORAGE_KEY=$(az storage account keys list \
        --resource-group "$RESOURCE_GROUP_NAME" \
        --account-name "$STORAGE_ACCOUNT_NAME" \
        --query "[0].value" \
        --output tsv)
    
    # Create container
    print_status "Creating container: $CONTAINER_NAME"
    az storage container create \
        --name "$CONTAINER_NAME" \
        --account-name "$STORAGE_ACCOUNT_NAME" \
        --account-key "$STORAGE_KEY" \
        --public-access off
    
    # Assign storage permissions to Service Principal
    print_status "Assigning storage permissions to Service Principal..."
    az role assignment create \
        --assignee "$SP_OBJECT_ID" \
        --role "Storage Blob Data Contributor" \
        --scope "/subscriptions/$SUBSCRIPTION_ID/resourceGroups/$RESOURCE_GROUP_NAME/providers/Microsoft.Storage/storageAccounts/$STORAGE_ACCOUNT_NAME"
    
    print_success "Storage account setup completed"
}

# Generate configuration files
generate_config_files() {
    print_header "Generating Configuration Files"
    
    # Update backend.tf with actual storage account name
    print_status "Updating backend.tf configuration..."
    cat > backend.tf << EOF
# Terraform Backend Configuration for Azure Storage
terraform {
  backend "azurerm" {
    resource_group_name  = "$RESOURCE_GROUP_NAME"
    storage_account_name = "$STORAGE_ACCOUNT_NAME"
    container_name       = "$CONTAINER_NAME"
    key                  = "filtro-curricular-dev.tfstate"
  }
}
EOF
    
    # Generate GitHub secrets template
    print_status "Generating GitHub secrets template..."
    cat > github_secrets.json << EOF
{
  "AZURE_CREDENTIALS": $(cat sp_credentials.json),
  "OPENAI_API_KEY": "sk-your-openai-api-key-here",
  "OPENAI_TOKEN": "your-openai-token-here",
  "ASSISTANT_ID_JURIDICO": "asst_your-juridico-assistant-id",
  "ASSISTANT_ID_CALIDAD": "asst_your-calidad-assistant-id",
  "NOTIFICATION_EMAIL": "<EMAIL>",
  "TEAMS_WEBHOOK_URL": "https://your-company.webhook.office.com/webhookb2/...",
  "SMTP_USERNAME": "<EMAIL>",
  "SMTP_PASSWORD": "your-smtp-password"
}
EOF
    
    print_success "Configuration files generated"
}

# Verify setup
verify_setup() {
    print_header "Verifying Setup"
    
    # Test Service Principal login
    print_status "Testing Service Principal authentication..."
    if [ -f "sp_credentials.json" ]; then
        CLIENT_ID=$(jq -r '.clientId' sp_credentials.json)
        CLIENT_SECRET=$(jq -r '.clientSecret' sp_credentials.json)
        
        # Test login (will logout after test)
        if az login --service-principal \
            --username "$CLIENT_ID" \
            --password "$CLIENT_SECRET" \
            --tenant "$TENANT_ID" >/dev/null 2>&1; then
            print_success "Service Principal authentication successful"
            
            # Test resource creation
            if az group create --name "test-sp-permissions-$(date +%s)" --location "$LOCATION" >/dev/null 2>&1; then
                print_success "Service Principal has resource creation permissions"
                # Clean up test resource group
                az group delete --name "test-sp-permissions-$(date +%s)" --yes --no-wait >/dev/null 2>&1
            else
                print_warning "Service Principal may not have sufficient permissions"
            fi
            
            # Logout and login back with original account
            az logout >/dev/null 2>&1
            az login >/dev/null 2>&1
        else
            print_error "Service Principal authentication failed"
        fi
    fi
    
    # Verify storage account
    print_status "Verifying storage account access..."
    if az storage container show \
        --name "$CONTAINER_NAME" \
        --account-name "$STORAGE_ACCOUNT_NAME" >/dev/null 2>&1; then
        print_success "Storage account accessible"
    else
        print_error "Storage account not accessible"
    fi
}

# Display summary
display_summary() {
    print_header "Setup Summary"
    
    echo "✅ Azure setup completed successfully!"
    echo ""
    echo "📋 Summary:"
    echo "  • Subscription ID: $SUBSCRIPTION_ID"
    echo "  • Service Principal: $SP_NAME"
    echo "  • Storage Account: $STORAGE_ACCOUNT_NAME"
    echo "  • Resource Group: $RESOURCE_GROUP_NAME"
    echo ""
    echo "📁 Generated Files:"
    echo "  • sp_credentials.json - Service Principal credentials for GitHub secrets"
    echo "  • github_secrets.json - Template for all GitHub secrets"
    echo "  • backend.tf - Updated Terraform backend configuration"
    echo ""
    echo "🔑 Next Steps:"
    echo "  1. Copy the AZURE_CREDENTIALS from sp_credentials.json to GitHub secrets"
    echo "  2. Add your OpenAI credentials to GitHub secrets"
    echo "  3. Configure notification settings (email, Teams)"
    echo "  4. Run 'terraform init' to initialize the backend"
    echo "  5. Deploy your infrastructure with GitHub Actions"
    echo ""
    echo "📚 Documentation:"
    echo "  • Azure Portal Setup Guide: AZURE_PORTAL_SETUP_GUIDE.md"
    echo "  • GitHub Actions Setup: GITHUB_ACTIONS_SETUP.md"
    echo "  • Deployment Checklist: DEPLOYMENT_CHECKLIST.md"
    echo ""
    print_success "Setup completed! 🎉"
}

# Main execution
main() {
    print_header "Filtro Curricular Azure Setup"
    echo "This script will set up Azure Service Principal and Terraform backend"
    echo "for the Filtro Curricular project."
    echo ""
    
    # Confirm execution
    read -p "Do you want to continue? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "Setup cancelled."
        exit 0
    fi
    
    check_prerequisites
    setup_variables
    register_providers
    create_service_principal
    assign_roles
    create_storage_account
    generate_config_files
    verify_setup
    display_summary
}

# Run main function
main "$@"
