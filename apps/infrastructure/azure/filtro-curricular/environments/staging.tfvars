# Staging Environment Configuration
project_name = "filtro-curricular"
environment  = "staging"
location     = "chilecentral"

# Monitoring Configuration
monitoring_location = "eastus"

# Tags
tags = {
  Project     = "filtro-curricular"
  Environment = "staging"
  ManagedBy   = "terraform"
  Owner       = "Cognisearch"
  CostCenter  = "staging"
}

# Azure Container Registry
acr_sku = "Standard"

# App Service Plan - Mid-tier for staging
app_service_plan_sku = "S1"

# Application Configuration
backend_image_tag  = "staging-latest"
frontend_image_tag = "staging-latest"
backend_port       = 5000
frontend_port      = 80

# Storage Account
storage_account_tier             = "Standard"
storage_account_replication_type = "GRS"

# Scaling Configuration - Moderate for staging
backend_min_instances  = 1
backend_max_instances  = 3
frontend_min_instances = 1
frontend_max_instances = 3

# Networking - Basic security for staging
enable_vnet        = true
enable_app_gateway = false

# Security - Enhanced for staging
key_vault_network_access = "Allow"
enable_diagnostic_logs   = true

# Custom Domain - Optional for staging
custom_domain        = ""
ssl_certificate_path = ""

# Environment-specific secrets (will be set via GitHub secrets)
# These are placeholders - actual values come from GitHub environment secrets
openai_api_key        = "placeholder-will-be-set-by-github"
openai_token          = "placeholder-will-be-set-by-github"
assistant_id_juridico = "placeholder-will-be-set-by-github"
assistant_id_calidad  = "placeholder-will-be-set-by-github"
