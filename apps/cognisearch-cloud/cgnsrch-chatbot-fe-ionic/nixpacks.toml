# https://nixpacks.com/docs/configuration/file

# set up some variables to minimize annoyance
[variables]
    NPM_CONFIG_UPDATE_NOTIFIER = 'false' # the update notification is relatively useless in a production environment
    NPM_CONFIG_FUND = 'false' # the fund notification is also pretty useless in a production environment
    CADDY_VERSION = '2.7.5' # specify the caddy version to use here, without a 'v' prefix. https://github.com/caddyserver/caddy/releases
    # angular_output_path = 'www'

# setup phase where we add any needed packages
[phases.setup]
    nixPkgs = ['...', 'jq'] # jq is needed to parse the outputPath from our angular.json file

# get the output path from our angular.json file
[phases.output]
    dependsOn = ['setup']
    cmds = [
        'jq -r "[.projects[] | .architect.build.options.outputPath][0]" angular.json > angular_output_path' # save the resulting value into a file so that we can load the file into a variable in the final image
    ]

# download and untar caddy
[phases.caddy]
    dependsOn = ['setup'] # make sure this phase runs after the default 'setup' phase
    cmds = [
        'curl -fsSLo caddy.tar.gz "https://github.com/caddyserver/caddy/releases/download/v${CADDY_VERSION}/caddy_${CADDY_VERSION}_linux_amd64.tar.gz"', # download the caddy release specified by 'CADDY_VERSION' from GitHub
        'tar -zxvf caddy.tar.gz caddy', # only extract 'caddy' from the tarball
        'chmod +x caddy' # enable file execution for caddy, needed to execute downloaded files
    ]

# format the Caddyfile with fmt
[phases.fmt]
    dependsOn = ['caddy'] # make sure this phase runs after the 'caddy' phase so that we know we have caddy downloaded
    cmds = ['caddy fmt --overwrite Caddyfile'] # format the Caddyfile to fix any formatting inconsistencies

# start the caddy web server
[start]
    cmd = 'export ANGULAR_OUTPUT_PATH=$(cat angular_output_path); ./caddy run --config Caddyfile --adapter caddyfile 2>&1' # start caddy using the Caddyfile config and caddyfile adapter