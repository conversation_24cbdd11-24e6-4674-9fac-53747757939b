@echo off
setlocal EnableDelayedExpansion

:: Activar echo para ver los comandos que se ejecutan
echo on

:: Definir rutas absolutas
set "FRONTEND_PATH=C:\Users\<USER>\Desktop\PoC\HOSPITAL EL PINO\I+A+T+P HOSPITAL EL PINO\cognisearch-cloud\cgnsrch-chatbot-fe-ionic"
set "BACKEND_PATH=C:\Users\<USER>\Desktop\PoC\HOSPITAL EL PINO\I+A+T+P HOSPITAL EL PINO\cognisearch-cloud\cgnsrch-chatbot-be-pfapi\src"

:: Verificar que las rutas existan
echo Verificando rutas...
if not exist "!FRONTEND_PATH!" (
    echo Error: La ruta del frontend no existe: !FRONTEND_PATH!
    pause
    exit /b 1
)

if not exist "!BACKEND_PATH!" (
    echo Error: La ruta del backend no existe: !BACKEND_PATH!
    pause
    exit /b 1
)

:: Limpiar backend
echo Limpiando backend...
cd /d "!BACKEND_PATH!"
if exist venv (
    echo Eliminando entorno virtual anterior...
    rmdir /s /q venv
)

:: Limpiar node_modules y actualizar dependencias del frontend
echo Limpiando frontend...
cd /d "!FRONTEND_PATH!"
if exist node_modules (
    echo Eliminando node_modules anterior...
    rmdir /s /q node_modules
)

:: Actualizar npm y dependencias globales
echo Actualizando npm...
call npm install -g npm@latest
call npm install -g @ionic/cli@latest

:: Instalar dependencias del frontend
echo Instalando dependencias del frontend...
call npm install
if !errorlevel! neq 0 (
    echo Error al instalar dependencias del frontend
    pause
    exit /b 1
)

:: Intentar arreglar vulnerabilidades
echo Arreglando vulnerabilidades...
call npm audit fix
if !errorlevel! neq 0 (
    echo Advertencia: Algunas vulnerabilidades no se pudieron corregir
    echo Continuando de todas formas...
)

:: Iniciar el backend en una nueva ventana
echo Iniciando backend...
start cmd /k "cd /d "!BACKEND_PATH!" && call START_BACKEND.bat"

:: Esperar unos segundos para que el backend inicie
echo Esperando que el backend inicie...
timeout /t 5

:: Cambiar al directorio del frontend
echo Cambiando al directorio del frontend...
cd /d "!FRONTEND_PATH!"

:: Aplicación Ionic en la ventana actual
echo Iniciando la aplicación Ionic...
call ionic serve
if !errorlevel! neq 0 (
    echo Error al iniciar Ionic serve
    pause
    exit /b 1
)

pause