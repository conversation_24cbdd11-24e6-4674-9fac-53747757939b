/* src/app/login/login.page.scss */
:host {
  ion-content {
    --background: #0B1221;
    --color: white;
  }
}

/* Contenedor principal */
.page-container {
  min-height: 100vh;
  padding: 0 2rem;
  background-color: #0c4581;
}

/* Header styles */
.header-section {
  padding: 1.5rem 0;
  margin-bottom: 2rem;

  .header-content {
    max-width: 1200px;
    margin: 0 auto;
  }

  .brand {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
    width: fit-content;

    .logo-container {
      width: 5rem;
      height: 5rem;

      .logo {
        width: 100%;
        height: 100%;
        color: #3B82F6;
      }
    }

    .brand-name {
      font-size: 1.25rem;
      font-weight: 600;
      color: white;
    }
  }
}

/* Login Container */
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: calc(50vh - 200px);
  padding: 2rem 0;
}

/* Login Box */
.login-box {
    background: #f5f5f5f2;
    border-radius: 2rem;
    padding: 1.5rem;
    width: 100%;
    max-width: 470px;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    border: 1px solid rgb(255 255 255 / 80%);
	text-align: -webkit-center;

  h1 {
    color: #585e68;
    font-size: 1.5rem;
    font-weight: 600;
    text-align: center;
    margin-bottom: 2rem;
  }

  .form-group {
    margin-bottom: 1.5rem;
  }

  /* Custom input styling */
  .custom-input {
    --background: rgba(17, 25, 40, 0.7);
    --border-color: rgba(255, 255, 255, 0.1);
    --border-radius: 0.5rem;
    --border-width: 1px;
    --padding-start: 1rem;
    --padding-end: 1rem;
    --highlight-color: #3B82F6;
    margin-bottom: 1rem;
    border-radius: 0.5rem;
    
    &.item-has-focus {
      --border-color: #3B82F6;
    }

    ion-label {
      --color: rgba(255, 255, 255, 0.7);
    }

    ion-input {
      --color: white;
      --placeholder-color: rgba(255, 255, 255, 0.5);
    }
  }

  /* Submit button styling */
  .submit-btn {
    --background: #3B82F6;
    --background-hover: #2563EB;
    --border-radius: 0.5rem;
    --color: white;
    margin-top: 2rem;
    height: 3rem;
    font-weight: 500;
	width: fit-content;

    &:disabled {
      --background: rgba(59, 130, 246, 0.5);
      --opacity: 1;
    }

    ion-spinner {
      --color: white;
    }
  }
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .page-container {
    padding: 0 1rem;
  }

  .login-box {
    padding: 2rem 1.5rem;
  }
}

/* Dark mode specific styles */
@media (prefers-color-scheme: dark) {
  .login-box {
    background: rgba(30, 41, 59, 0.5);
  }
}