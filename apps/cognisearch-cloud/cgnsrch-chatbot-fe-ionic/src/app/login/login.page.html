<!-- src/app/login/login.page.html -->

  <div class="page-container">
    <!-- Header con Logo -->
    <header class="header-section">
      <div class="header-content">
        <div class="brand">
		  <div class="logo-container">
              <img src="assets/logo_hep.png" alt="Logo" class="client-login-logo" />
          </div>
          <span class="brand-name">Hospital y CRS - El Pino</span>
        </div>
      </div>
    </header>

    <!-- Login Container -->
    <div class="login-container">
      <div class="login-box">
        <h1>Testing Access</h1>
        
        <form [formGroup]="loginForm" (ngSubmit)="onSubmit()">
          <!-- Email Field -->
          <div class="form-group">
            <ion-item class="custom-input">
              <ion-label position="floating">Email</ion-label>
              <ion-input
                type="email"
                formControlName="email"
                [value]=""
                (ionClear)="resetForm()">
              </ion-input>
            </ion-item>
          </div>

          <!-- Password Field -->
          <div class="form-group">
            <ion-item class="custom-input">
              <ion-label position="floating">Password</ion-label>
            <ion-input
              type="password"
              formControlName="password"
              [value]=""
              (ionClear)="resetForm()">
            </ion-input>
            </ion-item>
          </div>

          <!-- Submit Button -->
          <ion-button 
            expand="block" 
            class="submit-btn"
            type="submit" 
            [disabled]="!loginForm.valid || isLoading">
            <ion-spinner *ngIf="isLoading"></ion-spinner>
            <span *ngIf="!isLoading">Pre-Production Testing Platform</span>
          </ion-button>
        </form>
      </div>
    </div>
  </div>