# shared/config.py

from pydantic import BaseModel
from typing import List, Dict, Optional
import json
from pathlib import Path

class AssistantAccess(BaseModel):
    id: str
    role: str

class User(BaseModel):
    email: str
    password: str
    assistants: List[AssistantAccess]
    currentAssistant: Optional[AssistantAccess] = None

class UserConfig:
    def __init__(self, config_path: str = "../services/auth.config.json"):
        self.config_path = Path(config_path)
        self._users: List[User] = []
        self.load_config()

    def load_config(self):
        """Carga la configuración desde el archivo JSON compartido"""
        try:
            if self.config_path.exists():
                with open(self.config_path, 'r') as f:
                    data = json.load(f)
                    self._users = [User(**user) for user in data['authorizedUsers']]
            else:
                print(f"⚠️ Config file not found at {self.config_path}")
        except Exception as e:
            print(f"❌ Error loading config: {e}")
            self._users = []

    def get_user_assistants(self, email: str) -> List[Dict[str, str]]:
        """Obtiene los asistentes disponibles para un usuario"""
        user = next((u for u in self._users if u.email == email), None)
        if user:
            return [{"id": a.id, "role": a.role} for a in user.assistants]
        return []

    def validate_user(self, email: str, password: str) -> bool:
        """Valida las credenciales del usuario"""
        return any(u.email == email and u.password == password for u in self._users)

    def get_all_assistants(self) -> List[Dict[str, str]]:
        """Obtiene todos los asistentes únicos del sistema"""
        assistants = set()
        for user in self._users:
            for assistant in user.assistants:
                assistants.add((assistant.id, assistant.role))
        return [{"id": id, "role": role} for id, role in assistants]