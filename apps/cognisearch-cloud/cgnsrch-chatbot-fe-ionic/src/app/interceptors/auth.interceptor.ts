// src/app/interceptors/auth.interceptor.ts

import { Injectable } from '@angular/core';
import { HttpInterceptor, HttpRequest, <PERSON>ttpHandler, HttpEvent } from '@angular/common/http';
import { Observable } from 'rxjs';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {
  intercept(request: HttpRequest<any>, next: <PERSON>ttpHand<PERSON>): Observable<HttpEvent<any>> {
    // Obtener el token del localStorage
    const token = localStorage.getItem('auth_token');
    const userData = localStorage.getItem('user_data');

    if (token && userData) {
      // Clonar la request y añadir los headers de autenticación
      request = request.clone({
        setHeaders: {
          Authorization: `Bearer ${token}`,
          'X-User-Data': userData
        }
      });
    }

    return next.handle(request);
  }
}