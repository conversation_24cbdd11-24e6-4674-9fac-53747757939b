import { Injectable } from '@angular/core';

@Injectable({ providedIn: 'root' })
export class EnvService {
  public BACKEND_URL: string = '';

  fetchEnv(): Promise<void> {
    console.log('Cargando Variables de Despliegues');
    return fetch('/assets/env.json') // Cambia esta URL si cargas las variables desde otro lugar.
      .then((response) => response.json())
      .then((env) => {
        this.BACKEND_URL = env.BACKEND_URL || 'pepito';
        console.log('Environment variables loaded:', env);
      })
      .catch((error) => {
        console.error('Error loading environment variables:', error);
      });
  }
}
