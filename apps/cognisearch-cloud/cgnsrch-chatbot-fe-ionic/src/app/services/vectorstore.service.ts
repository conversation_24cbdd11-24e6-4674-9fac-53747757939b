// vectorstore.service.ts

import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class VectorStoreService {
  private baseUrl = 'http://localhost:5000';

  constructor(private http: HttpClient) {}

  startSystemInspection(): Observable<any> {
    return this.http.get(`${this.baseUrl}/api/vectorstore/status`);
  }

  getInspectionStatus(taskId: string): Observable<any> {
    return this.http.get(`${this.baseUrl}/api/vectorstore/inspection/${taskId}`);
  }
}