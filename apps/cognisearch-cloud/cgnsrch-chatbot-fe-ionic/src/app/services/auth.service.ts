// src/app/services/auth.service.ts

import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { Router } from '@angular/router';
import config from './auth.config.json';

// Interfaces para el tipado de datos
interface AssistantAccess {
  id: string;
  role: string;
}

interface User {
  email: string;
  assistants: AssistantAccess[];
  currentAssistant?: AssistantAccess;
}

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  // Observables para mantener el estado de autenticación
  private isAuthenticatedSubject = new BehaviorSubject<boolean>(false);
  private currentUserSubject = new BehaviorSubject<User | null>(null);
  private currentAssistantSubject = new BehaviorSubject<AssistantAccess | null>(null);

  // Observables públicos
  public isAuthenticated$: Observable<boolean> = this.isAuthenticatedSubject.asObservable();
  public currentUser$: Observable<User | null> = this.currentUserSubject.asObservable();
  public currentAssistant$: Observable<AssistantAccess | null> = this.currentAssistantSubject.asObservable();

  constructor(private router: Router) {
    this.loadStoredSession();
  }

  getToken(): string {
    const token = localStorage.getItem('auth_token');
    return token || '';
  }

  isAuthenticated(): boolean {
    return this.isAuthenticatedSubject.value;
  }

  /**
   * Carga la sesión almacenada en localStorage al iniciar el servicio
   */
  private loadStoredSession(): void {
    try {
      const token = localStorage.getItem('auth_token');
      const userStr = localStorage.getItem('user_data');
      const currentAssistantStr = localStorage.getItem('current_assistant');
      
      if (token && userStr) {
        const user = JSON.parse(userStr);
        this.currentUserSubject.next(user);
        this.isAuthenticatedSubject.next(true);
        
        if (currentAssistantStr) {
          const assistant = JSON.parse(currentAssistantStr);
          this.currentAssistantSubject.next(assistant);
        } else if (user.assistants?.length > 0) {
          this.setCurrentAssistant(user.assistants[0]);
        }
      } else {
        this.clearSession();
      }
    } catch (error) {
      console.error('Error cargando sesión:', error);
      this.clearSession();
    }
  }

  /**
   * Intenta autenticar al usuario con email y contraseña
   */
  login(email: string, password: string): boolean {
    try {
      const user = config.authorizedUsers.find(
        u => u.email === email && u.password === password
      );

      if (user) {
        const userData = {
          email: user.email,
          assistants: user.assistants
        };

        localStorage.setItem('auth_token', 'auth-token-' + user.email);
        localStorage.setItem('user_data', JSON.stringify(userData));
        
        if (user.assistants.length > 0) {
          this.setCurrentAssistant(user.assistants[0]);
        }
        
        this.currentUserSubject.next(userData);
        this.isAuthenticatedSubject.next(true);
        return true;
      }
    } catch (error) {
      console.error('Error durante el login:', error);
      this.clearSession();
    }
    return false;
  }

  /**
   * Cierra la sesión del usuario actual
   */
  logout(): void {
    this.clearSession();
    this.router.navigate(['/login']);
  }

  /**
   * Limpia todos los datos de sesión
   */
  private clearSession(): void {
    localStorage.removeItem('auth_token');
    localStorage.removeItem('user_data');
    localStorage.removeItem('current_assistant');
    this.currentUserSubject.next(null);
    this.currentAssistantSubject.next(null);
    this.isAuthenticatedSubject.next(false);
  }

  /**
   * Establece el asistente activo para el usuario actual
   * @param assistant Asistente a establecer como activo
   */
  setCurrentAssistant(assistant: AssistantAccess): void {
    localStorage.setItem('current_assistant', JSON.stringify(assistant));
    this.currentAssistantSubject.next(assistant);
  }

  /**
   * Obtiene el usuario actualmente autenticado
   * @returns User | null Usuario actual o null si no hay sesión
   */
  getCurrentUser(): User | null {
    return this.currentUserSubject.value;
  }

  /**
   * Obtiene el asistente actualmente seleccionado
   * @returns AssistantAccess | null Asistente actual o null si no hay ninguno seleccionado
   */
  getCurrentAssistant(): AssistantAccess | null {
    return this.currentAssistantSubject.value;
  }

  /**
   * Obtiene la lista de asistentes disponibles para el usuario actual
   * @returns AssistantAccess[] Lista de asistentes disponibles
   */
  getAvailableAssistants(): AssistantAccess[] {
    const user = this.currentUserSubject.value;
    return user ? user.assistants : [];
  }

  /**
   * Verifica si el usuario actual tiene un rol específico
   * @param role Rol a verificar
   * @returns boolean Indica si el usuario tiene el rol especificado
   */
  hasRole(role: string): boolean {
    const user = this.currentUserSubject.value;
    return user ? user.assistants.some(a => a.role === role) : false;
  }
  
  /**
   * Verifica si la sesión actual es válida
   */
  validateSession(): boolean {
    const token = this.getToken();
    const user = this.getCurrentUser();
    
    if (!token || !user) {
      this.clearSession();
      return false;
    }
    
    return true;
  }
}