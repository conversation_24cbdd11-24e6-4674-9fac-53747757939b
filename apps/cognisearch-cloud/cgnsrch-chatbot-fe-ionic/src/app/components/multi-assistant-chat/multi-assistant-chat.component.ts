// src/app/components/multi-assistant-chat/multi-assistant-chat.component.ts

import { Component, OnInit } from '@angular/core';
import { AuthService } from '../../services/auth.service';
import { HttpClient } from '@angular/common/http';
import { environment } from '../../../environments/environment';

interface AssistantResponse {
  assistant_id: string;
  role: string;
  content?: string;
  error?: string;
  status: string;
}

@Component({
  selector: 'app-multi-assistant-chat',
  template: `
    <ion-content>
      <ion-grid>
        <ion-row>
          <ion-col size="12">
            <ion-item>
              <ion-input 
                [(ngModel)]="query" 
                placeholder="Escribe tu pregunta..."
                (keyup.enter)="sendQuery()"
              ></ion-input>
              <ion-button slot="end" (click)="sendQuery()">
                <ion-icon name="send"></ion-icon>
              </ion-button>
            </ion-item>
          </ion-col>
        </ion-row>

        <!-- Selector de asistentes -->
        <ion-row>
          <ion-col size="12">
            <ion-item>
              <ion-label>Seleccionar Asistentes</ion-label>
              <ion-select [(ngModel)]="selectedAssistants" multiple="true">
                <ion-select-option *ngFor="let assistant of availableAssistants" 
                                 [value]="assistant.id">
                  {{getRoleName(assistant.role)}}
                </ion-select-option>
              </ion-select>
            </ion-item>
          </ion-col>
        </ion-row>

        <!-- Respuestas de los asistentes -->
        <ion-row>
          <ion-col size="12" *ngFor="let response of responses">
            <ion-card [ngClass]="{'response-complete': response.status === 'completed'}">
              <ion-card-header>
                <ion-card-subtitle>
                  {{getRoleName(response.role)}}
                  <ion-badge [color]="getStatusColor(response.status)">
                    {{response.status}}
                  </ion-badge>
                </ion-card-subtitle>
              </ion-card-header>
              <ion-card-content>
                <div *ngIf="response.content" [innerHTML]="response.content"></div>
                <div *ngIf="response.error" class="error-message">
                  {{response.error}}
                </div>
                <div *ngIf="response.status !== 'completed' && !response.error" 
                     class="loading-indicator">
                  <ion-spinner></ion-spinner>
                </div>
              </ion-card-content>
            </ion-card>
          </ion-col>
        </ion-row>
      </ion-grid>
    </ion-content>
  `,
  styles: [`
    .response-complete {
      border-left: 4px solid var(--ion-color-success);
    }
    .error-message {
      color: var(--ion-color-danger);
    }
    .loading-indicator {
      display: flex;
      justify-content: center;
      padding: 1rem;
    }
  `]
})
export class MultiAssistantChatComponent implements OnInit {
  query: string = '';
  availableAssistants: Array<{id: string, role: string}> = [];
  selectedAssistants: string[] = [];
  responses: AssistantResponse[] = [];
  private eventSource: EventSource | null = null;

  constructor(
    private authService: AuthService,
    private http: HttpClient
  ) {}

  ngOnInit() {
    this.availableAssistants = this.authService.getAvailableAssistants();
    // Por defecto, seleccionar todos los asistentes
    this.selectedAssistants = this.availableAssistants.map(a => a.id);
  }

  getRoleName(role: string): string {
    const roleNames: {[key: string]: string} = {
      'direccion': 'Dirección',
      'juridico': 'Jurídico',
      'calidad': 'Calidad',
      'qa': 'QA'
    };
    return roleNames[role] || role;
  }

  getStatusColor(status: string): string {
    switch (status) {
      case 'completed': return 'success';
      case 'error': return 'danger';
      case 'in_progress': return 'warning';
      default: return 'medium';
    }
  }

  async sendQuery() {
    if (!this.query.trim()) return;

    // Limpiar respuestas anteriores
    this.responses = [];
    
    // Cerrar conexión SSE anterior si existe
    if (this.eventSource) {
      this.eventSource.close();
    }

    const user = this.authService.getCurrentUser();
    if (!user) return;

    // Inicializar las respuestas para todos los asistentes seleccionados
    this.selectedAssistants.forEach(id => {
      const assistant = this.availableAssistants.find(a => a.id === id);
      if (assistant) {
        this.responses.push({
          assistant_id: id,
          role: assistant.role,
          status: 'starting'
        });
      }
    });

    // Crear nueva conexión SSE
    this.eventSource = new EventSource(
      `${environment.apiUrl}/ask/multi/stream?` + 
      `query=${encodeURIComponent(this.query)}` +
      `&email=${encodeURIComponent(user.email)}` +
      `&assistant_ids=${encodeURIComponent(JSON.stringify(this.selectedAssistants))}`
    );

    this.eventSource.onmessage = (event) => {
      const data = JSON.parse(event.data);
      
      // Actualizar la respuesta correspondiente
      const index = this.responses.findIndex(r => r.assistant_id === data.assistant_id);
      if (index !== -1) {
        this.responses[index] = { ...this.responses[index], ...data };
      }
    };

    this.eventSource.onerror = (error) => {
      console.error('SSE Error:', error);
      if (this.eventSource) {
        this.eventSource.close();
        this.eventSource = null;
      }
    };
  }

  ngOnDestroy() {
    if (this.eventSource) {
      this.eventSource.close();
    }
  }
}