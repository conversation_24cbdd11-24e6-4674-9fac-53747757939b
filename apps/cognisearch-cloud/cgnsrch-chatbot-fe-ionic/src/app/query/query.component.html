<div class="app-container" [class.sidebar-closed]="!isSidebarOpen">
    <!-- Sidebar Content -->
    <div class="sidebar-content-wrapper">
	
        <!-- Logout button -->
        <div class="header-container">
          <div class="logout-container">
            <ion-button fill="clear" 
                        size="small" 
                        class="logout-button"
                        (click)="logout()"
                        title="Cerrar Sesión">
              <ion-icon name="log-out-outline"></ion-icon>
            </ion-button>
          </div>
          <!-- Upload documents button -->
          <div class="upload-container">
            <ion-button fill="clear" 
                        size="small" 
                        class="upload-button"
                        (click)="uploadDocuments()"
                        title="Subir documentos">
              <ion-icon name="cloud-upload-outline"></ion-icon>
            </ion-button>
          </div>
        </div>
		
        <!-- Footer con versión aquí para mejorar UX -->
        <div class="version-footer">
            <span class="version-text">v2.02 --Pulse</span>
        </div>
		
        <div class="logo-container">
            <img src="assets/logo.png" alt="Logo" class="sidebar-logo" />
        </div>

        <!-- Mode Select -->
        <div class="mode-switch-container">
            <label class="toggle-switch" title="Selecciona el modo: Trabajo o Casual">
                <input type="checkbox" 
                       id="modeToggle"
                       name="modeToggle"
                       [(ngModel)]="isCasualMode"
                       (change)="onModeChange($event)"
                       aria-label="Selector de modo de chat">
                <span class="slider">
                    <span class="mode-text casual" [class.active]="isCasualMode">Casual</span>
                    <span class="mode-text work" [class.active]="!isCasualMode">Trabajo</span>
                </span>
            </label>
        </div>

        <!-- Warning section 1 -->
        <div class="warning-section1">
            <span class="warning-text1">⚠️ Chat Casual no se guarda en el historial de la sesión.</span>
        </div>

        <!-- Historial de Preguntas -->
        <div class="sidebar-content">
            <div class="question-list">
                <!-- Header con botón de limpiar -->
                <div class="history-header" *ngIf="multiResponses.length > 0">
                    <div class="history-title">Historial de Consultas</div>
                    <ion-button fill="clear" size="small" (click)="clearHistory()" class="clear-history-btn">
                        <ion-icon name="trash-outline"></ion-icon>
                    </ion-button>
                </div>
                <!-- Lista de preguntas -->
                <ion-list>
                    <ion-item-sliding *ngFor="let pregunta of multiResponses; let i = index">
                        <ion-item class="question-item">
                            <div class="question-content">
                                <!-- Cabecera de pregunta -->
                                <div class="question-header" (click)="toggleExpanded(pregunta)">
                                    <div class="header-content">
                                        <span class="question-number">#{{multiResponses.length - i}}</span>
                                        <div class="question-text" [innerHTML]="formatText(pregunta.question)"></div>
                                    </div>
                                    <ion-icon [name]="pregunta.isExpanded ? 'chevron-up' : 'chevron-down'" class="expand-icon" [class.rotated]="pregunta.isExpanded"></ion-icon>
                                </div>
                                <!-- Contenedor de respuestas expandible -->
                                <div class="response-container" [class.expanded]="pregunta.isExpanded">
                                    <div *ngFor="let response of pregunta.responses" class="multi-response-content">
                                        <div class="response-header">
                                            <strong>{{getRoleName(response.role)}}</strong>
                                        </div>
                                        <div class="response-content" 
                                             [innerHTML]="sanitizeHtmlContent(response.content)" 
                                             (click)="handleDocumentClick($event)">
                                        </div>
                                    </div>
                                    <div class="response-footer">
                                        <small class="timestamp">{{pregunta.timestamp | date:'yyyy-MM-dd HH:mm'}}</small>
                                        <div class="response-actions">
                                            <ion-button 
                                                fill="clear" 
                                                size="small" 
                                                (click)="copyResponse(pregunta)" 
                                                class="action-button"
                                                title="Copiar respuesta">
                                                <ion-icon name="copy-outline"></ion-icon>
                                            </ion-button>
                                            <ion-button 
                                                fill="clear" 
                                                size="small" 
                                                (click)="shareResponse(pregunta)" 
                                                class="action-button"
                                                title="Compartir respuesta">
                                                <ion-icon name="share-social-outline"></ion-icon>
                                            </ion-button>
                                            <ion-button 
                                                fill="clear" 
                                                size="small" 
                                                (click)="downloadAsDocx(pregunta)" 
                                                class="action-button"
                                                title="Descargar como documento">
                                                <ion-icon name="document-text-outline"></ion-icon>
                                            </ion-button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </ion-item>
                        <!-- Opciones deslizables -->
                        <ion-item-options side="end">
                            <ion-item-option color="danger" (click)="deleteQuestion(i)" class="delete-option">
                                <ion-icon name="trash-outline"></ion-icon>
                            </ion-item-option>
                            <ion-item-option color="primary" (click)="reuseQuestion(pregunta)" class="reuse-option">
                                <ion-icon name="repeat-outline"></ion-icon>
                            </ion-item-option>
                        </ion-item-options>
                    </ion-item-sliding>
                </ion-list>
                <!-- Estado vacío -->
                <div class="no-questions" *ngIf="!multiResponses.length">
                    <ion-icon name="chatbubbles-outline" class="empty-icon"></ion-icon>
                    <p class="empty-text">No hay consultas previas</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Botón toggle del sidebar -->
    <button class="sidebar-toggle" (click)="toggleSidebar()">
        <ion-icon [name]="isSidebarOpen ? 'chevron-back-outline' : 'chevron-forward-outline'"></ion-icon>
    </button>

    <!-- Main Content -->
    <div id="main-content" class="main-content">
        <div class="description">
            <h2 class="section-title">Hospital y CRS El Pino</h2>
            <p class="model-description">
                Esta sección utiliza estríctamente los archivos de la base de conocimiento en las respuestas.
            </p>
        </div>

        <!-- Selector de Asistentes -->
        <div class="assistant-selector" *ngIf="!isCasualMode && availableAssistants.length > 1">
            <ion-item lines="none" class="selector-item">
                <ion-label>Seleccionar Asistentes</ion-label>
                <ion-select [(ngModel)]="selectedAssistants" 
                           multiple="true" 
                           interface="popover"
                           class="assistant-select">
                    <ion-select-option *ngFor="let assistant of availableAssistants" 
                                     [value]="assistant.id">
                        {{getRoleName(assistant.role)}}
                    </ion-select-option>
                </ion-select>
            </ion-item>
        </div>

        <!-- Text Area Container -->
        <div class="textarea-container">
            <ion-textarea [(ngModel)]="userQuery" 
                         rows="4" 
                         placeholder="Escriba su consulta aquí">
            </ion-textarea>
        </div>

        <!-- Warning Section 2 -->
        <div class="warning-section2">
            <span class="warning-text2">
                El asistente puede cometer errores. Asegúrate de verificar la información importante.
            </span>
        </div>

        <!-- Submit Button -->
        <div class="wrap-out-button">
            <button class="wrap-button" 
                    (click)="handleSubmit()" 
                    [disabled]="!userQuery.trim() || isLoading">
                <span *ngIf="!isLoading">Preguntar</span>
                <ion-spinner *ngIf="isLoading" name="crescent" class="loading-spinner"></ion-spinner>
            </button>
        </div>

        <!-- Error Message -->
        <div class="error-message" *ngIf="errorMessage">
            {{ errorMessage }}
        </div>

        <!-- Respuestas en Tiempo Real -->
        <div *ngIf="assistantResponses.length > 0" class="responses-container">
            <ion-grid>
                <ion-row>
                    <ion-col size="12" 
                            sizeMd="12"
                            sizeLg="12"
                            sizeXl="12"
                            *ngFor="let response of assistantResponses">
                        <!-- Agregar condición especial para mensaje del sistema -->
                        <ion-card class="response-card" 
                                 [ngClass]="{
                                     'complete': response.status === 'completed',
                                     'system-message': response.assistant_id === 'system'
                                 }">
                            <ion-card-header *ngIf="response.assistant_id !== 'system'">
                                <!-- Header normal existente -->
                                <ion-card-subtitle class="response-header">
                                    <div class="response-title">
                                        {{getRoleName(response.role)}}
                                    </div>
                                    <div class="response-meta">
                                        <span class="response-time" *ngIf="response.responseTime">
                                            {{response.responseTime}} s
                                        </span>
                                        <ion-badge [color]="getStatusColor(response.status)">
                                            {{response.status}}
                                        </ion-badge>
                                    </div>
                                </ion-card-subtitle>
                            </ion-card-header>
                            <ion-card-content>
                                <div *ngIf="response.content" 
                                     [innerHTML]="sanitizeHtmlContent(response.content)"
                                     (click)="handleDocumentClick($event)"
                                     class="response-content"
                                     [ngClass]="{'system-content': response.assistant_id === 'system'}">
                                </div>
                                <div *ngIf="response.error" class="error-message">
                                    {{response.error}}
                                </div>
                                <ion-spinner *ngIf="!response.content && !response.error" 
                                             name="crescent">
                                </ion-spinner>
                            
                                <!-- Botones de acción para modo Casual y mensaje del sistema -->
                                <div class="response-actions" 
                                     *ngIf="(isCasualMode || response.assistant_id === 'system') && response.content">
                                    <ion-button 
                                        fill="clear" 
                                        size="small" 
                                        (click)="copyResponse(response)" 
                                        class="action-button"
                                        title="Copiar respuesta">
                                        <ion-icon name="copy-outline"></ion-icon>
                                    </ion-button>
                                    <ion-button 
                                        fill="clear" 
                                        size="small" 
                                        (click)="shareResponse(response)" 
                                        class="action-button"
                                        title="Compartir respuesta">
                                        <ion-icon name="share-social-outline"></ion-icon>
                                    </ion-button>
                                </div>
                            </ion-card-content>
                        </ion-card>
                    </ion-col>
                </ion-row>
            </ion-grid>
        </div>

        <!-- Document Modal -->
        <div class="document-modal" *ngIf="showDocumentModal" (click)="closeDocumentModal($event)">
            <div class="modal-content" (click)="$event.stopPropagation()">
                <div class="modal-header">
                    <h3>{{ currentDocument?.filename }}</h3>
                    <div class="modal-actions">
                        <ion-button fill="clear" (click)="closeDocumentModal()">
                            <ion-icon name="close-outline"></ion-icon>
                        </ion-button>
                    </div>
                </div>
                <div class="modal-body">
                    <iframe [src]="documentUrl" frameborder="0"></iframe>
                </div>
            </div>
        </div>
    </div>
</div>