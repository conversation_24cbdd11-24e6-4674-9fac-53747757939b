// query.component.ts

import { NgModule } from '@angular/core';
import { IonicModule } from '@ionic/angular';
import { Component, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { AuthService } from '../services/auth.service';
import { Router, RouterModule } from '@angular/router';
import { CommonModule, DatePipe } from '@angular/common';
import { environment } from '../../environments/environment';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { AlertController, ToastController } from '@ionic/angular';
import { FormBuilder, FormGroup, FormArray, Validators } from '@angular/forms';
import { DomSanitizer, SafeHtml, SafeResourceUrl } from '@angular/platform-browser';
import { Document, Packer, Paragraph, TextRun, HeadingLevel, AlignmentType } from 'docx';

interface AssistantResponse {
  assistant_id: string;
  role: string;
  content?: string;
  error?: string;
  status: string;
  responseTime?: number;
}

interface MultiResponse {
  question: string;
  responses: AssistantResponse[];
  timestamp: Date;
  isExpanded: boolean;
  response: string;  // Compatibilidad con PreguntaInterface
  responseTime: number;  // Compatibilidad con PreguntaInterface
}

interface PreguntaInterface {
  question: string;
  response: string;
  isExpanded: boolean;
  timestamp: Date;
  responseTime: number;
}

@Component({
  selector: 'app-query',
  templateUrl: './query.component.html',
  styleUrls: ['./query.component.scss'],
  providers: [DatePipe]
})
export class QueryComponent implements OnInit {
  // Propiedades
  availableAssistants: Array<{id: string, role: string}> = [];
  selectedAssistants: string[] = [];
  assistantResponses: AssistantResponse[] = [];
  multiResponses: MultiResponse[] = [];

  userQuery: string = '';
  response: string = '';
  streamingContent: string = '';
  isStreaming: boolean = false;
  preguntas: PreguntaInterface[] = [];
  isLoading: boolean = false;
  errorMessage: string = '';
  isSidebarOpen: boolean = true;
  selectedMode: string = 'trabajo';
  apiUrl: string = 'http://localhost:5000/ask';
  safeHtmlContent: SafeHtml | null = null;
  showMetadata: boolean = false;
  documentMetadata: any = {};
  responseTime: number = 0;
  isDocumentsPanelOpen: boolean = false;
  selectedDocumentType: string | null = null;
  selectedTab: string = 'familia';
  showDocumentModal = false;
  currentDocument: { filename: string; url: string } | null = null;
  documentUrl: SafeResourceUrl | string = '';
  isViewableDocument = false;
  isDocOrDocx = false;  
  isCasualMode: boolean = false;

  private isInCasualMode: boolean = false;
  private scrollLock: boolean = false;  
  private currentStartTime: number = 0;
  private responseContainer: HTMLElement | null = null;
  private scrollTimeout: any = null;
  private isScrolling: boolean = false;
  private lastScrollTime: number = 0;
  private scrollDebounceTime: number = 500;
  private lastContentLength: number = 0;
  private minContentDelta: number = 50;
  
  constructor(
    private http: HttpClient,
    private sanitizer: DomSanitizer,
    private alertController: AlertController,
    private toastController: ToastController,
    private router: Router,
    private authService: AuthService
  ) {
    console.log('====== DEBUG ENVIRONMENT ======');
    console.log('Environment:', environment);
    console.log('Environment production:', environment.production);
    console.log('BACKEND_URL:', environment.BACKEND_URL);
    console.log('this.apiUrl antes:', this.apiUrl);
    this.apiUrl = environment.BACKEND_URL.replace(/\/+$/, ''); 
    console.log('this.apiUrl después:', this.apiUrl);
    console.log('====== END DEBUG ======');
  }
 
  ngOnInit() {
    this.availableAssistants = this.authService.getAvailableAssistants();
    this.selectedAssistants = [...this.availableAssistants.map(a => a.id)];
    // Obtener referencia al contenedor de respuestas
    this.responseContainer = document.querySelector('.responses-container');
  }

  getStatusColor(status: string): string {
    const statusColors: { [key: string]: string } = {
      'iniciando': 'light',      // Gris claro para estado inicial
      'completado': 'success',   // Verde para éxito
      'en_proceso': 'warning',   // Amarillo para proceso
      'fallido': 'danger',       // Rojo para error
      'expirado': 'danger',      // Rojo para error
      'cancelado': 'medium',     // Gris medio para cancelado
      'error': 'danger',         // Rojo para error
      'requiere_acción': 'primary'  // Azul para acción requerida
    };
    return statusColors[status] || 'medium';
  }
  
  sanitizeHtmlContent(content: string | undefined): SafeHtml {
    if (!content) return '';
    return this.sanitizer.bypassSecurityTrustHtml(content);
  }

  getRoleName(role: string): string {
    const roleNames: {[key: string]: string} = {
      'direccion': 'Dirección',
      'juridico': 'Jurídico',
      'calidad': 'Calidad',
      'qa': 'QA',
      'casual': 'Casual'  // Agregamos el rol casual con mayúscula inicial
    };
    return roleNames[role] || role.charAt(0).toUpperCase() + role.slice(1).toLowerCase();
  }

  async logout(): Promise<void> {
      const alert = await this.alertController.create({
          header: 'Confirmar Cierre de Sesión',
          message: '¿Está seguro que desea cerrar la sesión?',
          buttons: [
              {
                  text: 'Cancelar',
                  role: 'cancel'
              },
              {
                  text: 'Cerrar Sesión',
                  handler: () => {
                      // Limpiar datos locales
                      this.multiResponses = [];
                      this.preguntas = [];
                      this.assistantResponses = [];
                      
                      // Cerrar sesión en el servicio de autenticación
                      this.authService.logout();
                      
                      // Mostrar mensaje de confirmación
                      this.toastController.create({
                          message: 'Sesión cerrada correctamente',
                          duration: 2000,
                          position: 'bottom',
                          color: 'success'
                      }).then(toast => toast.present());
                      
                      // Redireccionar al login
                      this.router.navigate(['/login']);
                  }
              }
          ]
      });
  
      await alert.present();
  }

  handleDocumentClick(event: MouseEvent): void {
      const target = event.target as HTMLElement;
      const documentLink = target.closest('.document-link') as HTMLElement;
      
      if (documentLink) {
          event.preventDefault();
          event.stopPropagation();
          
          const url = documentLink.getAttribute('data-url');
          const filename = documentLink.getAttribute('data-filename');
          
          if (url && filename) {
              // Ocultar el sidebar
              this.isSidebarOpen = false;
              
              const extension = filename.toLowerCase().split('.').pop();
              const isOfficeDoc = ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'].includes(extension || '');
              const isPdf = extension === 'pdf';
              
              if (isPdf) {
                  this.openDocumentModal(url, filename);
              } else if (isOfficeDoc) {
                  const officeUrl = `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(url)}`;
                  this.openDocumentModal(officeUrl, filename);
              } else {
                  window.open(url, '_blank');
              }
          }
      }
  }
  
  openDocumentModal(url: string, filename: string): void {
      this.documentUrl = this.sanitizer.bypassSecurityTrustResourceUrl(url);
      this.currentDocument = { url, filename };
      this.showDocumentModal = true;
  }

  // Cierra el modal de documentos
  closeDocumentModal(event?: Event) {
    if (event) event.stopPropagation();
    this.showDocumentModal = false;
  }

  // Detecta si el documento es PDF, DOC o DOCX.
  checkDocumentType() {
      if (!this.currentDocument || !this.currentDocument.filename || !this.currentDocument.url) {
          return;
      }
      const extension = this.currentDocument.filename.split('.').pop()?.toLowerCase();
      
      if (extension === 'pdf') {
          this.isViewableDocument = true;
          this.isDocOrDocx = false;
      } else if (extension === 'doc' || extension === 'docx') {
          this.isViewableDocument = false;
          this.isDocOrDocx = true;
          this.documentUrl = this.sanitizer.bypassSecurityTrustResourceUrl(
              `https://docs.google.com/gview?url=${this.currentDocument.url}&embedded=true`
          );
      } else {
          this.isViewableDocument = false;
          this.isDocOrDocx = false;
      }
  }

  // Descarga el documento actual sin errores de seguridad
  downloadDocument() {
    if (this.documentUrl) {
      const safeUrl = this.sanitizer.sanitize(1, this.documentUrl); // Sanitiza SafeResourceUrl a string
      if (safeUrl) {
        window.open(safeUrl, '_blank');
      }
    }
  }

  // Sidebar features
  toggleSidebar(): void {
    this.isSidebarOpen = !this.isSidebarOpen;
  }

  toggleDocumentsPanel(): void {
    this.isDocumentsPanelOpen = !this.isDocumentsPanelOpen;
    if (!this.isDocumentsPanelOpen) {
      this.selectedDocumentType = null;
    }
  }

  selectTab(tab: string): void {
    this.selectedTab = tab;
    this.selectedDocumentType = null;
  }
    
  onModeChange(event: Event): void {
      const isChecked = (event.target as HTMLInputElement).checked;
      this.isCasualMode = isChecked;
      this.selectedMode = this.isCasualMode ? 'casual' : 'trabajo';
      
      // Limpiar las respuestas principales
      this.assistantResponses = [];  // Esto limpia las respuestas en el área principal
      this.userQuery = '';          // Opcional: también limpiar la consulta actual
      
      const toastMessage = this.isCasualMode
          ? 'Modo Casual seleccionado: Las respuestas no se guardarán en el historial'
          : 'Modo Trabajo seleccionado: Todas sus preguntas y respuestas serán guardadas solo en la SESIÓN como recordatorio.';
          
      const toastColor = this.isCasualMode ? 'light' : 'secondary';
      
      this.toastController.create({
          message: toastMessage,
          duration: 5000,
          position: 'bottom',
          color: toastColor,
          buttons: [
              {
                  text: 'OK',
                  role: 'cancel'
              }
          ]
      }).then(toast => toast.present());
  }

  async handleSubmit(): Promise<void> {
      console.log('====== INICIANDO SOLICITUD ======');
      console.log('🔵 Backend URL:', this.apiUrl);
      console.log('🔵 Modo:', this.selectedMode);
      console.log('🔵 Timestamp:', new Date().toISOString());
      
      if (!this.userQuery.trim()) {
          const toast = await this.toastController.create({
              message: "Por favor, ingrese una pregunta válida.",
              duration: 2000,
              position: 'bottom'
          });
          await toast.present();
          return;
      }
  
      this.isLoading = true;
      this.errorMessage = '';
      this.assistantResponses = [];
      this.isStreaming = true;
      this.currentStartTime = Date.now();
      const startTime = Date.now();
      this.isInCasualMode = this.selectedMode === 'casual';
      this.scrollLock = false;
  
      const user = this.authService.getCurrentUser();
      if (!user) {
          console.error('Usuario no autenticado');
          this.errorMessage = 'Usuario no autenticado';
          this.router.navigate(['/login']);
          return;
      }
  
      const token = this.authService.getToken();
      if (!token) {
          console.error('Token no disponible');
          this.errorMessage = 'Token de autenticación no disponible';
          this.router.navigate(['/login']);
          return;
      }
  
      const userData = {
          email: user.email,
          role: 'qa'
      };
  
      try {
          // Health Check con múltiples estrategias y tolerante a fallos
          console.log('====== VERIFICACIÓN DE SERVIDOR ======');
          let serverAvailable = false;
          let healthError = null;
          
          // Estrategia 1: Verificación con CORS
          try {
              console.log('🔄 Estrategia 1: Health check estándar...');
              const controller = new AbortController();
              const signal = controller.signal;
              const timeout = setTimeout(() => controller.abort(), 3000);
              
              const healthResponse = await fetch(`${this.apiUrl}/health`, {
                  method: 'GET',
                  headers: { 
                      'Accept': 'application/json',
                      'Cache-Control': 'no-cache'
                  },
                  signal: signal
              });
              
              clearTimeout(timeout);
              const healthData = await healthResponse.json();
              serverAvailable = true;
              console.log('✅ Servidor verificado con éxito (método estándar)');
          } catch (error) {
              healthError = error;
              console.log('⚠️ Falló verificación estándar, intentando alternativas...');
          }
          
          // Estrategia 2: Verificación sin CORS
          if (!serverAvailable) {
              try {
                  console.log('🔄 Estrategia 2: Health check sin CORS...');
                  const controller = new AbortController();
                  const signal = controller.signal;
                  const timeout = setTimeout(() => controller.abort(), 3000);
                  
                  const response = await fetch(`${this.apiUrl}/health`, {
                      method: 'GET',
                      mode: 'no-cors',
                      signal: signal
                  });
                  
                  clearTimeout(timeout);
                  console.log('✅ El servidor respondió (modo no-cors)');
                  serverAvailable = true;
              } catch (error) {
                  console.log('⚠️ Falló verificación sin CORS, intentando URL base...');
              }
          }
          
          // Estrategia 3: Verificar solo la URL base
          if (!serverAvailable) {
              try {
                  console.log('🔄 Estrategia 3: Verificando URL base...');
                  const urlBase = this.apiUrl.split('/').slice(0, 3).join('/');
                  const controller = new AbortController();
                  const signal = controller.signal;
                  const timeout = setTimeout(() => controller.abort(), 3000);
                  
                  console.log(`🔍 Intentando conexión a: ${urlBase}`);
                  const response = await fetch(urlBase, {
                      method: 'GET',
                      mode: 'no-cors',
                      signal: signal
                  });
                  
                  clearTimeout(timeout);
                  console.log('✅ URL base respondió correctamente');
                  serverAvailable = true;
                  
                  // Si la URL base funciona pero la específica no, actualizar la URL
                  if (this.apiUrl.includes(':5000')) {
                      const newUrl = this.apiUrl.replace(':5000', '');
                      console.log(`🔄 Cambiando a URL sin puerto: ${newUrl}`);
                      this.apiUrl = newUrl;
                  }
              } catch (error) {
                  console.error('❌ Todas las estrategias de conexión fallaron');
                  throw error;
              }
          }
          
          // Si llegamos aquí con serverAvailable = true, intentamos la operación principal
          if (serverAvailable) {
              console.log('🚀 Servidor alcanzable, procediendo con la consulta...');
              
              // CÓDIGO ORIGINAL DE PROCESAMIENTO DE CONSULTAS
              if (this.selectedMode === 'casual') {
                  const url = `${this.apiUrl}/ask/stream`;
                  console.log('====== DETALLES DE LA SOLICITUD CASUAL ======');
                  console.log('URL:', url);
    
                  const response = await fetch(url, {
                      method: 'POST',
                      headers: {
                          'Content-Type': 'application/json',
                          'Authorization': `Bearer ${token}`,
                          'X-User-Data': JSON.stringify(userData)
                      },
                      body: JSON.stringify({
                          query: this.userQuery,
                          mode: 'casual'
                      })
                  });
    
                  console.log('Respuesta casual:', {
                      status: response.status,
                      ok: response.ok
                  });
    
                  if (!response.ok) {
                      if (response.status === 401) {
                          this.errorMessage = 'Sesión expirada. Por favor, inicie sesión nuevamente.';
                          this.router.navigate(['/login']);
                          return;
                      }
                      throw new Error(`HTTP error! status: ${response.status}`);
                  }
    
                  const reader = response.body?.getReader();
                  const decoder = new TextDecoder();
    
                  if (reader) {
                      this.updateAssistantResponse({
                          assistant_id: 'casual',
                          role: 'casual',
                          status: 'en_proceso',
                          content: ''
                      });
    
                      let accumulatedContent = '';
    
                      while (true) {
                          const { done, value } = await reader.read();
    
                          if (done) {
                              this.updateAssistantResponse({
                                  assistant_id: 'casual',
                                  role: 'casual',
                                  status: 'completado',
                                  content: this.applyCasualFormatting(accumulatedContent),
                                  responseTime: (Date.now() - startTime) / 1000
                              });
                              break;
                          }
    
                          const chunk = decoder.decode(value);
                          const lines = chunk.split('\n');
    
                          for (const line of lines) {
                              if (line.startsWith('data: ')) {
                                  try {
                                      const data = JSON.parse(line.slice(6));
                                      if (data.content) {
                                          accumulatedContent = data.content;
                                          this.updateAssistantResponse({
                                              assistant_id: 'casual',
                                              role: 'casual',
                                              status: 'en_proceso',
                                              content: this.applyCasualFormatting(accumulatedContent)
                                          });
                                      }
                                      if (data.error) {
                                          this.updateAssistantResponse({
                                              assistant_id: 'casual',
                                              role: 'casual',
                                              status: 'error',
                                              error: data.error
                                          });
                                      }
                                  } catch (e) {
                                      console.error('Error parsing stream data:', e);
                                  }
                              }
                          }
                      }
                  }
              } else {
                  const url = `${this.apiUrl}/ask/multi/stream`;
                  console.log('====== DETALLES DE LA SOLICITUD TRABAJO ======');
                  console.log('URL:', url);
    
                  const payload = {
                      query: this.userQuery,
                      mode: this.selectedMode,
                      email: user.email,
                      assistant_ids: this.selectedAssistants
                  };
    
                  const response = await fetch(url, {
                      method: 'POST',
                      headers: {
                          'Content-Type': 'application/json',
                          'Authorization': `Bearer ${token}`,
                          'X-User-Data': JSON.stringify(userData),
                          'Accept': 'text/event-stream',
                          'Cache-Control': 'no-cache',
                          'Pragma': 'no-cache'
                      },
                      body: JSON.stringify(payload)
                  });
                  
                  console.log('Respuesta trabajo:', {
                      status: response.status,
                      ok: response.ok,
                      url: response.url
                  });
    
                  if (!response.ok) {
                      if (response.status === 401) {
                          this.errorMessage = 'Sesión expirada. Por favor, inicie sesión nuevamente.';
                          this.router.navigate(['/login']);
                          return;
                      }
                      throw new Error(`Error del servidor: ${response.status}`);
                  }
    
                  const reader = response.body?.getReader();
                  const decoder = new TextDecoder();
    
                  if (reader) {
                      while (true) {
                          const { done, value } = await reader.read();
    
                          if (done) {
                              const endTime = Date.now();
                              this.responseTime = endTime - startTime;
    
                              if (this.selectedMode !== 'casual') {
                                  const hasValidResponses = this.assistantResponses.some(r => 
                                      r.content && r.assistant_id !== 'system');
    
                                  if (hasValidResponses || this.assistantResponses.length === 1) {
                                      const combinedResponse = this.assistantResponses
                                          .map(r => r.content || '')
                                          .join('\n\n');
                                      
                                      const avgResponseTime = this.assistantResponses.reduce((acc, curr) => 
                                          acc + (curr.responseTime || 0), 0) / this.assistantResponses.length;
    
                                      this.multiResponses.unshift({
                                          question: this.userQuery,
                                          responses: [...this.assistantResponses],
                                          timestamp: new Date(),
                                          isExpanded: true,
                                          response: combinedResponse,
                                          responseTime: avgResponseTime || this.responseTime
                                      });
                                  }
                              }
                              break;
                          }
    
                          const chunk = decoder.decode(value);
                          const lines = chunk.split('\n');
    
                          for (const line of lines) {
                              if (line.startsWith('data: ')) {
                                  try {
                                      const data = JSON.parse(line.slice(6));
                                      this.updateAssistantResponse(data);
                                  } catch (e) {
                                      console.error('Error parsing stream data:', e);
                                  }
                              }
                          }
                      }
                  }
              }
              
              this.userQuery = '';
              
          } else if (healthError) {
              // Si todas las estrategias fallaron, reportar el error original
              throw healthError;
          }
  
      } catch (err: any) {
          console.error('====== ERROR GENERAL ======');
          console.error('❌ Tipo:', err.constructor.name);
          console.error('❌ Mensaje:', err.message);
          console.error('❌ URL:', this.apiUrl);
          console.error('❌ Timestamp:', new Date().toISOString());
          console.error('❌ Detalles:', err);
          
          let errorMessage = 'Error de conexión con el servidor.';
          let errorDetails = '';
          
          if (err instanceof TypeError && err.message === 'Failed to fetch') {
              errorMessage = 'No se puede establecer conexión con el servidor.';
              errorDetails = 'Verifique que:\n' +
                           '• El servidor esté en ejecución\n' +
                           '• La URL sea correcta\n' +
                           '• No haya problemas de red\n' +
                           '• Los puertos estén abiertos';
              
              // Sugerencia específica para producción
              if (environment.production) {
                  console.log('🔥 Sugerencia: En producción, verifique que el backend esté expuesto en el puerto correcto');
                  console.log('🔄 Intente cambiar en el nginx.conf para redireccionar /api al backend');
              }
          } else if (err.message?.includes('405')) {
              errorMessage = 'Error de configuración del servidor';
              errorDetails = 'Método no permitido. Contacte al administrador.';
          } else if (err.message?.includes('CORS')) {
              errorMessage = 'Error de políticas de seguridad';
              errorDetails = 'Problema de CORS. Verifique la configuración del servidor.';
          }
  
          const toast = await this.toastController.create({
              message: `${errorMessage}\n${errorDetails}`,
              duration: 5000,
              position: 'bottom',
              color: 'danger',
              buttons: [{ text: 'OK', role: 'cancel' }]
          });
          await toast.present();
  
      } finally {
          this.isLoading = false;
          this.isStreaming = false;
          this.scrollLock = false;
          console.log('====== MÉTRICAS FINALES ======');
          console.log('⏱️ Tiempo total:', Date.now() - this.currentStartTime, 'ms');
          console.log('📊 Estado final:', {
              isLoading: this.isLoading,
              isStreaming: this.isStreaming,
              hasError: !!this.errorMessage
          });
          console.log('====== FIN DE LA SOLICITUD ======');
      }
  }
 
 
  private updateAssistantResponse(data: AssistantResponse) {
      const noInfoMessages = [
          "No se encontró información al respecto en los documentos analizados.",
          "No se encontró información legal o regulatoria relacionada en los documentos analizados."
      ];
  
      // Formatear el contenido primero
      if (data.content) {
          data.content = this.applyFormatting(data.content);
      }
  
      if (data.status === 'completado') {
          const index = this.assistantResponses.findIndex(r => r.assistant_id === data.assistant_id);
          
          // Si no hay contenido o es mensaje de no info
          if (!data.content || noInfoMessages.includes(data.content.trim())) {
              if (index !== -1) {
                  this.assistantResponses.splice(index, 1);
              }
              
              // Verificar si todos los asistentes han respondido sin información
              const completedResponses = this.assistantResponses.filter(r => 
                  r.status === 'completado' || r.status === 'error');
              
              if (completedResponses.length === this.selectedAssistants.length - 1) {
                  const allNoInfo = this.selectedAssistants.every(assistantId => {
                      const response = this.assistantResponses.find(r => r.assistant_id === assistantId);
                      return !response || noInfoMessages.includes(response.content?.trim() || '');
                  });
  
                  if (allNoInfo) {
                      this.assistantResponses = [{
                          assistant_id: 'system',
                          role: 'system',
                          status: 'completado',
                          content: `
                              <div class="no-info-message">
                                  <ion-icon name="information-circle-outline"></ion-icon>
                                  <h3>No se encontró información</h3>
                                  <p>No se encontró información relacionada con su consulta en las bases de conocimiento disponibles.</p>
                                  <p>Sugerencias:</p>
                                  <ul>
                                      <li>Intente reformular su pregunta</li>
                                      <li>Use términos más específicos</li>
                                      <li>Verifique que el tema esté dentro del alcance de la documentación</li>
                                  </ul>
                              </div>
                          `,
                          responseTime: data.responseTime
                      }];
                  }
              }
          } else {
              // Actualizar o agregar respuesta con contenido
              if (index === -1) {
                  this.assistantResponses.push(data);
              } else {
                  this.assistantResponses[index] = { 
                      ...this.assistantResponses[index], 
                      ...data,
                      content: data.content  // Asegurarse que se use el contenido formateado
                  };
              }
          }
      } else {
          // Manejar estados no completados
          const index = this.assistantResponses.findIndex(r => r.assistant_id === data.assistant_id);
          if (index === -1) {
              this.assistantResponses.push(data);
          } else {
              this.assistantResponses[index] = { ...this.assistantResponses[index], ...data };
          }
      }
  
      // Actualizar scroll si es necesario
      if (this.isInCasualMode || data.status === 'completado') {
          this.scrollToBottom();
      }
  }
 
  private scrollToLatestResponse(): void {
    requestAnimationFrame(() => {
      const mainContent = document.getElementById('main-content');
      if (mainContent) {
        const cards = mainContent.getElementsByClassName('response-card');
        if (cards.length > 0) {
          const lastCard = cards[cards.length - 1];
          lastCard.scrollIntoView({ behavior: 'smooth', block: 'end' });
        } else {
          // Si no hay cards, scroll al final del contenedor
          mainContent.scrollTo({
            top: mainContent.scrollHeight,
            behavior: 'smooth'
          });
        }
      }
    });
  }
  
  private scrollToBottom(immediate: boolean = false): void {
    if (this.scrollLock) return;
    
    const mainContent = document.getElementById('main-content');
    if (!mainContent) return;
  
    // En modo casual, siempre hacemos scroll inmediato
    const behavior = this.isInCasualMode ? 'auto' : (immediate ? 'auto' : 'smooth');
    
    const scrollToBottom = () => {
      mainContent.scrollTo({
        top: mainContent.scrollHeight,
        behavior
      });
    };
  
    // Para modo casual, aseguramos que el scroll se mantiene abajo
    if (this.isInCasualMode) {
      this.scrollLock = true;
      scrollToBottom();
      
      // Usar ResizeObserver para detectar cambios en el contenido
      const observer = new ResizeObserver(() => {
        scrollToBottom();
      });
      
      observer.observe(mainContent);
      
      // Limpiar el observer después de que termine el streaming
      if (!this.isStreaming) {
        observer.disconnect();
        this.scrollLock = false;
      }
    } else {
      scrollToBottom();
    }
  }
  
  applyFormatting(content: string): string {
      let formatted = content
          .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>')
          .replace(/<table>/g, '<table class="custom-table">')
          .replace(/<tr>/g, '<tr class="table-row">')
          .replace(/<th>/g, '<th class="table-header">')
          .replace(/<td>/g, '<td class="table-cell">')
          .replace(/####\s*([^#\n]+?)(?:\n|$)/g, '</ul></li></ol><h4 class="response-subtitle">$1</h4>')
          .replace(/###\s*([^#\n]+?)(?:\n|$)/g, '</ul></li></ol><h3 class="response-subtitle">$1</h3>')
          .replace(/##\s*([^#\n]+?)(?:\n|$)/g, '</ul></li></ol><h2 class="response-subtitle">$1</h2>')
          .replace(/#\s*([^#\n]+?)(?:\n|$)/g, '</ul></li></ol><h1 class="response-subtitle">$1</h1>')
          .replace(/^<\/ul><\/li><\/ol>/, '')
          .replace(/\*{4,5}([^*\n]+?)\*{4,5}/g, '<strong>$1</strong>')
          .replace(/\*\*([^*\n]+?)\*\*/g, '<strong>$1</strong>')
          .replace(/pág\.\s*(\d+)-(\d+)/g, 'pág. $1&#8209;$2')
          .replace(/^(\d+)\.\s+\*\*([^*]+?)\*\*\s*:?\s*([^\n]*)/gm, '<li class="list-item"><span class="item-number">$1.</span> <strong>$2</strong>$3')
          .replace(/^(\d+)\.\s+([^\n]+)/gm, '<li class="list-item"><span class="item-number">$1.</span> $2</li>')
          .replace(/^\s*-\s+\*\*([^*]+?)\*\*\s*:?\s*([^\n]*)/gm, '<li class="sublist-item"><strong>$1</strong>$2</li>')
          .replace(/^\s*-\s+([^\n]+)/gm, '<li class="sublist-item">$1</li>')
          .replace(/(<li class="sublist-item">.*?<\/li>)+/gs, '<ul class="custom-sublist">$&</ul>')
          .replace(/(?<!<ol[^>]*>)(<li class="list-item">.*?<\/li>)+/gs, '<ol class="custom-list">$&</ol>')
          .replace(/([^\s])(【[^】]+?】)/g, '$1 $2')
          .replace(/【([^】]+?)】/g, '<span class="reference">[$1]</span>')
          .replace(/([^\s])(\[\d+:\d+†source\])/g, '$1 <span class="reference">$2</span>')
          .replace(/\n\s*\n/g, '</p><p>')
          .replace(/\n/g, ' ')
          .replace(/^(.+?)(?=<\/p>|$)/, '<p>$1')
          .replace(/<p>\s*<\/p>/g, '')
          .replace(/(<br\s*\/?>\s*){3,}/g, '<br><br>')
          .replace(/^<br>/, '')
          .replace(/<\/?p>\s*<(table|h[1-4])/g, '<$1')
          .replace(/(<\/table>|<\/h[1-4]>)\s*<\/?p>/g, '$1')
          .replace(/<\/ol>\s*<\/ol>/g, '</ol>')
          .replace(/<\/ul>\s*<\/ul>/g, '</ul>')
          .replace(/<ol class="custom-list"><\/ol>/g, '')
          .replace(/<ul class="custom-sublist"><\/ul>/g, '');
  
      // Procesar referencias al final del contenido
      const referencesMatch = formatted.match(/Referencias\s*([^]+)$/);
      if (referencesMatch) {
          const refMap = new Map();
          
          // Extraer referencias únicas manteniendo el orden
          referencesMatch[1].split('\n').forEach(line => {
              const match = line.match(/\[(\d+)\]\s+Fuente:\s*(.+)/);
              if (match) {
                  const [, num, content] = match;
                  if (!refMap.has(num) && content.trim()) {
                      refMap.set(num, content.trim());
                  }
              }
          });
  
          // Construir HTML de referencias
          const references = Array.from(refMap.entries())
              .map(([num, ref]) => `<li>[${num}] Fuente: ${ref}</li>`)
              .join('');
  
          formatted = formatted.replace(
              /Referencias\s*([^]+)$/,
              `<h3 class="response-subtitle">Referencias</h3><ul class="custom-list">${references}</ul>`
          );
      }
  
      return formatted;
  }

  private applyCasualFormatting(content: string): string {
      // Eliminar los $2 que aparecen al final de frases (probablemente un error de formato)
      let formatted = content.replace(/\.\. \$2/g, '..')
                             .replace(/\. \$2/g, '.')
                             .replace(/ \$2/g, '');
      
      // Formateo básico del contenido
      formatted = formatted
          // Títulos y subtítulos
          .replace(/^(.*?):\s*$/gm, '<h3 class="casual-title">$1:</h3>')
          
          // Convertir guiones/viñetas en elementos de lista
          .replace(/^-\s+([^\n]+)/gm, '<li class="casual-list-item">$1</li>')
          
          // Mantener numeración simple
          .replace(/^(\d+)\.\s+([^\n]+)/gm, '<div class="casual-numbered-item"><span class="casual-number">$1.</span> $2</div>')
          
          // Espaciado entre párrafos
          .replace(/\n\n/g, '</p><p class="casual-paragraph">')
          
          // Enfatizar términos importantes
          .replace(/\*([^*]+)\*/g, '<em class="casual-emphasis">$1</em>')
          .replace(/__([^_]+)__/g, '<strong class="casual-strong">$1</strong>')
          
          // Procesar emojis y hacerlos más visibles
          .replace(/([\uD800-\uDBFF][\uDC00-\uDFFF])/g, '<span class="casual-emoji">$1</span>')
          
          // Aplicar wrapper para párrafos
          .replace(/^(.+?)(?=<\/p>|$)/, '<p class="casual-paragraph">$1')
          
          // Convertir saltos de línea simples
          .replace(/\n/g, '<br>')
          
          // Aplicar formato a listas
          .replace(/(<li class="casual-list-item">.*?<\/li>)+/g, '<ul class="casual-list">$&</ul>')
          
          // Limpiar tags vacíos y duplicados
          .replace(/<p class="casual-paragraph">\s*<\/p>/g, '')
          .replace(/(<br>){3,}/g, '<br><br>');
          
      return formatted;
  }
    
  async downloadAsDocx(pregunta: MultiResponse | PreguntaInterface): Promise<void> {
    try {
      const content = this.getResponseContent(pregunta);
      const cleanedContent = this.stripHtmlTags(content);
  
      // Procesar el contenido para una mejor estructura
      const processedParagraphs = this.processContentForDocx(cleanedContent);
  
      const doc = new Document({
        creator: 'CogniSearch',
        title: 'Respuesta CogniSearch',
        styles: {
          default: {
            document: {
              run: {
                font: 'Calibri',
                size: 22  // 11pt
              }
            }
          }
        },
        sections: [{
          properties: {},
          children: [
            // Título
            new Paragraph({
              heading: HeadingLevel.TITLE,
              alignment: AlignmentType.CENTER,
              children: [
                new TextRun({
                  text: 'Respuesta CogniSearch',
                  bold: true,
                  size: 32  // 16pt
                })
              ]
            }),
            
            // Fecha
            new Paragraph({
              alignment: AlignmentType.RIGHT,
              children: [
                new TextRun({
                  text: `Fecha: ${new Date().toLocaleDateString()}`,
                  italics: true,
                  size: 20  // 10pt
                })
              ]
            }),
            
            // Línea separadora
            new Paragraph({
              children: [
                new TextRun({
                  text: '—'.repeat(44),
                  color: '000000'
                })
              ],
              alignment: AlignmentType.CENTER
            }),
            
            // Contenido principal con formato mejorado
            ...processedParagraphs.map(para => {
              // Determinar el estilo del párrafo
              const paragraphOptions: any = {
                alignment: AlignmentType.JUSTIFIED,
                spacing: {
                  line: 276,  // 1.5 líneas 
                  before: 240,  // Espacio antes del párrafo
                  after: 240   // Espacio después del párrafo
                }
              };
  
              const children = [];
  
              // Manejar títulos y subtítulos
              if (para.type === 'heading') {
                paragraphOptions.heading = HeadingLevel.HEADING_2;
                children.push(new TextRun({
                  text: para.text,
                  bold: true,
                  size: 26  // 13pt
                }));
              } else if (para.type === 'subheading') {
                children.push(new TextRun({
                  text: para.text,
                  italics: true,
                  size: 24  // 12pt
                }));
              } else if (para.type === 'list') {
                // Manejar elementos de lista
                children.push(new TextRun({
                  text: `• ${para.text}`,
                  size: 22  // 11pt
                }));
              } else {
                // Párrafo normal
                children.push(new TextRun({
                  text: para.text,
                  size: 22  // 11pt
                }));
              }
  
              paragraphOptions.children = children;
              return new Paragraph(paragraphOptions);
            }),
            
            // Pie de página
            new Paragraph({
              alignment: AlignmentType.CENTER,
              children: [
                new TextRun({
                  text: 'Documento generado por CogniSearch',
                  size: 18,  // 9pt
                  italics: true,
                  color: '666666'
                })
              ]
            })
          ]
        }]
      });
  
      const buffer = await Packer.toBlob(doc);
      const url = window.URL.createObjectURL(buffer);
      const link = document.createElement('a');
      link.href = url;
      link.download = `respuesta_${new Date().toISOString().slice(0,10)}.docx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
  
      const toast = await this.toastController.create({
        message: 'Documento descargado correctamente',
        duration: 2000,
        position: 'bottom',
        color: 'success'
      });
      await toast.present();
  
    } catch (error) {
      console.error('Error al descargar:', error);
      const toast = await this.toastController.create({
        message: 'Error al descargar el documento',
        duration: 2000,
        position: 'bottom',
        color: 'danger'
      });
      await toast.present();
    }
  }

  // Método para sanitizar nombre de archivo
  private sanitizeFileName(filename: string): string {
    return filename
      .toLowerCase()
      .replace(/[^a-z0-9]/g, '_')  // Reemplaza caracteres no alfanuméricos con guion bajo
      .replace(/_+/g, '_')  // Elimina guiones bajos múltiples
      .trim();
  }

  toggleExpanded(pregunta: MultiResponse | PreguntaInterface): void {
    pregunta.isExpanded = !pregunta.isExpanded;
  }

  formatText(content: string): SafeHtml {
    if (!content) return '';
    const formatted = this.applyFormatting(content);
    return this.sanitizer.bypassSecurityTrustHtml(formatted);
  }

  async copyResponse(response: MultiResponse | AssistantResponse): Promise<void> {
      try {
          let content: string;
          
          if ('responses' in response) {
              // Es una MultiResponse del historial
              content = this.getResponseContent(response);
          } else {
              // Es una AssistantResponse individual
              content = response.content || '';
          }
          
          const text = this.stripHtmlTags(content);
          
          await navigator.clipboard.writeText(text);
          
          const toast = await this.toastController.create({
              message: 'Respuesta copiada al portapapeles',
              duration: 2000,
              position: 'bottom'
          });
          await toast.present();
      } catch (error) {
          console.error('Error al copiar:', error);
          const toast = await this.toastController.create({
              message: 'No se pudo copiar al portapapeles',
              duration: 2000,
              position: 'bottom',
              color: 'danger'
          });
          await toast.present();
      }
  }
  
  async shareResponse(response: MultiResponse | AssistantResponse): Promise<void> {
      try {
          let content: string;
          
          if ('responses' in response) {
              // Es una MultiResponse del historial
              content = this.getResponseContent(response);
          } else {
              // Es una AssistantResponse individual
              content = response.content || '';
          }
          
          const text = this.stripHtmlTags(content);
          
          if (navigator.share) {
              await navigator.share({
                  text: text,
                  title: 'Compartir respuesta'
              });
          } else {
              // Si la API Web Share no está disponible, copiar al portapapeles
              await navigator.clipboard.writeText(text);
              const toast = await this.toastController.create({
                  message: 'Contenido copiado. Use su aplicación preferida para compartir.',
                  duration: 3000,
                  position: 'bottom'
              });
              await toast.present();
          }
      } catch (error) {
          console.error('Error al compartir:', error);
      }
  }
  
  private getResponseContent(pregunta: MultiResponse | PreguntaInterface): string {
      if ('responses' in pregunta) {
          // Obtener la pregunta primero
          const questionHeader = `Consulta\n====================\n${pregunta.question}\n\n`;
          
          // Procesar cada respuesta manteniendo separación clara
          const responses = pregunta.responses.map(r => {
              if (!r.content) return '';
              
              // Agregar encabezado con el rol y separación clara
              const roleHeader = `Respuesta ${this.getRoleName(r.role)}\n====================\n`;
              
              // Procesar el contenido para asegurar que las referencias tengan espaciado adecuado
              let content = r.content;
              
              // Asegurar que las referencias tengan espacio antes y después
              if (content.includes('Referencias')) {
                  content = content.replace('Referencias', '\nReferencias');
                  content += '\n';
              }
              
              // Combinar encabezado y contenido
              return `${roleHeader}${content}\n\n`;
          }).filter(r => r.length > 0);
          
          // Combinar todo con espaciado apropiado
          return questionHeader + responses.join('\n');
      } else {
          return pregunta.response;
      }
  }
  
  private processContentForDocx(content: string): Array<{type: string, text: string}> {
      const lines = content.split('\n').map(line => line.trim()).filter(line => line);
      const processedLines: Array<{type: string, text: string}> = [];
      
      let currentRole = '';
      let inReferences = false;
  
      for (let i = 0; i < lines.length; i++) {
          const line = lines[i];
  
          // Manejar encabezados principales (Consulta y Respuesta [Rol])
          if (line === 'Consulta' || line.startsWith('Respuesta ')) {
              if (line.startsWith('Respuesta ')) {
                  currentRole = line;
              }
              // Saltarse la línea de '===================='
              i++;
              processedLines.push({ type: 'heading', text: line });
              continue;
          }
  
          // Manejar el contenido basado en el contexto
          if (line.startsWith('•')) {
              // Es un elemento de lista
              const cleanLine = line.replace(/^•\s*/, '');
              processedLines.push({ type: 'list', text: cleanLine });
          } else if (line === 'Referencias') {
              inReferences = true;
              processedLines.push({ type: 'heading', text: `Referencias ${currentRole}` });
          } else if (inReferences && line.startsWith('[')) {
              // Es una referencia
              processedLines.push({ type: 'subheading', text: line });
          } else if (line.startsWith('*') || line.startsWith('**')) {
              // Es una ruta o metadata
              processedLines.push({ type: 'subheading', text: line.replace(/\*/g, '') });
          } else if (line.length > 0) {
              // Es un párrafo normal
              processedLines.push({ type: 'paragraph', text: line });
          }
  
          // Si encontramos una nueva sección de respuesta, resetear inReferences
          if (line.startsWith('Respuesta ')) {
              inReferences = false;
          }
      }
  
      return processedLines;
  }
  
  async deleteQuestion(index: number): Promise<void> {
    const alert = await this.alertController.create({
      header: 'Confirmar eliminación',
      message: '¿Estás seguro de que deseas eliminar esta consulta?',
      buttons: [
        {
          text: 'Cancelar',
          role: 'cancel'
        },
        {
          text: 'Eliminar',
          handler: () => {
            // Eliminar del array multiResponses en lugar de preguntas
            this.multiResponses.splice(index, 1);
          }
        }
      ]
    });
    await alert.present();
  }
  
  async clearHistory(): Promise<void> {
    const alert = await this.alertController.create({
      header: 'Limpiar historial',
      message: '¿Estás seguro de que deseas eliminar todo el historial?',
      buttons: [
        {
          text: 'Cancelar',
          role: 'cancel'
        },
        {
          text: 'Limpiar',
          handler: () => {
            // Limpiar el array multiResponses en lugar de preguntas
            this.multiResponses = [];
          }
        }
      ]
    });
    await alert.present();
  }
  reuseQuestion(pregunta: MultiResponse | PreguntaInterface): void {
    this.userQuery = pregunta.question;
  }

  // Funcion que maneja la conversión del HTML a texto plano cuando se copia o comparte
  private stripHtmlTags(html: string): string {
      // Limpia el prefijo de SafeValue si existe
      const cleanHtml = html
          .replace('SafeValue must use [property]=binding: ', '')
          .replace(/\(see https:\/\/g\.co\/ng\/security#xss\)$/, '');
  
      // Crea un elemento temporal para parsear el HTML
      const temp = document.createElement('div');
      temp.innerHTML = cleanHtml;
  
      // Función recursiva para preservar la estructura
      const preserveStructure = (node: Node): string => {
          if (node.nodeType === Node.TEXT_NODE) {
              return node.textContent || '';
          }
  
          const children = Array.from(node.childNodes).map(preserveStructure);
          const element = node as Element;
  
          switch (element.tagName?.toLowerCase()) {
              case 'h1':
              case 'h2':
              case 'h3':
              case 'h4':
                  return `\n\n${children.join('')}\n`;
                  
              case 'li':
                  // Manejo específico para elementos de lista
                  const itemClass = element.className;
                  const itemNumber = element.querySelector('.item-number');
                  
                  if (itemClass === 'list-item') {
                      // Para listas numeradas
                      const number = itemNumber ? itemNumber.textContent?.replace('.', '') : '';
                      const content = children.join('').replace(number + '.', '').trim();
                      return `${number}. ${content}\n`;
                  } else if (itemClass === 'sublist-item') {
                      // Para sublistas con viñetas
                      return `• ${children.join('').trim()}\n`;
                  }
                  return `• ${children.join('').trim()}\n`;
  
              case 'ol':
                  return `\n${children.join('')}\n`;
                  
              case 'ul':
                  return `${children.join('')}\n`;
                  
              case 'p':
                  return `${children.join('')}\n\n`;
                  
              case 'br':
                  return '\n';
                  
              case 'strong':
                  return `*${children.join('')}*`;
                  
              case 'span':
                  // Manejo especial para referencias
                  if (element.className === 'reference') {
                      return ` ${children.join('')}`;
                  }
                  return children.join('');
                  
              case 'table':
                  return `\n${children.join('')}\n`;
                  
              case 'tr':
                  return `${children.join(' | ')}\n`;
                  
              case 'th':
              case 'td':
                  return children.join('');
                  
              default:
                  return children.join('');
          }
      };
  
      // Procesa el contenido y limpia el formato
      let formattedText = preserveStructure(temp)
          // Limpia múltiples saltos de línea
          .replace(/\n\s*\n\s*\n+/g, '\n\n')
          // Limpia espacios extra después de viñetas
          .replace(/•\s+/g, '• ')
          // Asegura que los números de lista tengan el formato correcto
          .replace(/(\d+)\.\s+\1\.\s+/g, '$1. ')
          // Limpia espacios extra
          .trim();
  
      return formattedText;
  }
  
  // Aquí implementas la lógica para subir documentos.
  uploadDocuments() {
    console.log("Iniciando proceso de subida de documentos");
  }
}