// src/app/query/query.module.ts

import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { RouterModule } from '@angular/router';
import { QueryComponent } from './query.component';

@NgModule({
  declarations: [QueryComponent],
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    RouterModule
  ],
  exports: [QueryComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class QueryModule { }

