/* query.component.scss */

:root {
  font-family: 'Montserrat'; /* Debería aplicar Mont<PERSON><PERSON> a todo el documento */
}

/* Estilos adicionales para el manejo del foco */
:host {
  .ion-color-primary {
    --ion-color-base: #1a73b3 !important;
   }
  .ion-color-danger {
    --ion-color-base: #b31a1a !important;
   }
  .ion-focused {
    outline: none;
    box-shadow: 0 0 0 2px rgba(154, 199, 199, 0.3);
  }

  ion-select {
    &:focus {
      outline: none;
    }
    
    &:focus-visible {
      outline: 2px solid #9ac7c7;
      outline-offset: 2px;
    }
  }
}

.custom-select {
  &::part(backdrop) {
    background: transparent;
  }
  
  &::part(text) {
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

::ng-deep {
  ion-select::part(backdrop) {
    display: none;
  }

  .select-interface-option.select-interface-option {
    --background-hover: var(--ion-color-light);
    position: relative;
    
    &:focus {
      outline: none;
      background: var(--ion-color-light);
    }
    
    &::after {
      content: '';
      position: absolute;
      inset: 0;
      pointer-events: none;
      transition: opacity 0.2s;
      opacity: 0;
      background: currentColor;
    }
    
    &:focus::after {
      opacity: 0.1;
    }
  }

  .select-popover {
    --width: 200px;
    --max-width: 90vw;
    --box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    --backdrop-opacity: 0;
    
    .popover-content {
      border-radius: 8px;
    }

    ion-item {
      --padding-start: 16px;
      --padding-end: 16px;
      --min-height: 40px;
      
      &:focus {
        --background: var(--ion-color-light);
      }
    }
  }
}

/* Contenedor principal de la aplicación */
.app-container {
  display: flex;
  height: 100vh;
  position: relative;
  overflow: hidden;
}

/* Contenido principal (fondo claro) */
.main-content {
  max-width: 100%;
  /*margin: 0 auto;*/
   z-index: 2; /* Coloca el contenido principal por encima del menú */
  font-family: 'Montserrat'; /* Hereda Montserrat de la configuración global */
  background-color: transparent !important;  /* Fondo blanco para el contenido principal */
  /*border-radius: 8px;*/
  /* box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.05);  Sombra suave */
  height: 100vh;          /* Ensure content takes full viewport height */
  overflow-y: auto;       /* Ensure content can be scrolled */
  padding: 20px 20px 20px 44px;          /* Add padding for better UX */
  box-sizing: border-box; /* Ensure padding doesn’t increase overall width */
}

/* Ajuste del contenido principal cuando el panel está abierto */
.main-content {
  transition: margin-right 0.3s ease;
  
  &.shifted {
    margin-right: 400px;
  }
}

.main-title {
  font-size: 40px;
  color: #9ac7c7;  /* Color turquesa para el título */
  text-align: left;
  margin-bottom: 10px;
}

.section-title {
  font-size: 28px;
  color: #939db6;
  margin-bottom: 8px;
}

.header-md {
	/*-webkit-box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);
    box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);*/
	-webkit-box-shadow: none;
    box-shadow: none;
}

.list-md {
    margin-left: 0;
    margin-right: 0;
    margin-top: 0;
    margin-bottom: 0;
    padding-left: 0;
    padding-right: 0;
    padding-top: 8px;
    padding-bottom: 8px;
    background: var(--ion-color-light);
}

/* Contenedor del sidebar */

.sidebar-content {
  padding: 6px 10px;
}

.sidebar-content-wrapper {
  width: 350px;
  min-width: 350px;
  height: 100vh;
  transition: transform 0.3s ease, opacity 0.2s ease;
  z-index: 3;
  background: #f4f5f8;
  border-right: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 2px 0 5px rgba(0, 0, 0, 0.05);
  overflow-y: auto;
  position: relative; /* Allows z-index on children */
  overflow: auto; /* Ensure the link isn't clipped */
}

.version-footer {
    position: absolute;
    bottom: 0;
    width: 100%;
    padding: 0.5rem;
    background: linear-gradient(180deg, rgb(244 245 248) 0%, #ffffff 40%);
    text-align: center;
    /* border-top: 1px solid rgba(0,0,0,0.05);*/
    margin-top: auto;
    
    .version-text {
        font-size: 0.62rem;
        color: #6c757d;
        opacity: 0.8;
        font-family: 'Courier New', monospace;
        letter-spacing: 0.5px;
        
        &::before {
            content: '';
            display: inline-block;
            width: 6px;
            height: 6px;
            background-color: #28a745;
            border-radius: 50%;
            margin-right: 6px;
            vertical-align: middle;
        }
    }
}

/* Selector de Asistente */
.assistant-selector {
  margin: 1rem 0;
  padding: 0.5rem;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.selector-item {
  --background: transparent;
  --border-color: transparent;
  --padding-start: 0;
  --padding-end: 0;
  --min-height: 48px;
}

ion-label {
  font-size: 0.9rem;
  font-weight: 500;
  color: #666;
  margin-right: 1rem;
}

.assistant-select {
  --padding-start: 8px;
  --padding-end: 8px;
  max-width: 100%;
  width: auto;
  font-size: 0.9rem;
  /*border: 1px solid #e0e0e0;*/
  border-radius: 6px;
  color: #333;
  
  &::part(icon) {
    color: #666;
    opacity: 0.7;
  }
}

ion-select-option {
  font-size: 0.9rem;
  color: #333;
  padding: 8px;
}

ion-label {
  font-family: 'Montserrat';
  font-size: 0.9rem;
  font-weight: 400;
  letter-spacing: 0.3px;
  color: #666;
  /*text-transform: uppercase;*/
  transition: color 0.2s ease;
  margin-right: 1.5rem;
  white-space: nowrap;
  
  /* Efecto hover sutil */
  &:hover {
    color: #333;
  }
}

/* Ajustes responsive */
@media (max-width: 768px) {
  .assistant-selector {
    margin: 0.75rem 0;
  }
  
  .selector-item {
    --min-height: 44px;
  }
  
  ion-label {
    font-size: 0.85rem;
  }
  
  .assistant-select {
    font-size: 0.85rem;
  }
}

/* Ajuste del botón toggle del Sidebar*/
.sidebar-toggle {
  position: absolute;
  left: 350px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 3;
  background-color: #1a73b3;
  border: none;
  border-radius: 0 50% 50% 0;
  width: 24px;
  height: 48px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
}

/* Ajuste del estado cerrado */
.sidebar-closed {
  .sidebar-content-wrapper {
    transform: translateX(-380px);
    opacity: 0;
  }

  .sidebar-toggle {
    left: 0;
  }

  .main-content {
    margin-left: -380px;
  }
}

.logo-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 70px 0 20px 0; /* Espaciado del logo */
}

/* Ajuste del logo */
.sidebar-logo {
  max-width: 285px; /* Ajustado proporcionalmente */
  height: auto;
}

.header-container {
    position: relative;
    width: 100%;
}

/* Estilos de boton para Cerrar Sesion */
.logout-container {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 1000;
}

.logout-button {
    --padding-start: 8px;
    --padding-end: 8px;
    --background: transparent;
    --color: #666;
    height: 36px;
    
    &:hover {
        --background: rgba(0, 0, 0, 0.05);
        --color: #1a73b3;
    }
    
    ion-icon {
        font-size: 20px;
        color: inherit;
    }
}

/* Estilos de boton para Subir archivos */
.upload-container {
    position: absolute;
    top: 10px;
    right: 60px; // Ajusta este valor según la separación que desees respecto al botón de cerrar sesión
    z-index: 1000;
}

.upload-button {
    --padding-start: 8px;
    --padding-end: 8px;
    --background: transparent;
    --color: #666;
    height: 36px;
    
    &:hover {
        --background: rgba(0, 0, 0, 0.05);
        --color: #1a73b3;
    }
    
    ion-icon {
        font-size: 20px;
        color: inherit;
    }
}

/* Estilos para los iconos en las opciones deslizables */
ion-item-option {
  ion-icon {
    color: #fff; /* Mantiene los iconos blancos en los botones de acción deslizables */
    font-size: 18px;
  }
}

/* Estilo para el icono de limpiar historial */
.clear-history-btn {
  --color: #333;
  
  ion-icon {
    color: #333;
    font-size: 18px;
  }
}

/* Estilo para el icono expandir/colapsar */
.expand-icon {
  font-size: 18px;
  color: #333 !important; /* Color negro para el icono de expandir/colapsar */
  transition: transform 0.3s ease;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  /*padding: 10px 0;
  margin-bottom: 10px;*/
}

.history-title {
  margin: 0;
  font-size: 0.85rem;
  color: #666;
  font-weight: 500;
  font-family: 'Montserrat';
}

.clear-history-btn {
  --padding-start: 8px;
  --padding-end: 8px;
  height: 30px;
}

.question-item {
  --padding-start: 0;
  --padding-end: 0;
  --inner-padding-end: 0;
    overflow: hidden;
    background-color: #1a73b317;
    border-radius: 10px;
    /*padding: 6px 6px 6px 6px;*/
    margin: 2px 6px 2px 6px;
    transition: all 0.3s ease;
    border: 2px solid #1a73b303;
  
  &:hover {
    border-color: rgba(154, 199, 199, 0.3);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.39);
  }

  .question-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    padding: 1px 0;
  }

  .question-label {
    color: #333;
    font-size: 13px;
    line-height: 1.4;
    white-space: pre-wrap;
    word-wrap: break-word;
    user-select: none;
  }

  .expand-icon {
    font-size: 18px;
    color: #9ac7c7;
    transition: transform 0.3s ease;
  }
}

.question-item {

}

.question-content {
  width: 100%;
  padding: 8px 12px;
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  padding: 4px 0;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.question-number {
  font-size: 0.8rem;
  color: #9ac7c7;
  min-width: 24px;
}

.question-text {
  font-size: 13px;
  color: #333;
  line-height: 1.1;
  flex: 1;
}

.expand-icon {
  font-size: 18px;
  color: #9ac7c7;
  transition: transform 0.3s ease;
}

.expand-icon.rotated {
  transform: rotate(180deg);
}

.response-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
  margin-top: 4px;
}

/* Timestamp del sidebar */
.timestamp {
  font-size: 11px;
  color: #999;
}

/* Estilos de tiempo de respuesta bajo main response */
.response-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.response-meta {
    display: flex;
    align-items: center;
    gap: 8px;
}

.response-time {
    font-size: 0.85rem;
    color: #666;
}

.response-metrics {
  display: flex;
  justify-content: flex-end;
  padding: 8px 0;
  border-top: 1px solid #eee;
  margin-top: 8px;
}

/* Estilos para los botones de acción */
.response-actions {
  display: flex;
  align-items: center;
  gap: 4px; /* Espacio entre botones reducido/*

  .action-button {
    /* Variables de Ionic Button*/
    --padding-start: 4px;
    --padding-end: 4px;
    --color: #333;
    
    /* Propiedades base*/
    height: 30px;
    transition: all 0.2s ease;

    /* Estado hover*/
    &:hover {
      --color: #1a73b3;  /* Color azul al hover*/
      transform: scale(1.05); /* Ligero zoom*/
    }

    /* Estado activo (clic)*/
    &:active {
      transform: scale(0.95); /* Efecto de "presión"
    }

    /* Estilos para el ícono*/
    ion-icon {
      color: inherit;
      font-size: 18px;
      transition: color 0.2s ease;
    }
  }
}

ion-card-content {
    position: relative;
    padding-bottom: 40px; /* Espacio para los botones*/

    .response-actions {
        position: absolute;
        bottom: 8px;
        right: 8px;
        display: flex;
        gap: 8px;
        
        .action-button {
            --padding-start: 8px;
            --padding-end: 8px;
            height: 32px;
            
            ion-icon {
                font-size: 18px;
                color: var(--ion-color-medium);
            }
            
            &:hover ion-icon {
                color: var(--ion-color-primary);
            }
        }
    }
}

/* Asegurar que los botones sean visibles sobre fondo claro u oscuro*/
.action-button {
    --background: transparent;
    --background-hover: rgba(var(--ion-color-primary-rgb), 0.1);
    --ripple-color: var(--ion-color-primary);
}

/* Estilos tooltip (opcional, si lo usas)*/
.custom-tooltip {
  --background: var(--ion-color-dark);
  --color: var(--ion-color-light);
  font-size: 12px;
  padding: 8px;
  border-radius: 4px;
}

.delete-option, .reuse-option {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 16px;
}

.no-questions {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 20px;
  color: #999;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 14px;
  margin: 0;
}

/* Animation for expand/collapse */
@keyframes slideDown {
  from { max-height: 0; opacity: 0; }
  to { max-height: 500px; opacity: 1; }
}

@keyframes slideUp {
  from { max-height: 500px; opacity: 1; }
  to { max-height: 0; opacity: 0; }
}

/* Contenedor para el label y select en columna */
.dropdown-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 10px 0px 0px 14px;
  background: #f4f5f8 !important;
}

/* Estilo para el label */
.dropdown-container ion-label {
    font-family: 'Montserrat';
    font-size: 16px;
    padding: 10px 0 0 0;
    color: #333;
    margin-bottom: 5px;
}

/* Estilo para el select */
.dropdown-container ion-select {
  width: clamp(145px, 76vw, 229px); /* Use a wider range to make them more adaptable */
  height: 35px; /* Increase height for better spacing */
  background: linear-gradient(90deg, #f5f5f5, rgba(173, 216, 230, 0.3)); /* Lighter, more cohesive gradient */
  border-radius: 8px;
  font-size: 15px;
  color: #333; /* Dark gray for improved contrast */
  padding: 0px 15px; /* Added padding for better spacing */
  display: flex;
  align-items: center;
  text-align: left;
  border: 1px solid #d0d0d0;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1); /* Subtle shadow to create a sense of depth */
  transition: background-color 0.3s, box-shadow 0.3s;
}

.dropdown-container ion-select:hover {
  background: linear-gradient(90deg, #eaeaea, rgba(173, 216, 230, 0.5)); /* Slightly different hover color */
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); /* Make the shadow stronger on hover */
}

/* Enlace Vectorestore manager */
.vector-store-link {
  margin: 15px 0;
  text-align: center;
  position: relative; /* Ensures z-index works */
  z-index: 10; /* Brings the link above other elements */

  .elegant-link {
    display: inline-block;
    font-size: 13px;
    font-weight: 500;
    color: #1a73b3;
    text-decoration: none;
    padding: 8px 12px;
    border: 1px solid #1a73b3;
    border-radius: 20px;
    transition: all 0.3s ease;
    pointer-events: auto; /* Asegura que sea clickeable */
	position: relative; /* Optional: If needed for the child element */
    z-index: 11; /* Higher than its parent */

    &:hover {
      color: var(--ion-color-primary-contrast);
      text-decoration: none;
      border-radius: 20px;
      background-color: #1a73b3;
      border-color: var(--ion-color-primary-contrast);
	  cursor: pointer;
    }

    &:focus {
      outline: none;
      box-shadow: 0 0 4px rgba(0, 123, 255, 0.6);
    }
  }
}

.warning-section1 {
  display: flex;
  align-items: center;
  color: #d3a900;
  padding: 10px 10px 10px 13px;
  background-color: #f4f5f8 !important;
  justify-content: center;
}

.warning-text1 {
  color: rgb(101 101 101 / 38%);
  font-family: 'Arial', sans-serif;
  background-color: #f4f5f8;
  font-size: 0.66rem;
}

.warning-section2 {
  color: #a8a9a9;
  margin-right: 8px;
  margin-left: 12px;
  background-color: transparent !important;
}

.warning-text2 {
  font-family: 'Arial', sans-serif;
  color: rgb(101 101 101 / 38%);
  font-size: 0.66rem;
}

.response-container {
  max-height: 0;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0;
  transform: translateY(-10px);
  
  &.expanded {
    opacity: 1;
    transform: translateY(0);
	max-height: 100%; /* Allow full visibility when expanded */
	margin-top: 0px;
    padding-top: 0px;
    padding-bottom: 0px;
  }
  
  .response-content {
    background-color: #f5f5f5;
    padding: 8px;
    border-radius: 6px;
    font-size: 13px;
    line-height: 1.4;
    color: #666;
    /*white-space: pre-wrap;*/
    word-wrap: break-word;
  }
}

.response-container.expanded {
    
}
  

/* Opcional: Agregar efecto hover al contenedor de la pregunta */
.question-header:hover {
  .expand-icon {
    color: darken(#9ac7c7, 10%);
  }
}

.small-text-response {
  line-height: 1.4;
  color: #898989;
  margin-top: 8px;
}

/* Parrafo de dscripctivo del modelo */
.model-description {
  font-size: 16px;
  color: #666;
  line-height: 1.5;
  margin-bottom: 20px;
}

/* Estilos optimizados para el switch de modo */
.mode-switch-container {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  justify-content: center;
  
  .mode-label {
    color: var(--ion-color-medium);
    font-size: 0.875rem;
    font-weight: 500;
  }
}

.toggle-switch {
  position: relative;
  display: inline-block;
  width: 130px; /* Reducido de 160px */
  height: 30px; /* Reducido de 34px */
  
  input {
    opacity: 0;
    width: 0;
    height: 0;
    
    &:checked + .slider {
      background-color: var(--ion-color-light-shade);
      
      &:before {
        transform: translateX(65px); /* Ajustado para el nuevo tamaño  */
      }
    }
  }
  
  .slider {
    position: relative;
    cursor: pointer;
    width: 100%;
    height: 100%;
    background-color: var(--ion-color-secondary-tint);
    transition: .3s;
    border-radius: 30px;
    display: flex;
    align-items: center;
    justify-content: space-around;
    padding: 0 8px;
    
    &:before {
      content: "";
      position: absolute;
      height: 24px; /* Reducido de 26px */
      width: 59px; /* Reducido de 75px */
      left: 3px;
      bottom: 3px;
      background-color: white;
      transition: .3s;
      border-radius: 30px;
    }
    
    .mode-text {
      font-size: 0.813rem; /* Reducido de 0.875rem */
      font-weight: 500;
      transition: color 0.2s ease;
      z-index: 1;
      user-select: none;
      color: rgba(255, 255, 255, 0.5);
      padding: 0 4px;
      
      &.active {
        color: white;
      }
      
      &.casual {
        margin-right: 4px;
      }
      
      &.work {
        margin-left: 4px;
      }
    }
  }
}

/* Media queries para responsividad */
@media (max-width: 768px) {
  .toggle-switch {
    width: 120px;
    height: 28px;
    
    .slider:before {
      width: 58px;
      height: 22px;
    }
    
    input:checked + .slider:before {
      transform: translateX(58px);
    }
    
    .slider .mode-text {
      font-size: 0.75rem;
    }
  }
}

/* Input text area styles */
.textarea-container ion-label {
  -webkit-transform: none;
  font-size: 15px;
  padding: 10px 0px 7px 14px;
  color: #333;
}

.textarea-container ion-textarea {
    width: 100%;
    max-width: clamp(280px, 76vw, 800px); /* Mínimo 280px, máximo 600px, ajustable con el 50% del ancho de la pantalla */
    min-width: 280px; /* Anchura mínima en pantallas pequeñas */
    padding: 14px;
    margin: 11px 11px 11px 11px; /* Centrar el textarea */
    font-size: 15px;
    transition: all 0.2s ease-in-out;
    border-radius: 7px;
    background-color: #f6f6f6;
    color: rgb(0, 0, 0);
    border: 1px solid hsla(53.13, 87.56%, 72.01%, 0.64);
}

.textarea-container ion-textarea:hover {
    border: 1px solid #2dd55b;
}

/* Media query para ajustar en pantallas pequeñas */
@media (max-width: 400px) {
    .textarea-container ion-textarea {
        width: 95%; /* Ajuste del ancho en pantallas pequeñas */
    }
}

/* General button container */
.wrap-out-button {
  text-align: center;
  margin-top: 20px;
}

/* Custom styles for the button */
.wrap-button {
  --black-700: hsla(0, 0%, 12%, 1);
  --border-radius: 30px;
  --transition: 0.3s ease-in-out;

  position: relative;
  display: flex;
  align-items: center;
  gap: 0.5rem;

  width: 161px;
  height: 42px;
  padding: 0.5rem 1rem;
  font-size: 15px;
  font-weight: bold;
  font-family: 'Montserrat';
  color: #fff;
  border-radius: var(--border-radius);
  background-color: #1a73b3;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transition: background-color var(--transition), transform var(--transition), box-shadow var(--transition);

  /* Remove default background if using Ionic
  --background: transparent; */

  /* Hover and focus effects */
  &:hover {
    background-color: hsla(209.23, 95%, 54%, 0.75);
    background-image: radial-gradient(at 51% 89%, hsl(266, 45%, 74%) 0px, transparent 50%), radial-gradient(at 100% 100%, hsl(266, 36%, 60%) 0px, transparent 50%), radial-gradient(at 22% 91%, hsl(266, 36%, 60%) 0px, transparent 50%);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
    transform: translateY(-2px);
  }

  &:active {
    transform: scale(0.98);
  }

  &[disabled] {
    background-color: #cccccc;
    cursor: not-allowed;
    box-shadow: none;
	font-family: 'Montserrat';
  }
}

/* Error message section */
.error-message {
  background-color: rgba(#ff3b30, 0.1);
  border-left: 4px solid #ff3b30;
  color: #d42f2f;
  padding: 12px 16px;
  margin: 12px 0;
  border-radius: 4px;
  font-size: 0.95rem;
  line-height: 1.4;
  
  /* Agregamos una suave animación de entrada */
  animation: slideIn 0.3s ease-out;
  
  /* Agregamos un ícono de advertencia (opcional) */
  &::before {
    content: "⚠️";
    margin-right: 8px;
    font-size: 1.1em;
    vertical-align: middle;
  }
  
  /* Efecto hover sutíl */
  &:hover {
    background-color: rgba(#ff3b30, 0.15);
    transition: background-color 0.2s ease;
  }
}

/* Keyframes para la animación de entrada */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Mensaje para cuando no encuentra ninguna respuesta en la base de conocimiento */
.no-info-message {
    text-align: center;
    padding: 2rem;
    background: var(--ion-color-light);
    border-radius: 8px;
    margin: 1rem 0;

    ion-icon {
        font-size: 3rem;
        color: var(--ion-color-warning);
        margin-bottom: 1rem;
    }

    h3 {
        color: var(--ion-color-dark);
        margin-bottom: 1rem;
    }

    ul {
        text-align: left;
        padding-left: 2rem;
        margin: 1rem 0;
        
        li {
            margin-bottom: 0.5rem;
            color: var(--ion-color-medium);
        }
    }
}

.system-message {
    border: none !important;
    box-shadow: none !important;
    background: transparent !important;
}

/* Styles for the spinner when loading */
.loading-spinner {
  font-size: 1.4rem; /* To make it more visible */
}

/* Custom (No hay consultas y flecha tab sidebar) icon styling */
ion-icon {
     font-size: 1.5rem;
     color: #c3b6b6;
}

/* Optional keyframes for sparkle effect */
@keyframes sparkle {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
}

/* If you need to keep the sparkle animation */
.sparkle {
  width: 1.5rem;
  height: 1.5rem;
  animation: sparkle 2s infinite ease-in-out;
  fill: #fff;
}


/* Respuesta */
.response-card {
  margin-top: 14px;
  padding: 11px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
}

.response-card ion-card-title {
  font-size: 16px;
  color: #2167ae;
}

.section-response-title {
  font-size: 24px;
  color: #939db6;
  margin-bottom: 8px;
}

/* Styles for metadata response */
.metadata-section {
  margin-top: 11px;
  padding: 13px;
  background-color: #f9f9f9;
  border-radius: 8px;
  font-size: 12px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  display: block !important; /* Forzar visualización */
}

.metadata-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0px;
}

.metadata-details {
  display: grid;
  gap: 2px;
}

.metadata-row {
  padding: 5px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  
  .label {
    font-weight: 600;
    color: #666;
  }
  
  .value {
    color: #333;
  }
}

.page-content-section {
  margin-top: 20px;
  
  .page-content-text {
    background: white;
    padding: 15px;
    border-radius: 6px;
    border: 1px solid #eee;
    max-height: 300px;
    overflow-y: auto;
    white-space: pre-wrap;
  }
}

/* Estilos de visualizacion de PDF */
.pdf-viewer-container {
    margin-top: 20px;
	padding: 0 10px 10px 10px;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.pdf-frame-container {
    width: 100%;
    height: 600px;
    position: relative;
    background: #f5f5f5;
    
    iframe {
        width: 100%;
        height: 100%;
        border: none;
    }
}

.download-section {
    padding: 15px;
    text-align: right;
    border-top: 1px solid #eee;
}

.download-button {
    padding: 8px 16px;
    background-color: #304cff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;

    &:hover {
        background-color: darken(#304cff, 10%);
    }
}



/* Estilos globales para los popovers de ion-select */
::ng-deep {
  .select-interface-option {
    font-family: 'Montserrat' !important;
    --color: #333333 !important;
    --padding-start: 16px !important;
    --padding-end: 16px !important;
    --padding-top: 12px !important;
    --padding-bottom: 12px !important;
    --background: #ffffff !important;
    --background-activated: #f4f5f8 !important;
    --background-hover: #f9f9f9 !important;

    &.select-interface-option-selected {
      --background: #f4f5f8 !important;
      color: #9ac7c7 !important;
      font-weight: 500 !important;
    }
  }

  ion-select-popover {
    --width: 200px !important;
    --border-radius: 8px !important;
    --box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;

    .popover-content {
      background: white !important;
    }

    ion-list {
      padding: 8px 0 !important;
      background: white !important;
    }

    ion-item {
      --background: transparent !important;
      --background-hover: #f9f9f9 !important;
      --padding-start: 16px !important;
      --padding-end: 16px !important;
      --padding-top: 12px !important;
      --padding-bottom: 12px !important;
      --min-height: 42px !important;
      font-size: 14px !important;
      color: #333333 !important;
      transition: background-color 0.2s ease !important;

      &:hover {
        --background: #f9f9f9 !important;
      }

      &.item-select-selected {
        color: #9ac7c7 !important;
        font-weight: 500 !important;
        --background: #f4f5f8 !important;
      }
    }
  }

  /* Mejora del estilo del select en sí */
  ion-select {
    .select-text {
      font-family: 'Montserrat' !important;
      font-size: 14px !important;
      color: #333333 !important;
    }

    .select-icon {
      color: #9ac7c7 !important;
      opacity: 1 !important;
    }

    &.select-expanded {
      .select-icon {
        transform: rotate(180deg) !important;
      }
    }
  }

  /* Animación suave para el icono del select */
  .select-icon {
    transition: transform 0.2s ease !important;
  }
}



/* Estilos de PDF */
.pdf-viewer-container {
  margin: 20px 0;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  background: #fff;

  .pdf-viewer {
    width: 100%;
    height: 600px;
    border: none;
  }
}

:host {
  ::ng-deep {
    /* Títulos */
    h1.main-title {
      font-size: 35px !important;
      color: #9ac7c7 !important;  /* Color turquesa para el título */
      text-align: left !important;
      margin-bottom: 10px !important;
    }

    h1.response-subtitle {
      font-size: 1.2rem !important;
      color: #222 !important;
      margin-top: 2rem !important;
      border-bottom-width: 2px !important;
    }

    h2.response-subtitle {
      font-size: 1.1rem !important;
      color: #2a2a2a !important;
      margin-top: 1.8rem !important;
      border-bottom-width: 1.5px !important;
    }

    h3.response-subtitle {
      font-size: 1rem !important;
      color: #333 !important;
      margin-top: 1.5rem !important;
    }

    h4.response-subtitle {
      font-size: 0.9rem !important;
      color: #444 !important;
      margin-top: 1.2rem !important;
      border-bottom: none !important;
      padding-bottom: 0.2rem !important;
    }

    /* Listas numeradas personalizadas */
    .custom-list {
      counter-reset: item !important;
      margin: 1rem 0 !important;
      padding-left: 0 !important;
      list-style-type: none !important;

      .list-item {
        display: flex !important;
        margin-bottom: 1rem !important;
        line-height: 1.5 !important;
        color: #333 !important;
        text-align: justify !important;
        
        .item-number {
          min-width: 2rem !important;
          color: #666 !important;
          font-weight: 600 !important;
          margin-right: 0.5rem !important;
        }

        strong {
          color: #333 !important;
          font-weight: 600 !important;
          margin-right: 0.3rem !important;
        }
      }

      .custom-sublist {
        margin: 0.5rem 0 0.5rem 2rem !important;
        list-style-type: none !important;
        
        .sublist-item {
          position: relative !important;
          padding-left: 1.5rem !important;
          margin-bottom: 0.5rem !important;
          line-height: 1.5 !important;
          color: #555 !important;

          &::before {
            content: "•" !important;
            position: absolute !important;
            left: 0 !important;
            color: #666 !important;
          }
        }
      }
    }
  }
}

:host ::ng-deep {
  .formatted-response {
    background-color: #f9f9f9;
    padding: 16px 16px 16px 16px;
    margin: 16px 0 0 0;
    border-radius: 8px;
    line-height: 1.6;
    font-family: 'Montserrat';
    font-size: 0.9rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);

    /* Título de la sección de respuesta */
    .section-response-title {
      font-size: 24px !important;
      color: #939db6 !important;
      margin-bottom: 8px !important;
      font-weight: 600 !important;
    }

    /* Títulos */
    h1.response-subtitle {
      font-size: 1.3rem !important;
      color: #222 !important;
      margin-top: 2rem !important;
      border-bottom-width: 2px !important;
    }

    h2.response-subtitle {
      font-size: 1.2rem !important;
      color: #2a2a2a !important;
      margin-top: 1.8rem !important;
      border-bottom-width: 1.5px !important;
    }

    h3.response-subtitle {
      font-size: 1.1rem !important;
      color: #333 !important;
      margin-top: 1.5rem !important;
    }

    h4.response-subtitle {
      font-size: 1rem !important;
      color: #444 !important;
      margin-top: 1.2rem !important;
      border-bottom: none !important;
      padding-bottom: 0.2rem !important;
    }

    /* Reset de márgenes para títulos */
    h1.response-subtitle:first-child,
    h2.response-subtitle:first-child,
    h3.response-subtitle:first-child,
    h4.response-subtitle:first-child {
      margin-top: 0 !important;
    }

    /* Espaciado después de títulos */
    h1.response-subtitle + p,
    h2.response-subtitle + p,
    h3.response-subtitle + p,
    h4.response-subtitle + p {
      margin-top: 0.8rem !important;
    }

    /* Elementos de texto */
    p {
      margin-bottom: 12px !important;
      text-align: justify !important;
      line-height: 1.6 !important;
    }

    strong {
      color: #333 !important;
      font-weight: 600 !important;
    }

    em {
      color: #555 !important;
      font-style: italic !important;
    }

    /* Referencias inline */
    .reference {
      color: #666 !important;
      font-size: 0.9em !important;
      margin-left: 0.3em !important;
    }

    /* Listas numeradas */
    .custom-list {
      margin: 1rem 0 !important;
      padding-left: 1rem !important;

      .list-item {
        display: flex !important;
        align-items: flex-start !important;
        margin-bottom: 1rem !important;
        line-height: 1.5 !important;
        color: #333 !important;
        text-align: justify !important;
        
        .item-number {
          min-width: 2rem !important;
          color: #666 !important;
          font-weight: 600 !important;
          margin-right: 0.5rem !important;
        }

        strong {
          color: #333 !important;
          font-weight: 600 !important;
          display: inline-block !important;
          margin-right: 0.3rem !important;
        }
      }
    }

    /* Sublistas */
    .custom-sublist {
      margin: 0.5rem 0 0.5rem 2rem !important;
      list-style-type: none !important;
      
      .sublist-item {
        position: relative !important;
        padding-left: 1.5rem !important;
        margin-bottom: 0.8rem !important;
        line-height: 1.5 !important;
        color: #555 !important;
        text-align: justify !important;

        &::before {
          content: "•" !important;
          position: absolute !important;
          left: 0 !important;
          color: #666 !important;
          font-size: 1.2em !important;
          line-height: 1.2 !important;
        }

        em {
          font-style: italic !important;
          color: #444 !important;
          margin-right: 0.3rem !important;
        }
      }
    }

    /* Lista de referencias */
    .reference-container {
        margin-top: 1rem !important;
    
        .reference-line {
            margin-bottom: 1.2rem !important;
            padding-left: 0.5rem !important;
            color: #666 !important;
            font-size: 0.9rem !important;
            line-height: 1.6 !important;
    
            br {
                content: "" !important;
                display: block !important;
                margin: 0.2rem 0 !important;
            }
    
            .document-link {
                color: #1a73b3 !important;
                text-decoration: none !important;
                
                &:hover {
                    text-decoration: underline !important;
                }
            }
        }
    }

    /* Enlaces */
    .custom-link {
      color: #2196F3 !important;
      text-decoration: none !important;
      transition: color 0.2s ease !important;
      
      &:hover {
        color: #1976D2 !important;
        text-decoration: underline !important;
      }
    }

    /* Tablas */
    .custom-table {
      width: 100% !important;
      margin: 1rem 0 !important;
      border-collapse: collapse !important;
      font-size: 0.9rem !important;
      background-color: white !important;

      .table-header {
        background-color: #f5f5f5 !important;
        color: #333 !important;
        padding: 0.8rem !important;
        text-align: left !important;
        border: 1px solid #ddd !important;
        font-weight: 600 !important;
      }

      .table-cell {
        padding: 0.8rem !important;
        border: 1px solid #ddd !important;
        color: #666 !important;
      }

      .table-row {
        &:nth-child(even) {
          background-color: #fafafa !important;
        }
        &:hover {
          background-color: #f0f0f0 !important;
        }
      }
    }

    /* Tabla estándar */
    table {
      width: 100% !important;
      border-collapse: collapse !important;
      margin: 1em 0 !important;
      font-size: 0.9rem !important;
      color: #333 !important;

      th {
        background-color: #f5f5f5 !important;
        font-weight: bold !important;
        text-align: center !important;
        padding: 10px !important;
        border: 1px solid #ddd !important;
      }

      td {
        padding: 10px !important;
        text-align: left !important;
        vertical-align: top !important;
        border: 1px solid #ddd !important;
      }

      tr:hover {
        background-color: #f0f0f0 !important;
        transition: background-color 0.2s ease !important;
      }
    }
  }
}

/* Estilos para el modo casual */
:host ::ng-deep {
  .casual-paragraph {
    font-size: 0.95rem !important;
    line-height: 1.5 !important;
    margin-bottom: 0.8rem !important;
    color: #333 !important;
    text-align: left !important;
    font-family: 'Montserrat', sans-serif !important;
  }
  
  .casual-title {
    font-size: 1.1rem !important;
    font-weight: 600 !important;
    color: #1a73b3 !important;
    margin: 1rem 0 0.5rem 0 !important;
  }
  
  .casual-list {
    padding-left: 1.5rem !important;
    margin: 0.8rem 0 !important;
    list-style-type: none !important;
    
    .casual-list-item {
      position: relative !important;
      padding-left: 0.5rem !important;
      margin-bottom: 0.5rem !important;
      
      &::before {
        content: "•" !important;
        position: absolute !important;
        left: -1rem !important;
        color: #1a73b3 !important;
      }
    }
  }
  
  .casual-numbered-item {
    display: flex !important;
    margin-bottom: 0.5rem !important;
    
    .casual-number {
      min-width: 1.5rem !important;
      color: #1a73b3 !important;
      font-weight: 600 !important;
    }
  }
  
  .casual-emphasis {
    font-style: italic !important;
    color: #555 !important;
  }
  
  .casual-strong {
    font-weight: 600 !important;
    color: #333 !important;
  }
  
  .casual-emoji {
    font-size: 1.2rem !important;
    display: inline-block !important;
    margin: 0 0.15rem !important;
    vertical-align: middle !important;
  }
  
  /* Estilo específico para respuestas casual */
  ion-card.response-card {
    &:has(.casual-paragraph) {
      background-color: #f8f9fa !important;
      border-radius: 12px !important;
      border-left: 3px solid #1a73b3 !important;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08) !important;
    }
  }
}

/* Ventana modal de documentos */
.document-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    display: flex;
    justify-content: center;
    align-items: center;
    animation: fadeIn 0.2s ease-out;
    
    .modal-content {
        background: white;
        border-radius: 12px;
        width: 90%;
        height: 90%;
        max-width: 1200px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        display: flex;
        flex-direction: column;
        overflow: hidden;
        animation: slideIn 0.3s ease-out;
    }
    
    .modal-header {
        padding: 1rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid #eee;
        background: #f8f9fa;
        
        h3 {
            margin: 0;
            font-size: 1.2rem;
            color: #333;
            font-weight: 500;
        }
        
        .modal-actions {
            display: flex;
            gap: 0.5rem;
            
            ion-button {
                --padding-start: 0.8rem;
                --padding-end: 0.8rem;
                
                ion-icon {
                    font-size: 1.2rem;
                }
                
                &:hover {
                    opacity: 0.8;
                }
            }
        }
    }
    
    .modal-body {
        flex: 1;
        overflow: hidden;
        padding: 0;
        background: #f1f1f1;
        
        iframe {
            width: 100%;
            height: 100%;
            border: none;
            background: white;
        }
    }
}

/* Animaciones */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Estilos para los enlaces de documento */
.document-link {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: #f8f9fa;
    border-radius: 6px;
    color: #0066cc;
    text-decoration: none;
    transition: all 0.2s ease;
    margin: 0.5rem 0;
    border: 1px solid #e9ecef;
    
    ion-icon {
        font-size: 1.2rem;
    }
    
    &:hover {
        background: #e9ecef;
        color: #004999;
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    }
}

@media (max-width: 768px) {
    .metadata-columns {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

.pdf-viewer-container .pdf-viewer {
      height: 400px;
    }

  .sidebar-content-wrapper {
    position: absolute;
    height: 100%;
    z-index: 1000;
	    overflow-y: auto;
  }

  .main-content {
    margin-left: 0;
    width: 100%;
  }

  .sidebar-closed .main-content {
    margin-left: 7px;
  }

  .sidebar-logo {
    max-width: 285px; /* Ajustado para móviles */
  }

/* Estilos para el panel de documentos */
.documents-panel {
  width: 400px;
  height: 100vh;
  position: fixed;
  top: 0;
  right: -400px;
  background: #ffffff;
  box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1);
  transition: right 0.3s ease;
  z-index: 1000;
  overflow-y: auto;

  &.open {
    right: 0;
  }

  ion-header {
    ion-toolbar {
      --background: #9ac7c7;
      --color: white;
    }
  }
}

/* Estilos para las pestañas de documentos */
.documents-tabs {
  ion-tab-bar {
    --background: #f5f5f5;
  }

  ion-tab-button {
    --color: #666;
    --color-selected: #9ac7c7;
    
    &.tab-selected {
      ion-icon, ion-label {
        color: #9ac7c7;
      }
    }
  }
}

/* Estilos para la lista de documentos */
.documents-list {
  ion-item {
    --padding-start: 16px;
    --padding-end: 16px;
    --min-height: 60px;
    
    h3 {
      font-size: 16px;
      font-weight: 500;
      color: #333;
      margin-bottom: 4px;
    }
    
    p {
      font-size: 14px;
      color: #666;
    }
    
    ion-icon {
      color: #9ac7c7;
      font-size: 24px;
    }
    
    &:hover {
      --background: #f9f9f9;
    }
  }
}

/* Botón flotante para abrir el panel */
ion-fab {
  margin-right: 16px;
  margin-top: 16px;
  
  ion-fab-button {
    --background: #9ac7c7;
    --background-hover: #8bb6b6;
    --background-activated: #8bb6b6;
    --box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16);
  }
}
}