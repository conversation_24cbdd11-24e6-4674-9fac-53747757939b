{"name": "CogniSearch", "version": "2.0.3", "author": "Ionic Framework", "homepage": "https://ionicframework.com/", "scripts": {"build": "ng build --configuration=production", "build:dev": "ng build --configuration=development", "start": "ng serve", "watch": "ng build --watch --configuration development"}, "private": true, "dependencies": {"@angular/animations": "18.0.0", "@angular/common": "18.0.0", "@angular/compiler": "18.0.0", "@angular/core": "18.0.0", "@angular/forms": "18.0.0", "@angular/platform-browser": "18.0.0", "@angular/platform-browser-dynamic": "18.0.0", "@angular/router": "18.0.0", "@capacitor/app": "6.0.1", "@capacitor/core": "6.1.2", "@capacitor/haptics": "6.0.1", "@capacitor/keyboard": "6.0.2", "@capacitor/status-bar": "6.0.1", "@ionic/angular": "^8.0.0", "chart.js": "4.4.5", "ionicons": "7.0.0", "rxjs": "~7.8.0", "tslib": "2.3.0", "zone.js": "~0.14.2", "docx": "^9.1.1"}, "devDependencies": {"@angular-devkit/build-angular": "^18.2.12", "@angular/cli": "18.0.0", "@angular/compiler-cli": "18.0.0", "@angular/language-service": "18.0.0", "@capacitor/cli": "6.1.2", "@ionic/angular-toolkit": "11.0.1", "typescript": "~5.4.0"}, "description": "Democratising information with CogniSearch"}