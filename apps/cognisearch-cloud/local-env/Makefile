# Makefile para gestionar docker compose y tareas locales

help:
	@echo "Comandos disponibles:"
	@echo "  make help            - <PERSON><PERSON><PERSON> esta ayuda."
	@echo "  make build           - Construye las imágenes Docker usando docker compose."
	@echo "  make up              - Levanta todos los servicios definidos en docker compose."
	@echo "  make down            - Baja todos los servicios definidos en docker compose."
	@echo "  make restart         - Reinicia todos los servicios."
	@echo "  make logs            - Muestra los logs de los servicios en tiempo real."
	@echo "  make clean           - Elimina contenedores, redes y volúmenes creados por docker compose."
	@echo "  make rebuild         - Reconstruye las imágenes y levanta los servicios."
	@echo "  make shell-backend   - Abre una terminal en el contenedor del backend."
	@echo "  make shell-frontend  - Abre una terminal en el contenedor del frontend."
	@echo "  make test-backend    - Ejecuta pruebas automatizadas en el backend."
	@echo "  make test-frontend   - Ejecuta pruebas automatizadas en el frontend."

build:
	docker compose build

up:
	docker compose up -d

down:
	docker compose down

restart:
	docker compose down && docker compose up -d

logs:
	docker compose logs -f

clean:
	docker compose down --volumes --remove-orphans

rebuild:
	docker compose down --volumes --remove-orphans
	docker compose build
	docker compose up -d

shell-backend:
	docker exec -it backend_service sh

shell-frontend:
	docker exec -it frontend_service sh

test-backend:
	docker exec -it backend_service pytest

test-frontend:
	docker exec -it frontend_service npm test
