services:
  backend:
    build:
      context: ../cgnsrch-chatbot-be-pfapi
      dockerfile: Dockerfile
    container_name: backend_service
    ports:
      - "5000:5000"
    environment:
      # Mantén tus variables existentes
      - GPT_API_KEY=${GPT_API_KEY}
      - OPENAI_TOKEN=${OPENAI_TOKEN}
      - ASSISTANT_ID_JURIDICO=${ASSISTANT_ID_JURIDICO}
      - ASSISTANT_ID_CALIDAD=${ASSISTANT_ID_CALIDAD}
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - AWS_REGION=${AWS_REGION}
      - BUCKET_NAME=${BUCKET_NAME}
      - BUCKET_OWNER_ID=${BUCKET_OWNER_ID}
      - AWS_ACCOUNT_ID=${AWS_ACCOUNT_ID}
    volumes:
      - ./local-env/.env:/app/local-env/.env:ro
      - ../cgnsrch-chatbot-fe-ionic/src/app/services/auth.config.json:/app/config/auth.config.json:ro
    networks:
      - app_network

  frontend:
    build:
      context: ../cgnsrch-chatbot-fe-ionic
      dockerfile: Dockerfile
    container_name: frontend_service
    ports:
      - "80:80"
    environment:
      - BACKEND_URL=http://localhost:5000
    networks:
      - app_network
    restart: always

networks:
  app_network:
    driver: bridge