# app/main.py

import os
import time
import json
import asyncio
import tempfile
import datetime
import threading


from pathlib import Path
from functools import wraps
from pydantic import BaseModel
from utils.healthcheck import HealthCheck # Ponerle punto en Local al correr con .bat
from typing import Optional, Dict, Any, List, Union
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse, FileResponse
from fastapi import Request, FastAPI, APIRouter, HTTPException, UploadFile, File, Form, Response

# Importar constantes y conexiones
from utils.constants import ( # Ponerle punto en Local al correr con .bat
    ASSISTANT_IDS,
    INTERNAL_ID_TO_ROLE,
    ASSISTANT_ID_MAPPING,
    BUCKET_NAME,
    GPT_MODEL
)
from utils.connections import s3_client, assistant_client, casual_client # Ponerle punto en Local al correr con .bat

app = FastAPI()
router = APIRouter()

# main.py - Ajusta el bloque principal de ejecución
if __name__ == "__main__":
    import uvicorn
    
    print("="*50)
    print("Iniciando servidor FastAPI...")
    print(f"Host: 0.0.0.0")
    print(f"Port: 5000")
    print(f"CORS permitidos: {ALLOWED_ORIGINS}")
    print("="*50)
    
    try:
        uvicorn.run(
            "main:app",  # Usa la notación de punto para importar la app
            host="0.0.0.0",
            port=5000,
            reload=True,
            log_level="info"
        )
    except Exception as e:
        print(f"Error iniciando servidor: {e}")

# Agregar el endpoint de health check
@app.get("/health", response_model=HealthCheck, tags=["Health Check"])
async def health_check():
    """
    Endpoint para verificar el estado del servicio.
    Retorna un 200 OK si el servicio está funcionando correctamente.
    """
    return HealthCheck(status="OK")

# Agregar el endpoint raíz que redirecciona a /health
@app.get("/")
async def root():
    """Redirecciona a /health para facilitar la verificación del servicio"""
    return {"message": "Service is running", "health_check": "/health"}

# Diccionario para mantener los threads activos por sesión
active_threads: Dict[str, str] = {}
    
# Asegúrate de que estos orígenes estén al inicio del archivo
ALLOWED_ORIGINS = [
    "http://localhost",
    "http://localhost:80",
    "http://localhost:4200",
    "http://localhost:5000",
    "http://localhost:8100",
    "https://dev.frontend.cognisearch.cl",
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Permite todos los orígenes en desarrollo
    allow_credentials=False,  # Cambiado a False para permitir wildcard
    allow_methods=["*"],
    allow_headers=["*"],
    expose_headers=["*"],
    max_age=3600,
)

def log_error(context: str, error: Exception, user: str = None):
    """
    Función para logging de errores de manera consistente
    """
    print_separator("!")
    print(f"❌ ERROR EN {context.upper()}:")
    if user:
        print(f"👤 Usuario: {user}")
    print(f"💥 Error: {str(error)}")
    print(f"📍 Tipo: {type(error).__name__}")
    print_separator("!")
    
CACHE_FILE = "s3_cache.json"
CACHE_UPDATE_INTERVAL = 86400  # 24 horas

class S3FileMapper:
    def __init__(self, s3_client, bucket_name):
        self.s3_client = s3_client
        self.bucket_name = bucket_name
        self._file_cache = self.load_cache()
        self.validate_s3_client()
        threading.Thread(target=self.update_cache_periodically, daemon=True).start()

    def validate_s3_client(self):
        """Verifica que el cliente S3 tenga acceso al bucket antes de continuar."""
        try:
            self.s3_client.list_objects_v2(Bucket=self.bucket_name, MaxKeys=1)
            print("✅ Conexión con S3 validada correctamente.")
        except Exception as e:
            print(f"❌ Error validando acceso a S3: {e}")

    def load_cache(self):
        """Carga la caché desde un archivo JSON."""
        if os.path.exists(CACHE_FILE):
            try:
                with open(CACHE_FILE, "r") as f:
                    return json.load(f)
            except Exception as e:
                print(f"⚠️ Error cargando caché: {e}")
        return {}

    def save_cache(self):
        """Guarda la caché en un archivo JSON."""
        try:
            with open(CACHE_FILE, "w") as f:
                json.dump(self._file_cache, f)
            print("✅ Caché de S3 guardada correctamente.")
        except Exception as e:
            print(f"❌ Error guardando caché: {e}")

    def update_cache(self):
        """Actualiza la caché local con los archivos del bucket S3."""
        print("🔄 Actualizando caché de S3...")
        try:
            paginator = self.s3_client.get_paginator("list_objects_v2")
            new_cache = {}
            for page in paginator.paginate(Bucket=self.bucket_name):
                for obj in page.get("Contents", []):
                    filename = os.path.basename(obj["Key"]).lower()
                    new_cache[filename] = obj["Key"]
            self._file_cache = new_cache
            self.save_cache()
            print("✅ Caché de S3 actualizada correctamente.")
        except Exception as e:
            print(f"❌ Error actualizando caché de S3: {e}")

    def update_cache_periodically(self):
        """Ejecuta la actualización de la caché cada 24 horas en un hilo separado."""
        while True:
            self.update_cache()
            time.sleep(CACHE_UPDATE_INTERVAL)

    def get_file_url(self, s3_path: str) -> Optional[str]:
        """Genera un URL presignado para acceder al archivo"""
        if not s3_path:
            return None
            
        try:
            # Extraer la key del path s3://bucket/key
            key = s3_path.replace(f"s3://{self.bucket_name}/", "")
            
            # Generar URL presignado
            url = self.s3_client.generate_presigned_url(
                'get_object',
                Params={
                    'Bucket': self.bucket_name,
                    'Key': key
                },
                ExpiresIn=3600  # URL válida por 1 hora
            )
            return url
        except Exception as e:
            print(f"❌ Error generando URL presignado: {e}")
            return None

    async def get_s3_path(self, file_id: str) -> Optional[str]:
        """Obtiene la ruta S3 usando el nombre del archivo original de OpenAI."""
        if file_id in self._file_cache:
            return self._file_cache[file_id]
        try:
            cited_file = assistant_client.files.retrieve(file_id)
            original_filename = cited_file.filename
            print(f"🔍 Buscando archivo: {original_filename}")
            try:
                paginator = self.s3_client.get_paginator('list_objects_v2')
                for page in paginator.paginate(Bucket=self.bucket_name):
                    for obj in page.get('Contents', []):
                        if original_filename.lower() == os.path.basename(obj['Key']).lower():
                            s3_path = f"s3://{self.bucket_name}/{obj['Key']}"
                            self._file_cache[file_id] = s3_path
                            print(f"✅ Archivo encontrado en S3: {obj['Key']}")
                            return s3_path
                        elif original_filename.lower().replace(" ", "") in obj['Key'].lower().replace(" ", ""):
                            s3_path = f"s3://{self.bucket_name}/{obj['Key']}"
                            self._file_cache[file_id] = s3_path
                            print(f"✅ Archivo encontrado en S3 (coincidencia parcial): {obj['Key']}")
                            return s3_path
                print(f"⚠️ No se encontró el archivo: {original_filename}")
                return None
            except Exception as e:
                print(f"❌ Error listando objetos en S3: {e}")
                return None
        except Exception as e:
            print(f"❌ Error obteniendo información del archivo: {e}")
            return None

# Inicializar
s3_mapper = S3FileMapper(s3_client, BUCKET_NAME)

@app.get("/status")
def get_status():
    return {"status": "running", "bucket": BUCKET_NAME}
    
@app.get("/file-references")
def get_file_references():
    """Devuelve la lista de archivos en caché con sus rutas S3."""
    references = [
        {"filename": filename, "s3_path": f"s3://{BUCKET_NAME}/{path}"}
        for filename, path in s3_mapper._file_cache.items()
    ]
    return {"file_references": references}

class AssistantResponse(BaseModel):
    assistant_id: str
    role: str
    content: Optional[str] = None
    error: Optional[str] = None
    status: str
    responseTime: Optional[int] = None
    
class MultiQueryRequest(BaseModel):
    query: str
    session_id: str = "default"
    email: str
    assistant_ids: List[str] = []

class MultiQueryResponse(BaseModel):
    responses: List[AssistantResponse]

class MultiResponse(BaseModel):
    question: str
    responses: List[AssistantResponse]
    timestamp: datetime.datetime
    isExpanded: bool = False
    
class QueryRequest(BaseModel):
    query: str
    mode: str = 'trabajo'
    session_id: str = "default"
    email: Optional[str] = None
    assistant_ids: Optional[List[str]] = None

class QueryResponse(BaseModel):
    response: str

async def verify_token(request: Request):
    """
    Verifica el token y extrae la información del usuario.
    
    Args:
        request (Request): La request de FastAPI
        
    Returns:
        dict: Información del usuario
        
    Raises:
        HTTPException: Si hay problemas con la autenticación
    """
    try:
        # Obtener headers
        auth_header = request.headers.get('Authorization')
        user_data = request.headers.get('X-User-Data')
        
        # Verificar que existan los headers necesarios
        if not auth_header:
            print("❌ Error: Falta header de autorización")
            raise HTTPException(
                status_code=401,
                detail="No se encontró el header de autorización"
            )
            
        if not user_data:
            print("❌ Error: Falta header de datos de usuario")
            raise HTTPException(
                status_code=401,
                detail="No se encontró información del usuario"
            )
        
        # Verificar formato del token
        if not auth_header.startswith('Bearer '):
            print("❌ Error: Formato de token inválido")
            raise HTTPException(
                status_code=401,
                detail="Formato de token inválido"
            )
            
        token = auth_header.split('Bearer ')[1]
        
        try:
            # Decodificar user_data
            user_info = json.loads(user_data)
            print(f"✅ Usuario autenticado: {user_info.get('email', 'Unknown')}")
            return user_info
            
        except json.JSONDecodeError:
            print("❌ Error: JSON de usuario inválido")
            raise HTTPException(
                status_code=401,
                detail="Error decodificando información del usuario"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ Error inesperado en verificación: {str(e)}")
        raise HTTPException(
            status_code=401,
            detail="Error en autenticación"
        )
        
def get_user_assistants(email: str) -> List[Dict[str, str]]:
    try:
        # Primero intentar la ruta de producción
        config_path = Path("/app/config/auth.config.json")
        
        # Si no existe, intentar la ruta local
        if not config_path.exists():
            local_config_path = Path(__file__).parent / "config" / "auth.config.json"
            if local_config_path.exists():
                config_path = local_config_path
            else:
                print(f"[DEBUG] Usando configuración de desarrollo para {email}")
                # Configuración hardcodeada para desarrollo
                return [
                    {"id": "asst_jur_001", "role": "juridico"},
                    {"id": "asst_cal_001", "role": "calidad"}
                ]
        
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
            
        user = next((user for user in config["authorizedUsers"] if user["email"].lower() == email.lower()), None)
        
        if user:
            print(f"[OK] Usuario encontrado: {email}")
            mapped_assistants = []
            for assistant in user["assistants"]:
                role = assistant["role"]
                internal_id = f"asst_{role[:3]}_001"
                if role in ASSISTANT_IDS and ASSISTANT_IDS[role]:
                    mapped_assistants.append({
                        "id": internal_id,
                        "role": role
                    })
            
            print(f"Asistentes disponibles: {mapped_assistants}")
            return mapped_assistants
        else:
            print(f"[DEBUG] Usuario no encontrado, usando configuración de desarrollo")
            return [
                {"id": "asst_jur_001", "role": "juridico"},
                {"id": "asst_cal_001", "role": "calidad"}
            ]
            
    except Exception as e:
        print(f"[ERROR] Error al obtener asistentes para {email}: {str(e)}")
        print("[DEBUG] Usando configuración de desarrollo por defecto")
        return [
            {"id": "asst_jur_001", "role": "juridico"},
            {"id": "asst_cal_001", "role": "calidad"}
        ]
        
@app.post("/ask/multi", response_model=MultiQueryResponse)
async def multi_assistant_query(request: Request, query_request: MultiQueryRequest):
    try:
        # Verificar autenticación y obtener datos del usuario
        user_info = await verify_token(request)
        
        # Usar el email del token en lugar del request
        user_assistants = get_user_assistants(user_info['email'])
        
        # Si no se especifican asistentes, usar todos los disponibles
        target_assistants = (
            [a for a in user_assistants if a['id'] in query_request.assistant_ids]
            if query_request.assistant_ids
            else user_assistants
        )

        if not target_assistants:
            raise HTTPException(
                status_code=400,
                detail="No se encontraron asistentes válidos para la consulta"
            )

        # Crear tareas para consultar a cada asistente en paralelo
        async def query_assistant(assistant: Dict[str, str]) -> AssistantResponse:
            try:
                # Crear un nuevo thread para cada asistente
                thread = assistant_client.beta.threads.create()
                
                # Crear el mensaje en el thread
                message = assistant_client.beta.threads.messages.create(
                    thread_id=thread.id,
                    role="user",
                    content=query_request.query
                )

                # Ejecutar el asistente
                run = assistant_client.beta.threads.runs.create(
                    thread_id=thread.id,
                    assistant_id=assistant['id']
                )

                # Esperar la respuesta
                while True:
                    run_status = assistant_client.beta.threads.runs.retrieve(
                        thread_id=thread.id,
                        run_id=run.id
                    )
                    
                    if run_status.status == "completado":
                        messages = assistant_client.beta.threads.messages.list(
                            thread_id=thread.id,
                            order="desc",
                            limit=1
                        )
                        if messages.data:
                            response_text = await process_annotations(messages.data[0].content[0].text)
                            return AssistantResponse(
                                assistant_id=assistant['id'],
                                role=assistant['role'],
                                content=response_text,
                                status="completado"
                            )
                    elif run_status.status in ["failed", "expired", "cancelled"]:
                        return AssistantResponse(
                            assistant_id=assistant['id'],
                            role=assistant['role'],
                            error=f"Error: {run_status.status}",
                            status="error"
                        )
                    
                    await asyncio.sleep(0.5)

            except Exception as e:
                return AssistantResponse(
                    assistant_id=assistant['id'],
                    role=assistant['role'],
                    error=str(e),
                    status="error"
                )

        # Ejecutar todas las consultas en paralelo
        tasks = [query_assistant(assistant) for assistant in target_assistants]
        responses = await asyncio.gather(*tasks)

        return MultiQueryResponse(responses=responses)

    except HTTPException as he:
        raise he
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

async def process_annotations(message_content) -> str:
    """Procesa las anotaciones del mensaje de manera asíncrona"""
    if not hasattr(message_content, 'annotations') or not message_content.annotations:
        return message_content.value

    processed_text = message_content.value
    citations = {}
    file_indices = {}
    current_index = 1
    processed_files = set()

    async def process_file(annotation):
        if hasattr(annotation, 'file_citation'):
            file_id = annotation.file_citation.file_id
            if file_id not in processed_files:
                processed_files.add(file_id)
                try:
                    cited_file = await asyncio.to_thread(assistant_client.files.retrieve, file_id)
                    s3_path = await s3_mapper.get_s3_path(file_id)
                    if s3_path:
                        file_url = s3_mapper.get_file_url(s3_path)
                        path_parts = s3_path.replace(f"s3://{BUCKET_NAME}/", "").split('/')
                        display_path = '/'.join(path_parts[:-1]) if len(path_parts) > 1 else ""
                        print(f"Procesando archivo: {cited_file.filename} en {display_path}")
                        return (file_id, cited_file.filename, file_url, display_path)
                except Exception as e:
                    print(f"Error procesando archivo: {e}")
                    pass
        return None

    # Procesar todos los archivos en paralelo
    tasks = [process_file(annotation) for annotation in message_content.annotations]
    results = await asyncio.gather(*tasks)

    # Eliminar duplicados y valores None
    unique_results = []
    seen = set()
    for result in results:
        if result and result[0] not in seen:
            seen.add(result[0])
            unique_results.append(result)

    # Construir citations con resultados únicos
    for i, result in enumerate(unique_results, 1):
        if result:  # Verificar que result no sea None
            file_id, filename, file_url, display_path = result
            citations[i] = (
                f'[{i}] Fuente: {filename}<br>\n'
                f'Ruta: {display_path}<br>\n'
                f'<a href="javascript:void(0)" class="document-link" '
                f'data-url="{file_url}" data-filename="{filename}" '
                f'target="_blank">'
                f'<ion-icon name="document-text-outline"></ion-icon>'
                f'Ver documento</a>'
            )
            file_indices[file_id] = i

    # Reemplazar referencias en el texto
    for annotation in message_content.annotations:
        if hasattr(annotation, 'file_citation'):
            file_id = annotation.file_citation.file_id
            if file_id in file_indices:
                processed_text = processed_text.replace(annotation.text, f'[{file_indices[file_id]}]')

    # Agregar referencias al final
    if citations:
        processed_text += "\n\nReferencias:\n"
        for i in sorted(citations.keys()):
            processed_text += f"{citations[i]}\n\n"

    return processed_text

def print_separator(char="=", length=70):
    print(f"\n{char * length}")

async def save_thread_for_finetuning(thread_ids: Union[str, List[str]], folder: str = "fine-tuning", filename: str = "finetuning_data.jsonl"):
    try:
        # Asegurarse de que thread_ids sea una lista
        if isinstance(thread_ids, str):
            thread_ids = [thread_ids]
            
        # Obtener la ruta base del backend
        base_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        folder_path = os.path.join(base_path, folder)
        
        # Crear la carpeta si no existe
        os.makedirs(folder_path, exist_ok=True)
        print(f"📂 Using folder path: {folder_path}")

        # Ruta completa del archivo
        filepath = os.path.join(folder_path, filename)
        print(f"📄 Target file path: {filepath}")

        for thread_id in thread_ids:
            try:
                print(f"\n🔄 Processing thread: {thread_id}")
                
                # Obtener mensajes del thread
                try:
                    messages = assistant_client.beta.threads.messages.list(
                        thread_id=thread_id,
                        order="asc"
                    )
                    
                    messages_list = list(messages.data)
                    print(f"📨 Found {len(messages_list)} messages in thread")

                    # Preparar la conversación
                    conversation = {
                        "messages": [
                            {
                                "role": "system",
                                "content": "You are an assistant specialized in answering questions about administrative medical documents, chilean health standards and healthcare information on norms and laws."
                            }
                        ]
                    }

                    # Procesar cada mensaje
                    valid_messages = 0
                    for msg in messages_list:
                        if msg.role not in ["user", "assistant"]:
                            continue
                            
                        try:
                            content = ""
                            if hasattr(msg, 'content') and msg.content and len(msg.content) > 0:
                                if msg.role == "assistant":
                                    content = process_annotations(msg.content[0].text)
                                else:
                                    content = msg.content[0].text.value
                            
                            if content and content.strip():
                                conversation["messages"].append({
                                    "role": msg.role,
                                    "content": content.strip()
                                })
                                valid_messages += 1
                                print(f"✓ Added {msg.role} message ({valid_messages})")
                            
                        except Exception as msg_error:
                            print(f"⚠️ Error processing message: {str(msg_error)}")
                            continue

                    # Solo guardar si hay suficientes mensajes válidos
                    if valid_messages >= 2:
                        print(f"💾 Writing conversation with {valid_messages} messages...")
                        
                        # Convertir a JSON en una sola línea y escribir
                        json_str = json.dumps(conversation, ensure_ascii=False)
                        with open(filepath, 'a', encoding='utf-8') as f:
                            f.write(json_str + '\n')
                        print(f"✅ Successfully saved conversation to {filepath}")
                        
                        if os.path.exists(filepath):
                            file_size = os.path.getsize(filepath)
                            print(f"📄 File size after writing: {file_size} bytes")
                    else:
                        print(f"⚠️ Skipping thread {thread_id}: Not enough valid messages (found {valid_messages})")

                except Exception as api_error:
                    print(f"❌ Error accessing thread {thread_id}: {str(api_error)}")
                    continue

            except Exception as thread_error:
                print(f"❌ Error processing thread {thread_id}: {str(thread_error)}")

    except Exception as e:
        print(f"❌ Critical error: {str(e)}")

    # Verificación final del archivo
    if os.path.exists(filepath):
        line_count = 0
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                for line_count, line in enumerate(f, 1):
                    try:
                        # Verificar que cada línea sea un JSON válido
                        conversation = json.loads(line)
                        # Verificar la estructura esperada
                        if not isinstance(conversation, dict) or "messages" not in conversation:
                            print(f"⚠️ Line {line_count}: Invalid conversation structure")
                        else:
                            messages = conversation["messages"]
                            if not isinstance(messages, list):
                                print(f"⚠️ Line {line_count}: 'messages' is not a list")
                            for msg in messages:
                                if not all(k in msg for k in ["role", "content"]):
                                    print(f"⚠️ Line {line_count}: Message missing required fields")
                    except json.JSONDecodeError:
                        print(f"⚠️ Line {line_count} is not valid JSON")
            
            if line_count > 0:
                print(f"✅ File verification complete: {line_count} conversations checked")
            else:
                print("⚠️ File is empty")
        except Exception as verify_error:
            print(f"❌ Error verifying file: {str(verify_error)}")
    
@app.post("/ask", response_model=QueryResponse)
async def ask_question(query_request: QueryRequest):
    try:
        if query_request.mode == 'trabajo':
            response_text = await handle_trabajo_mode(
                query_request.query,
                query_request.session_id
            )
        else:
            response_text = await handle_casual_mode(query_request.query)
        return QueryResponse(response=response_text)
    except Exception as e:
        print(f"Error procesando la solicitud: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error interno: {str(e)}")

@app.post("/ask/stream")
async def stream_response(query_request: QueryRequest):
    try:
        if query_request.mode == 'trabajo':
            return StreamingResponse(
                stream_multi_trabajo_response(
                    query_request.query,
                    query_request.session_id
                ),
                media_type="text/event-stream"
            )
        else:
            return StreamingResponse(
                stream_casual_response(
                    query_request.query,
                    query_request.session_id
                ),
                media_type="text/event-stream"
            )
    except Exception as e:
        print(f"Error en streaming: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

async def stream_multi_trabajo_response(
    query: str,
    session_id: str,
    email: str,
    assistant_ids: List[str] = None
):
    """
    Maneja el streaming de respuestas múltiples de asistentes en paralelo.
    """
    try:
        # Verificar asistentes autorizados
        user_assistants = get_user_assistants(email)
        if not user_assistants:
            yield f"data: {json.dumps({'error': 'No se encontraron asistentes autorizados'})}\n\n"
            return

        # Convertir IDs internos a IDs reales de OpenAI
        target_assistants = []
        for assistant in user_assistants:
            internal_id = assistant['id']
            role = INTERNAL_ID_TO_ROLE.get(internal_id)
            if role and ASSISTANT_IDS[role]:
                target_assistants.append({
                    'id': ASSISTANT_IDS[role],
                    'internal_id': internal_id,
                    'role': role
                })

        if not target_assistants:
            yield f"data: {json.dumps({'error': 'No se encontraron asistentes válidos'})}\n\n"
            return

        print(f"🔄 Procesando consulta en paralelo con asistentes: {target_assistants}")

        # Estados en español
        STATUS_TRANSLATIONS = {
            'starting': 'iniciando',
            'completed': 'completado',
            'in_progress': 'en_proceso',
            'failed': 'fallido',
            'expired': 'expirado',
            'cancelled': 'cancelado',
            'error': 'error',
            'requires_action': 'requiere_acción'
        }

        # Enviar estado inicial para TODOS los asistentes antes de comenzar
        for assistant in target_assistants:
            initial_status = {
                'assistant_id': assistant['internal_id'],
                'role': assistant['role'],
                'status': 'iniciando',
                'timestamp': datetime.datetime.now().isoformat()
            }
            yield f"data: {json.dumps(initial_status)}\n\n"
            print(f"📤 Estado inicial enviado para {assistant['role']}")

        async def initialize_assistant(assistant):
            """Inicializa el thread y crea la primera ejecución para un asistente"""
            try:
                # Crear thread
                thread = await asyncio.to_thread(
                    assistant_client.beta.threads.create
                )
                print(f"📝 Creado thread {thread.id} para asistente {assistant['role']}")
                
                # Crear mensaje
                await asyncio.to_thread(
                    assistant_client.beta.threads.messages.create,
                    thread_id=thread.id,
                    role="user",
                    content=query
                )
                print(f"💬 Mensaje creado en thread {thread.id}")

                # Iniciar ejecución
                run = await asyncio.to_thread(
                    assistant_client.beta.threads.runs.create,
                    thread_id=thread.id,
                    assistant_id=assistant['id']
                )
                print(f"🚀 Iniciada ejecución {run.id} en thread {thread.id}")

                yield f"data: {json.dumps({'assistant_id': assistant['internal_id'], 'role': assistant['role'], 'status': 'en_proceso', 'timestamp': datetime.datetime.now().isoformat()})}\n\n"

                yield {'thread_id': thread.id, 'run_id': run.id, 'assistant': assistant}

            except Exception as e:
                print(f"❌ Error inicializando asistente {assistant['role']}: {str(e)}")
                yield f"data: {json.dumps({'assistant_id': assistant['internal_id'], 'role': assistant['role'], 'status': 'error', 'error': str(e), 'timestamp': datetime.datetime.now().isoformat()})}\n\n"

        # Inicializar todos los asistentes en paralelo
        init_results = []
        init_tasks = [initialize_assistant(assistant) for assistant in target_assistants]
        
        # Procesar los resultados de inicialización
        async for result in async_generator_merge(*init_tasks):
            if isinstance(result, str):
                # Es una actualización de estado
                yield result
            else:
                # Es información del asistente inicializado
                init_results.append(result)

        if not init_results:
            yield f"data: {json.dumps({'error': 'Error inicializando asistentes'})}\n\n"
            return

        async def monitor_assistant(assistant_data):
            """Monitorea el estado de un asistente específico"""
            thread_id = assistant_data['thread_id'] 
            run_id = assistant_data['run_id']
            assistant = assistant_data['assistant']

            try:
                while True:
                    run_status = await asyncio.to_thread(
                        assistant_client.beta.threads.runs.retrieve,
                        thread_id=thread_id,
                        run_id=run_id
                    )
                    
                    status_es = STATUS_TRANSLATIONS.get(run_status.status, run_status.status)
                    yield f"data: {json.dumps({'assistant_id': assistant['internal_id'], 'role': assistant['role'], 'status': status_es, 'timestamp': datetime.datetime.now().isoformat()})}\n\n"

                    if run_status.status == "completed":
                        messages = await asyncio.to_thread(
                            assistant_client.beta.threads.messages.list,
                            thread_id=thread_id,
                            order="desc",
                            limit=1
                        )
                        
                        if messages.data:
                            content = messages.data[0].content[0].text
                            if "No se encontró información" in content:
                                print(f"⚠️ No se encontró información para asistente {assistant['role']}")
                                yield f"data: {json.dumps({'assistant_id': assistant['internal_id'], 'role': assistant['role'], 'status': 'completado', 'content': None})}\n\n"
                            else:
                                response_text = await process_annotations(content)
                                yield f"data: {json.dumps({'assistant_id': assistant['internal_id'], 'role': assistant['role'], 'content': response_text, 'status': 'completado'})}\n\n"
                        break

                    elif run_status.status in ["failed", "expired", "cancelled"]:
                        error_msg = {
                            "failed": "Error: La consulta falló",
                            "expired": "Error: La consulta expiró",
                            "cancelled": "Error: La consulta fue cancelada"
                        }.get(run_status.status, f"Error: {status_es}")
                        
                        print(f"❌ {error_msg} para asistente {assistant['role']}")
                        yield f"data: {json.dumps({'assistant_id': assistant['internal_id'], 'role': assistant['role'], 'error': error_msg, 'status': 'error', 'timestamp': datetime.datetime.now().isoformat()})}\n\n"
                        break

                    await asyncio.sleep(0.5)

            except Exception as e:
                error_msg = f"Error en el procesamiento: {str(e)}"
                print(f"❌ {error_msg} con asistente {assistant['role']}")
                yield f"data: {json.dumps({'assistant_id': assistant['internal_id'], 'role': assistant['role'], 'error': error_msg, 'status': 'error', 'timestamp': datetime.datetime.now().isoformat()})}\n\n"

        # Monitorear todos los asistentes en paralelo
        monitor_tasks = [monitor_assistant(assistant_data) 
                        for assistant_data in init_results]
        
        # Combinar los generadores de respuestas
        async for response in async_generator_merge(*monitor_tasks):
            yield response

    except Exception as e:
        error_msg = f"Error en el streaming: {str(e)}"
        print(f"❌ {error_msg}")
        yield f"data: {json.dumps({'error': error_msg, 'timestamp': datetime.datetime.now().isoformat()})}\n\n"

# Función auxiliar para combinar generadores asíncronos
async def async_generator_merge(*generators):
    for gen in generators:
        async for item in gen:
            yield item

@app.post("/ask/multi/stream")
async def stream_multi_response(request: Request, query_request: MultiQueryRequest):
    print_separator()
    print("🔵 NUEVA SOLICITUD MULTI-STREAM")
    print(f"📝 Query: {query_request.query}")
    print(f"🎯 Asistentes solicitados: {query_request.assistant_ids}")
    
    try:
        # Verificar token y obtener info de usuario
        try:
            user_info = await verify_token(request)
            print(f"✅ Token verificado para usuario: {user_info['email']}")
        except Exception as e:
            log_error("Verificación de Token", e)
            raise HTTPException(
                status_code=401,
                detail="Error en autenticación"
            )

        # Validar coincidencia de email
        if user_info['email'].lower() != query_request.email.lower():
            error_msg = f"Email mismatch: token={user_info['email']}, request={query_request.email}"
            print(f"❌ {error_msg}")
            raise HTTPException(
                status_code=403,
                detail="Email mismatch between token and request"
            )

        # Obtener asistentes autorizados
        try:
            user_assistants = get_user_assistants(user_info['email'])
            print(f"📋 Asistentes autorizados encontrados: {len(user_assistants)}")
            
            if not user_assistants:
                print("❌ No se encontraron asistentes autorizados para el usuario")
                raise HTTPException(
                    status_code=403,
                    detail="User has no authorized assistants"
                )
        except Exception as e:
            log_error("Obtención de Asistentes", e, user_info['email'])
            raise

        # Convertir IDs internos a IDs de OpenAI
        real_assistant_ids = []
        print("🔄 Convirtiendo IDs internos a IDs de OpenAI...")
        
        for internal_id in query_request.assistant_ids:
            role = next((a['role'] for a in user_assistants if a['id'] == internal_id), None)
            if role and role in ASSISTANT_IDS:
                real_assistant_ids.append(ASSISTANT_IDS[role])
                print(f"✓ ID convertido: {internal_id} -> {ASSISTANT_IDS[role]}")
            else:
                print(f"⚠️ ID no encontrado o no válido: {internal_id}")

        if not real_assistant_ids:
            print("❌ No se encontraron asistentes válidos para la conversión de IDs")
            raise HTTPException(
                status_code=400,
                detail="No matching assistants found"
            )

        print(f"✅ IDs convertidos exitosamente: {len(real_assistant_ids)}")
        print_separator()

        return StreamingResponse(
            stream_multi_trabajo_response(
                query=query_request.query,
                session_id=query_request.session_id,
                email=user_info['email'],
                assistant_ids=real_assistant_ids
            ),
            media_type="text/event-stream",
            headers={
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
                'X-Accel-Buffering': 'no',
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'POST, OPTIONS',
                'Access-Control-Allow-Headers': '*'
            }
        )
        
    except HTTPException as he:
        log_error(
            "HTTP Exception", 
            he, 
            query_request.email if hasattr(query_request, 'email') else None
        )
        raise he
    except Exception as e:
        log_error(
            "Error Inesperado", 
            e, 
            query_request.email if hasattr(query_request, 'email') else None
        )
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )
    
class CasualConversation:
    def __init__(self):
        self.messages = [
            {
                "role": "system", 
                "content": """Eres un asistente casual y amigable que responde en español latinoamericano. 
                            Tu tono es más relajado pero mantienes la precisión en la información. 
                            Usas un lenguaje más coloquial y cercano."""
            }
        ]

# Diccionario para mantener las conversaciones activas por sesión
active_conversations: Dict[str, CasualConversation] = {}

async def stream_casual_response(query: str, session_id: str = "default"):
    try:
        print_separator()
        print("🔵 NUEVA CONSULTA MODO CASUAL")
        print(f"📝 Pregunta: {query}")
        print_separator()
        
        start_time = time.time()
        response_text = ""

        # Obtener o crear conversación para la sesión
        if session_id not in active_conversations:
            active_conversations[session_id] = CasualConversation()
        
        conversation = active_conversations[session_id]
        
        # Agregar el mensaje del usuario al historial
        conversation.messages.append({"role": "user", "content": query})

        # Crear el stream de la respuesta
        stream = casual_client.chat.completions.create(
            model=GPT_MODEL,
            messages=conversation.messages,
            temperature=0.7,
            max_tokens=700,
            top_p=0.85,
            frequency_penalty=0.5,
            presence_penalty=0.5,
            stream=True
        )

        collected_message = {"role": "assistant", "content": ""}

        for chunk in stream:
            if hasattr(chunk.choices[0].delta, 'content') and chunk.choices[0].delta.content:
                content = chunk.choices[0].delta.content
                response_text += content
                collected_message["content"] += content
                yield f"data: {json.dumps({'content': response_text})}\n\n"

        # Agregar la respuesta completa al historial
        conversation.messages.append(collected_message)
        
        # Mantener un límite de mensajes para evitar tokens excesivos
        if len(conversation.messages) > 10:  # Mantener system + últimos 4 pares de mensajes
            conversation.messages = [conversation.messages[0]] + conversation.messages[-8:]

        log_response("RESPUESTA CASUAL COMPLETA", response_text, start_time)

    except Exception as e:
        error_msg = str(e)
        print_separator("!")
        print(f"❌ ERROR EN STREAMING CASUAL:")
        print(f"{error_msg}")
        print_separator("!")
        yield f"data: {json.dumps({'error': error_msg})}\n\n"

def log_response(label: str, content: str, start_time: float):
    print_separator()
    print(f"🔵 {label}")
    print(f"⏱️ Tiempo de respuesta: {time.time() - start_time:.2f} segundos")
    print(f"📝 Contenido:\n{content}")
    print_separator()