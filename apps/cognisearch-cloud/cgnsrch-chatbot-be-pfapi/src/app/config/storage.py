# storage.py

from pathlib import Path

class StorageConfig:
    # Rutas base
    BASE_DIR = Path(__file__).parent.parent.parent
    STORAGE_DIR = BASE_DIR / 'storage' / 'documents'
    
    # Rutas específicas
    POLICIES_DIR = STORAGE_DIR / 'policies'
    MANUALS_DIR = STORAGE_DIR / 'manuals'  # Nueva ruta para los manuales
    
    # Tipos de documentos y sus directorios
    POLICY_TYPES = {
        'life': POLICIES_DIR / 'life',
        'health': POLICIES_DIR / 'health',
        'vehicle': POLICIES_DIR / 'vehicle',
        'manuals': MANUALS_DIR  # Agregar la categoría "manuals"
    }
    
    # Configuraciones
    MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB
    ALLOWED_EXTENSIONS = {'pdf'}
    
    @classmethod
    def initialize_directories(cls):
        """Crear estructura de directorios necesaria"""
        directories = [
            cls.STORAGE_DIR,
            cls.POLICIES_DIR,
            cls.MANUALS_DIR,  # Incluir el directorio de manuals en la inicialización
            *cls.POLICY_TYPES.values()
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
            
    @classmethod
    def get_policy_directory(cls, policy_type: str) -> Path:
        """Obtener el directorio para un tipo de póliza o manual específico"""
        return cls.POLICY_TYPES.get(policy_type.lower(), cls.STORAGE_DIR)
        
    @classmethod
    def is_valid_file(cls, filename: str) -> bool:
        """Verificar si el archivo es válido"""
        return '.' in filename and \
               filename.rsplit('.', 1)[1].lower() in cls.ALLOWED_EXTENSIONS
