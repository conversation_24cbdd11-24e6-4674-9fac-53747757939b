{
    "demanda_alimentos": {
        "filename": "demanda_alimentos.docx",
        "version": "1.0",
        "updated_at": "2024-03-15",
        "description": "Plantilla para demanda de alimentos para menores",
        "category": "familia",
        "required_fields": {
            "datos_demandante": {
                "nombre": {
                    "type": "string",
                    "description": "Nombre completo del demandante",
                    "required": true
                },
                "tipo_documento": {
                    "type": "string",
                    "enum": ["DNI", "CE", "PASAPORTE"],
                    "description": "Tipo de documento de identidad",
                    "required": true
                },
                "num_documento": {
                    "type": "string",
                    "pattern": "^[0-9]{8,12}$",
                    "description": "Número de documento de identidad",
                    "required": true
                },
                "direccion": {
                    "type": "string",
                    "description": "Dirección completa del demandante",
                    "required": true
                },
                "telefono": {
                    "type": "string",
                    "pattern": "^[0-9]{9}$",
                    "description": "Número de teléfono",
                    "required": false
                }
            },
            "datos_demandado": {
                "nombre": {
                    "type": "string",
                    "description": "Nombre completo del demandado",
                    "required": true
                },
                "tipo_documento": {
                    "type": "string",
                    "enum": ["DNI", "CE", "PASAPORTE"],
                    "description": "Tipo de documento de identidad",
                    "required": true
                },
                "num_documento": {
                    "type": "string",
                    "pattern": "^[0-9]{8,12}$",
                    "description": "Número de documento de identidad",
                    "required": false
                },
                "direccion": {
                    "type": "string",
                    "description": "Dirección donde será notificado el demandado",
                    "required": true
                }
            },
            "datos_menores": {
                "type": "array",
                "minItems": 1,
                "items": {
                    "nombre": {
                        "type": "string",
                        "description": "Nombre completo del menor",
                        "required": true
                    },
                    "edad": {
                        "type": "number",
                        "minimum": 0,
                        "maximum": 17,
                        "description": "Edad del menor",
                        "required": true
                    },
                    "fecha_nacimiento": {
                        "type": "string",
                        "format": "date",
                        "description": "Fecha de nacimiento del menor",
                        "required": true
                    }
                }
            },
            "monto_solicitado": {
                "type": "number",
                "minimum": 0,
                "description": "Monto de la pensión alimenticia solicitada",
                "required": true
            },
            "fundamentos": {
                "type": "array",
                "minItems": 1,
                "description": "Lista de fundamentos que sustentan la demanda",
                "required": true
            },
            "medios_prueba": {
                "type": "array",
                "minItems": 1,
                "description": "Lista de medios probatorios",
                "required": true
            }
        },
        "placeholders": {
            "juzgado": "{juzgado}",
            "nombre_demandante": "{datos_demandante.nombre}",
            "doc_demandante": "{datos_demandante.tipo_documento} N° {datos_demandante.num_documento}",
            "dir_demandante": "{datos_demandante.direccion}",
            "lista_menores": "{lista_menores}",
            "nombre_demandado": "{datos_demandado.nombre}",
            "monto_texto": "{monto_texto}",
            "monto_numerico": "S/. {monto_solicitado}",
            "fundamentos_lista": "{fundamentos_lista}",
            "pruebas_lista": "{pruebas_lista}",
            "fecha_actual": "{fecha_actual}",
            "ciudad": "{ciudad}"
        },
        "default_values": {
            "ciudad": "Lima",
            "juzgado": "Lima"
        },
        "formatting_rules": {
            "monto_texto": "convertir_a_texto",
            "fecha_actual": "formato_fecha_larga",
            "lista_menores": "formato_lista_viñetas",
            "fundamentos_lista": "formato_lista_numerada",
            "pruebas_lista": "formato_lista_numerada"
        }
    },
    "demanda_divorcio": {
        "filename": "demanda_divorcio.docx",
        "version": "1.0",
        "updated_at": "2024-03-15",
        "description": "Plantilla para demanda de divorcio por causal",
        "category": "familia",
        "required_fields": {
            // Similar estructura para divorcio
        }
    },
    "carta_notarial": {
        "filename": "carta_notarial.docx",
        "version": "1.0",
        "updated_at": "2024-03-15",
        "description": "Plantilla para carta notarial",
        "category": "notarial",
        "required_fields": {
            // Similar estructura para carta notarial
        }
    }
}