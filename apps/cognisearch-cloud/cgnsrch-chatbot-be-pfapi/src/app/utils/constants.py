import os
from pathlib import Path
from dotenv import load_dotenv

def load_environment():
    """Carga las variables de entorno desde diferentes ubicaciones posibles"""
    env_locations = [
        Path('/app/local-env/.env'),  # Ubicación en Docker
        Path(__file__).parent.parent.parent.parent.parent / 'local-env' / '.env',  # Ubicación local
        Path.cwd() / 'local-env' / '.env'  # Ubicación relativa
    ]

    env_loaded = False
    for env_path in env_locations:
        if env_path.exists():
            print(f"✅ Archivo .env encontrado en: {env_path}")
            load_dotenv(env_path)
            env_loaded = True
            break

    if not env_loaded:
        paths_checked = "\n- ".join([str(p) for p in env_locations])
        print(f"❌ No se encontró el archivo .env en ninguna ubicación:\n- {paths_checked}")

# Cargar variables de entorno
load_environment()

# Configuración de Asistentes
ASSISTANT_IDS = {'juridico': os.getenv("ASSISTANT_ID_JURIDICO"), 'calidad': os.getenv("ASSISTANT_ID_CALIDAD")}

# Verificar asistentes configurados
for role, assistant_id in ASSISTANT_IDS.items():
    if not assistant_id:
        raise ValueError(f"❌ ASSISTANT_ID_{role.upper()} no está configurado en .env")

# Mapeos de IDs
INTERNAL_ID_TO_ROLE = {'asst_jur_001': 'juridico', 'asst_cal_001': 'calidad'}

ASSISTANT_ID_MAPPING = {'asst_jur_001': ASSISTANT_IDS['juridico'], 'asst_cal_001': ASSISTANT_IDS['calidad']}

# Variables requeridas y sus mensajes de error
REQUIRED_VARS = {"OPENAI_TOKEN": "Token de OpenAI","GPT_API_KEY": "API Key de GPT", "AWS_ACCESS_KEY_ID": "AWS Access Key ID", "AWS_SECRET_ACCESS_KEY": "AWS Secret Access Key", "AWS_REGION": "AWS Region", "BUCKET_NAME": "Nombre del Bucket S3", "AWS_ACCOUNT_ID": "ID de cuenta AWS"}

# Verificar todas las variables requeridas
missing_vars = []
for var_name, var_description in REQUIRED_VARS.items():
    if not os.getenv(var_name):
        missing_vars.append(f"❌ {var_description} ({var_name}) no está configurado en .env")

if missing_vars:
    raise ValueError("\n".join(missing_vars))

# Exportar variables verificadas
GPT_API_KEY = os.getenv("GPT_API_KEY")
GPT_MODEL = "gpt-4o-mini"
OPENAI_TOKEN = os.getenv("OPENAI_TOKEN")

AWS_ACCESS_KEY_ID = os.getenv("AWS_ACCESS_KEY_ID")
AWS_SECRET_ACCESS_KEY = os.getenv("AWS_SECRET_ACCESS_KEY")
AWS_REGION = os.getenv("AWS_REGION")
BUCKET_NAME = os.getenv("BUCKET_NAME")
AWS_ACCOUNT_ID = os.getenv("AWS_ACCOUNT_ID")
