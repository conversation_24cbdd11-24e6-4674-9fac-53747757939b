# utils/connections.py

import boto3
from openai import OpenAI
from botocore.config import Config

from .constants import (
    OPENAI_TOKEN,
    GPT_API_KEY,
    AWS_ACCESS_KEY_ID,
    AWS_SECRET_ACCESS_KEY,
    AWS_REGION,
    BUCKET_NAME
)

def init_s3_client():
    """Inicializa y retorna el cliente de S3"""
    try:
        s3_client = boto3.client(
            's3',
            aws_access_key_id=AWS_ACCESS_KEY_ID,
            aws_secret_access_key=AWS_SECRET_ACCESS_KEY,
            region_name=AWS_REGION,
            config=Config(
                retries=dict(
                    max_attempts=3
                )
            )
        )
        
        # Verificar conexión intentando listar el bucket
        s3_client.head_bucket(Bucket=BUCKET_NAME)
        print("[OK] Conexión con S3 establecida correctamente")
        return s3_client
        
    except Exception as e:
        print(f"[ERROR] Error inicializando cliente S3: {str(e)}")
        raise

def init_openai_clients():
    """Inicializa y retorna los clientes de OpenAI"""
    try:
        # Cliente para asistentes
        assistant_client = OpenAI(api_key=OPENAI_TOKEN)
        
        # Cliente para modo casual
        casual_client = OpenAI(api_key=GPT_API_KEY)
        
        print("[OK] Clientes OpenAI inicializados correctamente")
        return assistant_client, casual_client
        
    except Exception as e:
        print(f"[ERROR] Error inicializando clientes OpenAI: {str(e)}")
        raise

# Inicializar clientes
try:
    s3_client = init_s3_client()
    assistant_client, casual_client = init_openai_clients()
except Exception as e:
    print(f"[ERROR] Error crítico inicializando servicios: {str(e)}")
    raise