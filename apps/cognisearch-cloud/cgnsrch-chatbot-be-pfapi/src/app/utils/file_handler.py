# file_handler.py

import time
from openai import OpenAI
from constants import ASSISTANT_ID, THREAD_ID, OPENAI_TOKEN

# Cliente OpenAI
client = OpenAI(api_key=OPENAI_TOKEN)

def obtener_respuesta_con_metadatos(mensaje_usuario):
    print("🔵 Creando un nuevo hilo con el mensaje del usuario...")
    print(f"Mensaje enviado: {mensaje_usuario}")
    
    # Crear un nuevo hilo con el mensaje inicial del usuario
    thread = client.beta.threads.create(
        messages=[
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": mensaje_usuario}
                ]
            }
        ]
    )
    print(f"🟢 Hilo creado con ID: {thread.id}")

    # Crear el Run en el asistente configurado
    print("🔵 Creando el Run para el asistente...")
    print(f"Usando ASSISTANT_ID: {ASSISTANT_ID}")
    run = client.beta.threads.runs.create(
        thread_id=thread.id,
        assistant_id=ASSISTANT_ID,
    )
    print(f"🟢 Run creado con ID: {run.id}")

    # Esperar a que el Run esté completo
    while run.status != "completed":
        run = client.beta.threads.runs.retrieve(thread_id=thread.id, run_id=run.id)
        print(f"🔄 Estado del Run: {run.status}")
        time.sleep(0.5)

    print("🟢 El Run ha sido completado.")
    
    # Obtener todos los mensajes en el hilo después de que el Run esté completo
    mensajes = client.beta.threads.messages.list(thread_id=thread.id).data
    print(f"Mensajes en el hilo: {mensajes}")

    # Extraer respuesta y metadatos del último mensaje
    respuesta_asistente = None
    metadatos = {}

    for mensaje in mensajes:
        if mensaje.role == "assistant":
            # Asumimos que el asistente devuelve la respuesta y los metadatos en un formato específico
            # Extracción de contenido y metadatos
            for contenido in mensaje.content:
                if contenido.type == "text":
                    respuesta_asistente = contenido.text.value
                elif contenido.type == "metadata":
                    metadatos = contenido.metadata  # Suponiendo que la metadata está aquí

    # Retornar la respuesta y los metadatos
    return {
        "respuesta": respuesta_asistente,
        "metadatos": metadatos
    }

# Ejemplo de uso
if __name__ == "__main__":
