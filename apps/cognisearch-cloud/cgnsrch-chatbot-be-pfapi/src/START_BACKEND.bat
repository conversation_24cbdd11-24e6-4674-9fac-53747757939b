@echo off
:: <PERSON>biar al directorio del script (carpeta backend)
cd /d %~dp0

:: Nombre del entorno virtual
set VENV_DIR=venv

:: Crear el entorno virtual si no existe
if not exist %VENV_DIR% (
    echo Borrando cache de pip...
    python -m pip cache purge

    echo Creando entorno virtual en la carpeta backend...
    python -m venv %VENV_DIR%

    :: Activar el entorno virtual
    echo Activando el entorno virtual...
    call %VENV_DIR%\Scripts\activate

    :: Actualizar pip a la última versión disponible
    echo Actualizando pip a la ultima version disponible...
    python -m pip install --upgrade pip

    :: Instalar dependencias del core
    echo Instalando dependencias core desde requirements.txt...
    pip install -r requirements.txt

    :: Verificar instalación de dependencias críticas
    echo Verificando dependencias criticas...
    pip show boto3 >nul 2>&1
    if errorlevel 1 (
        echo Instalando boto3...
        pip install boto3
    )

    pip show fastapi >nul 2>&1
    if errorlevel 1 (
        echo Instalando fastapi...
        pip install fastapi
    )

    pip show uvicorn >nul 2>&1
    if errorlevel 1 (
        echo Instalando uvicorn...
        pip install uvicorn
    )

) else (
    :: Si el entorno virtual ya existe, activarlo y verificar dependencias
    echo Activando el entorno virtual existente...
    call %VENV_DIR%\Scripts\activate

    :: Actualizar pip
    echo Actualizando pip a la ultima version disponible...
    python -m pip install --upgrade pip

    :: Verificar dependencias críticas
    echo Verificando dependencias criticas...
    pip show boto3 >nul 2>&1
    if errorlevel 1 (
        echo Instalando boto3...
        pip install boto3
    )

    pip show fastapi >nul 2>&1
    if errorlevel 1 (
        echo Instalando fastapi...
        pip install fastapi
    )

    pip show uvicorn >nul 2>&1
    if errorlevel 1 (
        echo Instalando uvicorn...
        pip install uvicorn
    )
)

:: Iniciar el servidor FastAPI
echo Iniciando el servidor FastAPI...
uvicorn app.main:app --host 0.0.0.0 --port 5000 --reload