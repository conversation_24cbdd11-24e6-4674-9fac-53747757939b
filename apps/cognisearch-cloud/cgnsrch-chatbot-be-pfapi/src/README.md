
project-root/
├── backend/
│   ├── app/
│   │    ├── main.py
│   │    └── utils/
│   │        └── file_handler.py
│   ├── requirements.txt
│   ├── .env
│   ├── utils.py
│   └── constants.py
├────── storage/
│          ├── documents/
│          │    ├── manuals          # Manuales privados
│          │    ├── policies/        # PDFs de pólizas
│          │    │   ├── life/
│          │    │   ├── health/
│          │    │   └── vehicle/
│          │    └── temp/
│          └── storage.py             # Configuración de almacenamiento
├── frontend/
│   └── src/
│       └── assets/
│           └── documents/         # Solo documentos públicos estáticos
│               └── public-docs/   # Como manuales de usuario, etc.