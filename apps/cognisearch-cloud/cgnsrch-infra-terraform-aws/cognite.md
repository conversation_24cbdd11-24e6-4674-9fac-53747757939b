# Create User Pool

```bash
aws cognito-idp create-user-pool \
  --pool-name "CognisearchDemoPool" \
  --username-attributes "email" \
  --auto-verified-attributes "email" \
  --schema '[
    {
      "Name": "custom:tenantId",
      "AttributeDataType": "String",
      "Mutable": true,
      "Required": false
    },
    {
      "Name": "custom:featureFlags",
      "AttributeDataType": "String",
      "Mutable": true,
      "Required": false,
      "StringAttributeConstraints": {
        "MaxLength": "2048"
      }
    }
  ]'


Userpoolid-> sa-east-1_yEQPfKU2C
# Create App Client
aws cognito-idp create-user-pool-client \
  --user-pool-id sa-east-1_yEQPfKU2C \
  --client-name "DemoAppClient" \
  --no-generate-secret \
  --explicit-auth-flows "ALLOW_USER_PASSWORD_AUTH" "ALLOW_REFRESH_TOKEN_AUTH"
```


```shell
{
    "UserPoolClient": {
        "UserPoolId": "sa-east-1_yEQPfKU2C",
        "ClientName": "DemoAppClient",
        "ClientId": "1vqe7gbnt70n6c4vkdrdn1ojfa",
        "LastModifiedDate": "2024-12-21T01:27:12.810000-03:00",
        "CreationDate": "2024-12-21T01:27:12.810000-03:00",
        "RefreshTokenValidity": 30,
        "TokenValidityUnits": {},
        "ExplicitAuthFlows": [
            "ALLOW_USER_PASSWORD_AUTH",
            "ALLOW_REFRESH_TOKEN_AUTH"
        ],
        "AllowedOAuthFlowsUserPoolClient": false,
        "EnableTokenRevocation": true,
        "EnablePropagateAdditionalUserContextData": false,
        "AuthSessionValidity": 3
    }
}
```



## Create Admin Group

aws cognito-idp create-group \
  --user-pool-id sa-east-1_yEQPfKU2C \
  --group-name "admin" \
  --description "Administrators with full access"

## Create Basic Users Group

aws cognito-idp create-group \
  --user-pool-id sa-east-1_yEQPfKU2C \
  --group-name "basic-users" \
  --description "Basic users with limited access"

## Create Premium Users Group

aws cognito-idp create-group \
  --user-pool-id sa-east-1_yEQPfKU2C \
  --group-name "premium-users" \
  --description "Premium users with extended access"


# Create user
aws cognito-idp admin-create-user \
  --user-pool-id sa-east-1_yEQPfKU2C \
  --username "<EMAIL>" \
  --user-attributes '[
    {
      "Name": "email",
      "Value": "<EMAIL>"
    },
    {
      "Name": "email_verified",
      "Value": "true"
    },
    {
      "Name": "custom:custom:tenantId",
      "Value": "demo-tenant"
    },
    {
      "Name": "custom:custom:featureFlags",
      "Value": "{\"canAccessQueries\":true}"
    }
  ]' \
  --temporary-password "ChangeMe123."

# Add user to premium group
aws cognito-idp admin-add-user-to-group \
  --user-pool-id sa-east-1_yEQPfKU2C \
  --username "<EMAIL>" \
  --group-name "premium-users"

## Feature Flags Structure

```json
{
  "admin": {
    "featureFlags": {
      "canAccessQueries": true,
      "canAccessCharts": true,
      "canManageUsers": true,
      "canAccessReports": true,
      "maxQueriesPerDay": -1  // unlimited
    }
  },
  "premium-users": {
    "featureFlags": {
      "canAccessQueries": true,
      "canAccessCharts": true,
      "canAccessReports": true,
      "maxQueriesPerDay": 100
    }
  },
  "basic-users": {
    "featureFlags": {
      "canAccessQueries": true,
      "canAccessCharts": false,
      "canAccessReports": false,
      "maxQueriesPerDay": 10
    }
  }
}
```

## Update Feature Flag for a user


```bash
aws cognito-idp admin-update-user-attributes \
  --user-pool-id sa-east-1_yEQPfKU2C \
  --username "<EMAIL>" \
  --user-attributes '[
    {
      "Name": "custom:custom:featureFlags",
      "Value": "{\"canAccessQueries\":true,\"canAccessCharts\":true,\"maxQueriesPerDay\":100}"
    }
  ]'
```