# Secrets

```shell
aws secretsmanager put-secret-value \
   --secret-id cognisearch/cgnsrch-chatbot-be-secrets \
   --secret-string '{
    "GPT_API_KEY": "********************************************************************************************************************************************************************",
    "OPENAI_TOKEN": "********************************************************",
    "ASSISTANT_ID_JURIDICO": "asst_BlsI7FjYzOowkqTyQdCScfun",
    "ASSISTANT_ID_CALIDAD": "asst_sMK3uTeEJneA6GLUz9STubsy",
    "AWS_ACCESS_KEY_ID": "********************",
    "AWS_SECRET_ACCESS_KEY": "fHLI0vRReVosGTlpLbWvMEYfe4NOU9777Y4vV7JQ",
    "AWS_REGION": "us-east-2",
    "BUCKET_NAME": "bucketelpino1",
    "BUCKET_OWNER_ID": "2b84697402c500358dbf2b9a7a28f4e1dab1a2b8dbdd05d56b767ef9026f8dba",
    "AWS_ACCOUNT_ID": "************"
    }'
```
