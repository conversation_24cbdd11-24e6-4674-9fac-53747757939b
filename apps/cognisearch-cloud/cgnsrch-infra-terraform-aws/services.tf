# Add this data source to get the current AWS account ID
data "aws_caller_identity" "current" {}

# Recuperar el secreto
data "aws_secretsmanager_secret" "backend_secrets" {
  name = "cognisearch/cgnsrch-chatbot-be-secrets"
}

data "aws_secretsmanager_secret_version" "backend_secrets" {
  secret_id = data.aws_secretsmanager_secret.backend_secrets.id
}

# Parsear los secretos
locals {
  backend_secrets = jsondecode(data.aws_secretsmanager_secret_version.backend_secrets.secret_string)
}

resource "aws_ecs_task_definition" "frontend_task" {
  family                   = "frontend-task"
  network_mode             = "awsvpc" # Cambiado de bridge a awsvpc
  execution_role_arn       = aws_iam_role.ecs_task_execution_role.arn
  requires_compatibilities = ["FARGATE"] # Cambiado de EC2 a FARGATE
  cpu                      = "256"
  memory                   = "512"

  container_definitions = jsonencode([
    {
      name  = "frontend"
      image = "${data.aws_caller_identity.current.account_id}.dkr.ecr.sa-east-1.amazonaws.com/cgnsrch-chatbot-fe-ionic:latest" #var.frontend_image
      imagePullPolicy = "Always"
      logConfiguration = {
        logDriver = "awslogs"
        options = {
          "awslogs-group"         = "/ecs/frontend-task"
          "awslogs-region"        = "sa-east-1"
          "awslogs-stream-prefix" = "ecs"
        }
      }
      portMappings = [
        { containerPort = 80, hostPort = 80, protocol = "tcp" },
        { containerPort = 443, hostPort = 443, protocol = "tcp" },
        { containerPort = 22, hostPort = 22, protocol = "tcp" }
      ]
    }
  ])
}

resource "aws_ecs_task_definition" "backend_task" {
  family                   = "backend-task"
  network_mode             = "awsvpc" # Cambiado de bridge a awsvpc
  execution_role_arn       = aws_iam_role.ecs_task_execution_role.arn
  requires_compatibilities = ["FARGATE"] # Cambiado de EC2 a FARGATE
  cpu                      = "512"
  memory                   = "1024"

  container_definitions = jsonencode([
    {
      name  = "backend"
      image = "${data.aws_caller_identity.current.account_id}.dkr.ecr.sa-east-1.amazonaws.com/cgnsrch-chatbot-be-pfapi:latest" #var.backend_image
      imagePullPolicy = "Always"

      # Inyección de secretos
      secrets = [
        {
          name      = "GPT_API_KEY"
          valueFrom = "${data.aws_secretsmanager_secret.backend_secrets.arn}:GPT_API_KEY::"
        },
        {
          name      = "OPENAI_TOKEN"
          valueFrom = "${data.aws_secretsmanager_secret.backend_secrets.arn}:OPENAI_TOKEN::"
        },
        {
          name      = "ASSISTANT_ID_JURIDICO"
          valueFrom = "${data.aws_secretsmanager_secret.backend_secrets.arn}:ASSISTANT_ID_JURIDICO::"
        },
        {
          name      = "ASSISTANT_ID_CALIDAD"
          valueFrom = "${data.aws_secretsmanager_secret.backend_secrets.arn}:ASSISTANT_ID_CALIDAD::"
        },
        {
          name      = "AWS_ACCESS_KEY_ID"
          valueFrom = "${data.aws_secretsmanager_secret.backend_secrets.arn}:AWS_ACCESS_KEY_ID::"
        },
        {
          name      = "AWS_SECRET_ACCESS_KEY"
          valueFrom = "${data.aws_secretsmanager_secret.backend_secrets.arn}:AWS_SECRET_ACCESS_KEY::"
        },
        {
          name      = "AWS_REGION"
          valueFrom = "${data.aws_secretsmanager_secret.backend_secrets.arn}:AWS_REGION::"
        },
        {
          name      = "BUCKET_NAME"
          valueFrom = "${data.aws_secretsmanager_secret.backend_secrets.arn}:BUCKET_NAME::"
        },
        {
          name      = "BUCKET_OWNER_ID"
          valueFrom = "${data.aws_secretsmanager_secret.backend_secrets.arn}:BUCKET_OWNER_ID::"
        },
        {
          name      = "AWS_ACCOUNT_ID"
          valueFrom = "${data.aws_secretsmanager_secret.backend_secrets.arn}:AWS_ACCOUNT_ID::"
        }
      ]

      logConfiguration = {
        logDriver = "awslogs"
        options = {
          "awslogs-group"         = "/ecs/backend-task"
          "awslogs-region"        = "sa-east-1"
          "awslogs-stream-prefix" = "ecs"
        }
      }
      portMappings = [
        { containerPort = 80, hostPort = 80, protocol = "tcp" },
        { containerPort = 443, hostPort = 443, protocol = "tcp" },
        { containerPort = 5000, hostPort = 5000, protocol = "tcp" },
        { containerPort = 22, hostPort = 22, protocol = "tcp" }
      ]
    }
  ])
}

resource "aws_ecs_service" "frontend_service" {
  name            = "frontend-service"
  cluster         = aws_ecs_cluster.cognisearch_cluster.id
  task_definition = aws_ecs_task_definition.frontend_task.arn
  desired_count   = 1
  launch_type     = "FARGATE" # Cambiado de EC2 a Fargate

  network_configuration {
    subnets          = aws_subnet.public_subnet[*].id
    security_groups  = [aws_security_group.ecs_security_group.id]
    assign_public_ip = true # Asigna IP pública a la tarea Fargate
  }

  load_balancer {
    target_group_arn = aws_lb_target_group.frontend_tg.arn
    container_name   = "frontend"
    container_port   = var.frontend_port
  }
}

resource "aws_ecs_service" "backend_service" {
  name            = "backend-service"
  cluster         = aws_ecs_cluster.cognisearch_cluster.id
  task_definition = aws_ecs_task_definition.backend_task.arn
  desired_count   = 1
  launch_type     = "FARGATE" # Cambiado de EC2 a Fargate

  network_configuration {
    subnets          = aws_subnet.public_subnet[*].id
    security_groups  = [aws_security_group.ecs_security_group.id]
    assign_public_ip = true # Asigna IP pública a la tarea Fargate
  }

  load_balancer {
    target_group_arn = aws_lb_target_group.backend_tg.arn
    container_name   = "backend"
    container_port   = var.backend_port
  }
}
