resource "aws_vpc_endpoint" "ecr_docker" {
  vpc_id              = aws_vpc.cognisearch_vpc.id
  service_name        = "com.amazonaws.sa-east-1.ecr.dkr" # Endpoint para ECR Docker
  vpc_endpoint_type   = "Interface"                       # Asegúrate de que el tipo sea Interface
  subnet_ids          = aws_subnet.public_subnet[*].id    # Asociamos las subnets públicas o privadas
  security_group_ids  = [aws_security_group.ecs_security_group.id]
  private_dns_enabled = true # Habilita DNS privado para acceso a ECR sin pasar por Internet

  tags = {
    Name = "ECR Docker VPC Endpoint"
  }
}

resource "aws_vpc_endpoint" "ecr_api" {
  vpc_id              = aws_vpc.cognisearch_vpc.id
  service_name        = "com.amazonaws.sa-east-1.ecr.api" # Endpoint para ECR API
  vpc_endpoint_type   = "Interface"                       # Asegúrate de que el tipo sea Interface
  subnet_ids          = aws_subnet.public_subnet[*].id    # Asociamos las subnets públicas o privadas
  security_group_ids  = [aws_security_group.ecs_security_group.id]
  private_dns_enabled = true # Habilita DNS privado para acceso a ECR sin pasar por Internet

  tags = {
    Name = "ECR API VPC Endpoint"
  }
}


resource "aws_vpc_endpoint" "s3" {
  vpc_id            = aws_vpc.cognisearch_vpc.id
  service_name      = "com.amazonaws.sa-east-1.s3"
  vpc_endpoint_type = "Gateway"
  route_table_ids   = [aws_route_table.public.id]

  tags = {
    Name = "S3 VPC Endpoint"
  }
}

# Add a route table for public subnets
resource "aws_route_table" "public" {
  vpc_id = aws_vpc.cognisearch_vpc.id

  route {
    cidr_block = "0.0.0.0/0"
    gateway_id = aws_internet_gateway.gateway.id
  }

  tags = {
    Name = "Public Route Table"
  }
}

# Associate route table with public subnets
resource "aws_route_table_association" "public" {
  count          = length(aws_subnet.public_subnet)
  subnet_id      = aws_subnet.public_subnet[count.index].id
  route_table_id = aws_route_table.public.id
}

# IAM Role Permissions
# Expand the IAM role for ECS task execution to ensure full ECR access:
resource "aws_iam_role_policy_attachment" "ecr_access" {
  role       = aws_iam_role.ecs_task_execution_role.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonEC2ContainerRegistryFullAccess"
}

# Add an ECR authorization policy
resource "aws_iam_role_policy" "ecr_auth" {
  name = "ecr-auth-policy"
  role = aws_iam_role.ecs_task_execution_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "ecr:GetAuthorizationToken",
          "ecr:BatchCheckLayerAvailability",
          "ecr:GetDownloadUrlForLayer",
          "ecr:BatchGetImage"
        ]
        Resource = "*"
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "ecs_task_execution_policy" {
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"
  role       = aws_iam_role.ecs_task_execution_role.name
}

# Update the IAM role for ECS task execution
resource "aws_iam_role_policy_attachment" "ecr_pull" {
  role       = aws_iam_role.ecs_task_execution_role.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonEC2ContainerRegistryReadOnly"
}
