resource "aws_lb" "cognisearch_alb" {
  name               = "cognisearch-alb"
  internal           = false
  load_balancer_type = "application"
  security_groups    = [aws_security_group.ecs_security_group.id]
  subnets            = aws_subnet.public_subnet[*].id
}

resource "aws_lb_target_group" "frontend_tg" {
  name     = "frontend-tg"
  port     = var.frontend_port
  protocol = "HTTP"
  vpc_id   = aws_vpc.cognisearch_vpc.id

  # Cambiar el tipo de destino a 'ip' para compatibilidad con Fargate
  target_type = "ip"

  health_check {
    protocol            = "HTTP"
    port               = 80
    path               = "/"
    healthy_threshold   = 2
    unhealthy_threshold = 10
    timeout             = 60
    interval            = 300
  }
}

resource "aws_lb_target_group" "backend_tg" {
  name     = "backend-tg"
  port     = var.backend_port
  protocol = "HTTP"
  vpc_id   = aws_vpc.cognisearch_vpc.id

  # Cambiar el tipo de destino a 'ip' para compatibilidad con Fargate
  target_type = "ip"
}

resource "aws_lb_listener" "http_listener" {
  load_balancer_arn = aws_lb.cognisearch_alb.arn
  port              = 443
  protocol          = "HTTPS"
  ssl_policy        = "ELBSecurityPolicy-2016-08"
  certificate_arn   = var.acm_certificate_arn

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.frontend_tg.arn
  }
}


resource "aws_lb_listener" "http_listener_api" {
  load_balancer_arn = aws_lb.cognisearch_alb.arn
  port              = 5000
  protocol          = "HTTPS"
  ssl_policy        = "ELBSecurityPolicy-2016-08"
  certificate_arn   = var.acm_certificate_arn

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.frontend_tg.arn
  }
}

resource "aws_lb_listener_rule" "backend_rule" {
  listener_arn = aws_lb_listener.http_listener.arn
  priority     = 100

  condition {
    host_header {
      values = ["dev.backend.cognisearch.cl"]
    }
  }

  action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.backend_tg.arn
  }
}

resource "aws_lb_listener_rule" "backend_rule_api_port" {
  listener_arn = aws_lb_listener.http_listener_api.arn
  priority     = 100

  condition {
    host_header {
      values = ["dev.backend.cognisearch.cl"]
    }
  }

  action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.backend_tg.arn
  }
}

resource "aws_lb_listener_rule" "frontend_rule" {
  listener_arn = aws_lb_listener.http_listener.arn
  priority     = 200

  condition {
    host_header {
      values = ["dev.frontend.cognisearch.cl"]
    }
  }

  action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.frontend_tg.arn
  }
}


