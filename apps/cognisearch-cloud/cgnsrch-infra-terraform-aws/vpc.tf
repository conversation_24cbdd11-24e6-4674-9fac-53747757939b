provider "aws" {
  region = "sa-east-1"
}

data "aws_availability_zones" "available" {
  state = "available"
}

resource "aws_vpc" "cognisearch_vpc" {
  cidr_block = var.vpc_cidr
  enable_dns_support = true
  enable_dns_hostnames = true
  tags = {
    Name = "cognisearch-vpc"
  }
}

resource "aws_internet_gateway" "gateway" {
  vpc_id = aws_vpc.cognisearch_vpc.id
  tags = {
    Name = "cognisearch-gateway"
  }
}

resource "aws_subnet" "public_subnet" {
  count = 2
  vpc_id                  = aws_vpc.cognisearch_vpc.id
  cidr_block              = cidrsubnet(aws_vpc.cognisearch_vpc.cidr_block, 8, count.index)
  map_public_ip_on_launch = true
  availability_zone       = data.aws_availability_zones.available.names[count.index]
  tags = {
    Name = "public-subnet-${count.index}"
  }
}
