# main.py

import os
import sys
import uuid
import json
import shutil
import logging

from typing import List
from datetime import datetime
from fastapi import BackgroundTasks
from fastapi.responses import JSONResponse, FileResponse
from fastapi.middleware.cors import CORSMiddleware
from fastapi import FastAPI, UploadFile, File, HTTPException

# Handle imports for both Docker and local environments
try:
    # Try relative imports first (for package structure)
    from .utils.constants import GPT_MODEL
    from .utils.connections import assistant_client
except ImportError:
    # Fall back to absolute imports (for Docker/direct execution)
    from utils.constants import GPT_MODEL
    from utils.connections import assistant_client


logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Log startup information
logger.info("🚀 Starting Filtro Curricular Backend API")
logger.info(f"Python version: {sys.version}")
logger.info(f"Working directory: {os.getcwd()}")
logger.info(f"Environment variables: WEBSITES_PORT={os.getenv('WEBSITES_PORT')}, ENVIRONMENT={os.getenv('ENVIRONMENT')}")

app = FastAPI(
    title="Filtro Curricular Backend API",
    description="Backend API for Filtro Curricular application",
    version="1.0.0"
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:8100"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.on_event("startup")
async def startup_event():
    logger.info("✅ FastAPI application startup complete")
    logger.info("🔍 Checking OpenAI configuration...")
    try:
        from utils.connections import assistant_client
        if assistant_client:
            logger.info("✅ OpenAI client initialized successfully")
        else:
            logger.warning("⚠️  OpenAI client not available - running in limited mode")
    except Exception as e:
        logger.error(f"❌ Error checking OpenAI configuration: {e}")
    logger.info("🌐 Application ready to serve requests")
TRANSPARENCY_DIR = "transparencia"
UPLOAD_DIR = "uploads"
WORD_DIR = os.path.join(UPLOAD_DIR, "word")

os.makedirs(TRANSPARENCY_DIR, exist_ok=True)
os.makedirs(UPLOAD_DIR, exist_ok=True)
os.makedirs(WORD_DIR, exist_ok=True)

tokens_used_accumulated = 0
requests_used_accumulated = 0

@app.get("/")
async def root():
    """
    Root endpoint for basic connectivity testing.
    """
    return {
        "message": "Filtro Curricular Backend API is running",
        "service": "filtro-curricular-backend",
        "version": "1.0.0",
        "timestamp": datetime.now().isoformat(),
        "endpoints": {
            "status": "/status",
            "health": "/health",
            "api_docs": "/docs"
        }
    }

@app.get("/api/usage-stats")
async def usage_stats():
    global tokens_used_accumulated, requests_used_accumulated
    return JSONResponse(content={
        "tokensUsed": tokens_used_accumulated,
        "requestsUsed": requests_used_accumulated
    })

@app.get("/api/transparency-files")
async def list_transparency_files():
    try:
        files = [f for f in os.listdir(TRANSPARENCY_DIR) if f.endswith(".json")]
        # Ordenar por fecha de modificación descendente (más recientes primero)
        files.sort(key=lambda f: os.path.getmtime(os.path.join(TRANSPARENCY_DIR, f)), reverse=True)
        return {"files": files}
    except Exception as e:
        return JSONResponse(status_code=500, content={"detail": f"Error listando archivos: {str(e)}"})

@app.get("/api/transparency-file-content/{filename}")
async def get_transparency_file_content(filename: str):
    file_path = os.path.join(TRANSPARENCY_DIR, filename)
    if not os.path.isfile(file_path):
        return JSONResponse(status_code=404, content={"detail": "Archivo no encontrado"})
    with open(file_path, "r", encoding="utf-8") as f:
        content = f.read()
    return JSONResponse(content={"filename": filename, "content": content})
    
@app.post("/api/upload")
async def upload_files(
    wordFiles: List[UploadFile] = File(None),
    excelJson: List[UploadFile] = File(...)
):
    uploaded_words = []
    try:
        if wordFiles:
            for file in wordFiles:
                if not file.filename.lower().endswith((".doc", ".docx")):
                    raise HTTPException(status_code=400, detail=f"Formato no admitido para Word: {file.filename}")
                file_path = os.path.join(WORD_DIR, f"{uuid.uuid4()}_{file.filename}")
                contents = await file.read()
                with open(file_path, "wb") as f:
                    f.write(contents)
                uploaded_words.append(file_path)

        uploaded_json_paths = []
        for json_file in excelJson:
            if not json_file.filename.endswith(".json"):
                raise HTTPException(status_code=400, detail="Archivo JSON esperado para excelJson")
            json_path = os.path.join(UPLOAD_DIR, f"{uuid.uuid4()}_{json_file.filename}")
            contents = await json_file.read()
            with open(json_path, "wb") as f:
                f.write(contents)
            uploaded_json_paths.append(json_path)

        return {"message": "Archivos subidos correctamente", "wordFiles": uploaded_words, "excelJsons": uploaded_json_paths}
    except Exception as e: 
        raise HTTPException(status_code=500, detail=f"Error al subir archivos: {str(e)}")
 
def read_json_excel(path: str):
    with open(path, "r", encoding="utf-8") as f:
        data = json.load(f) 
    return data

def read_word_text(path: str) -> str:
    from docx import Document
    doc = Document(path)
    fullText = []
    for para in doc.paragraphs:
        fullText.append(para.text)
    return "\n".join(fullText)

progress_data = { 
    "current_batch": -1,
    "total_batches": None,
    "results": []
}

@app.post("/api/autopilot-progress")
async def autopilot_progress():
    global progress_data, tokens_used_accumulated, requests_used_accumulated

    if progress_data.get("finished", False):
        logger.info("Proceso finalizado, enviando resultados totales")
        return {"finished": True, "results": progress_data["results"]}

    if progress_data["total_batches"] is None:
        json_files = [f for f in os.listdir(UPLOAD_DIR) if f.endswith(".json")]
        progress_data["total_batches"] = len(json_files)
        logger.info(f"Total batches: {progress_data['total_batches']}")

    current_idx = progress_data.get("current_batch", -1)

    if current_idx + 1 >= progress_data["total_batches"]:
        progress_data["finished"] = True
        logger.info("Último batch procesado, marcando finished=True")
        return {"finished": True, "results": progress_data["results"]}

    current_idx += 1
    progress_data["current_batch"] = current_idx

    json_files_sorted = sorted([f for f in os.listdir(UPLOAD_DIR) if f.endswith(".json")])
    json_path = os.path.join(UPLOAD_DIR, json_files_sorted[current_idx])

    logger.info(f"Procesando batch {current_idx + 1} de {progress_data['total_batches']}: {json_files_sorted[current_idx]}")

    with open(json_path, "r", encoding="utf-8") as f:
        batch_data = json.load(f)

    word_files = os.listdir(WORD_DIR)
    word_txts = []
    word_filenames = []

    for wf in word_files:
        word_filenames.append(wf)
        with open(os.path.join(WORD_DIR, wf), "r", encoding="utf-8", errors="ignore") as f:
            word_txts.append(f.read())

    prompt = (
        "Eres un asistente que selecciona el o los candidatos más idóneos basándote en los criterios extraídos del documento Word Maestro y los datos del batch actual.\n\n"
        "Criterios extraídos de los documentos Word:\n"
    )
    
    for idx, txt in enumerate(word_txts):
        prompt += f"Documento {idx + 1}:\n{txt}\n\n"
    
    prompt += "Datos del batch de candidatos en formato JSON:\n"
    prompt += json.dumps(batch_data, ensure_ascii=False, indent=2)
    
    prompt += (
        "\nRealiza un análisis exhaustivo para seleccionar candidatos que cumplan con los criterios indicados.\n"
        "Luego, verifica nuevamente cada selección y justificación para asegurarte de que cumple estrictamente con los criterios.\n"
        "Solo entrega la respuesta final después de esta doble revisión.\n"
        "Selecciona únicamente a los candidatos cuyo porcentaje de exactitud en la comparación sea igual o superior a 95%.\n"
        "Por favor, entrega el \"Porcentaje de Exactitud\" con un valor numérico con decimales, por ejemplo: 98.3, 95.7, etc., para mayor precisión.\n"
        "Esta precisión es necesaria para cumplir con requisitos de concursos públicos y transparencia.\n"
        "Para cada candidato seleccionado, incluye un fragmento explicativo breve que justifique la selección y explique por qué no obtuvo un puntaje mayor.\n"
        "Si ningún candidato alcanza el puntaje mínimo, devuelve un array JSON con un solo objeto que contenga el campo "
        "\"motivoNoSeleccion\" con una breve explicación del motivo por el cual no se seleccionaron candidatos en este batch.\n\n"
        "Entrega la respuesta en formato JSON con un array de objetos donde cada objeto tenga los siguientes campos:\n"
        "- Num Postulación\n"
        "- ID Usuario\n"
        "- RUN\n"
        "- Nombre Registral\n"
        "- Primer Apellido\n"
        "- Segundo Apellido\n"
        "- Fecha Postulación\n"
        "- Correo\n"
        "- Telefono Fijo\n"
        "- Telefono Celular\n"
        "- Porcentaje de Exactitud (valor numérico entre 95 y 100 con decimales)\n"
        "- Justificación Selección (texto explicativo)\n\n"
        "No agregues texto fuera del JSON."
    )

    # Check if assistant_client is available
    if assistant_client is None:
        # Return mock response when OpenAI is not configured
        selection_json = [{
            "motivoNoSeleccion": "Servicio de IA no disponible - credenciales de OpenAI no configuradas correctamente"
        }]
        logger.warning("OpenAI no configurado, retornando respuesta mock")
    else:
        response = assistant_client.chat.completions.create(
            model=GPT_MODEL,
            messages=[{"role": "user", "content": prompt}],
            temperature=0.1,
            max_tokens=2000,
        )

        # Acumular tokens usados si están disponibles
        usage_info = getattr(response, 'usage', None)
        if usage_info and hasattr(usage_info, 'total_tokens'):
            tokens_used_accumulated += usage_info.total_tokens
        elif isinstance(response, dict) and 'usage' in response and 'total_tokens' in response['usage']:
            tokens_used_accumulated += response['usage']['total_tokens']

        # Incrementar requests usadas
        requests_used_accumulated += 1

        selection_text = response.choices[0].message.content

        try:
            selection_json = json.loads(selection_text)
        except Exception:
            selection_json = {"raw_response": selection_text}

    logger.info(f"Respuesta IA para batch {current_idx + 1}: {json.dumps(selection_json, ensure_ascii=False)}")

    # Guardar archivo transparencia JSON
    try:
        # Seleccionar nombre archivo maestro (el primero, o el que tenga "maestro" en el nombre)
        maestro_name = next((name for name in word_filenames if "maestro" in name.lower()), None)
        if not maestro_name and len(word_filenames) > 0:
            maestro_name = word_filenames[0]

        fecha_str = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        if maestro_name:
            safe_maestro_name = os.path.splitext(maestro_name)[0].replace(" ", "_")
            transparency_filename = f"{safe_maestro_name}_{fecha_str}_batch{current_idx+1}.json"
            transparency_path = os.path.join(TRANSPARENCY_DIR, transparency_filename)

            # Guardar respuesta completa (criterios incluidos) sin modificar
            with open(transparency_path, "w", encoding="utf-8") as f:
                json.dump(selection_json, f, ensure_ascii=False, indent=2)
            logger.info(f"Archivo transparencia guardado: {transparency_path}")
    except Exception as e:
        logger.error(f"Error guardando archivo transparencia: {str(e)}")

    progress_data["results"].append({"batch_file": json_files_sorted[current_idx], "selection": selection_json})

    finished = current_idx + 1 >= progress_data["total_batches"]
    if finished:
        progress_data["finished"] = True
        logger.info("Todos los batches procesados, terminando proceso")

    # Enviar respuesta sin mostrar justificación en GUI (frontend debe ignorar campo)
    return {
        "finished": finished,
        "currentBatch": current_idx,
        "totalBatches": progress_data["total_batches"],
        "partialResult": progress_data["results"][-1],
    }

@app.get("/status")
async def get_status():
    """
    Health check endpoint for Azure App Service.
    Returns the status of the application.
    """
    return {
        "status": "healthy",
        "service": "filtro-curricular-backend",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0"
    }

@app.get("/health")
async def health_check():
    """
    Alternative health check endpoint.
    """
    return {
        "status": "OK",
        "service": "filtro-curricular-backend"
    }

@app.post("/api/reset-temp-files")
async def reset_temp_files():
    try:
        # Borrar archivos .json en UPLOAD_DIR
        for filename in os.listdir(UPLOAD_DIR):
            if filename.endswith(".json"):
                os.remove(os.path.join(UPLOAD_DIR, filename))

        # Borrar archivos Word en WORD_DIR
        for filename in os.listdir(WORD_DIR):
            if filename.lower().endswith((".doc", ".docx")):
                os.remove(os.path.join(WORD_DIR, filename))

        # Resetear progreso
        global progress_data
        progress_data = {
            "current_batch": -1,
            "total_batches": None,
            "results": []
        }

        return {"message": "Archivos temporales eliminados correctamente."}
    except Exception as e:
        return JSONResponse(status_code=500, content={"detail": f"Error al eliminar archivos temporales: {str(e)}"})