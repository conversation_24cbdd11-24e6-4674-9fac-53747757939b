#!/usr/bin/env python3
"""
Simple test script to verify basic FastAPI functionality
This script has minimal dependencies to help debug container startup issues
"""

import os
import sys
from datetime import datetime

# Simple FastAPI app for testing
try:
    from fastapi import Fast<PERSON><PERSON>
    from fastapi.responses import JSONResponse
    
    app = Fast<PERSON><PERSON>(title="Simple Test API", version="1.0.0")
    
    @app.get("/")
    def root():
        return {
            "message": "Simple test API is working",
            "timestamp": datetime.now().isoformat(),
            "python_version": sys.version,
            "working_directory": os.getcwd(),
            "environment": {
                "WEBSITES_PORT": os.getenv("WEBSITES_PORT"),
                "ENVIRONMENT": os.getenv("ENVIRONMENT"),
                "PYTHONPATH": os.getenv("PYTHONPATH")
            }
        }
    
    @app.get("/health")
    def health():
        return {"status": "OK", "service": "simple-test"}
    
    @app.get("/test")
    def test():
        return {
            "test": "success",
            "imports": "working",
            "fastapi": "loaded"
        }
        
    print("✅ Simple FastAPI app created successfully")
    
except Exception as e:
    print(f"❌ Error creating FastAPI app: {e}")
    sys.exit(1)

if __name__ == "__main__":
    print("🧪 Running simple test...")
    print(f"Python version: {sys.version}")
    print(f"Working directory: {os.getcwd()}")
    print("✅ Simple test completed")
