import os
from pathlib import Path
from dotenv import load_dotenv

def load_environment():
    """Carga las variables de entorno desde diferentes ubicaciones posibles"""
    env_locations = [
        Path('/app/local-env/.env'),  # Ubicación en Docker
        Path(__file__).parent.parent.parent.parent.parent / 'local-env' / '.env',  # Ubicación local
        Path.cwd() / 'local-env' / '.env'  # Ubicación relativa
    ]

    env_loaded = False
    for env_path in env_locations:
        if env_path.exists():
            print(f"✅ Archivo .env encontrado en: {env_path}")
            load_dotenv(env_path)
            env_loaded = True
            break

    if not env_loaded:
        paths_checked = "\n- ".join([str(p) for p in env_locations])
        print(f"❌ No se encontró el archivo .env en ninguna ubicación:\n- {paths_checked}")

# Cargar variables de entorno
load_environment()


# Variables requeridas y sus mensajes de error
REQUIRED_VARS = {"OPENAI_TOKEN": "Token de OpenAI","GPT_API_KEY": "API Key de GPT"}

# Verificar todas las variables requeridas
missing_vars = []
for var_name, var_description in REQUIRED_VARS.items():
    var_value = os.getenv(var_name)
    # Check if variable is missing or contains placeholder values
    if not var_value or var_value in ["your-openai-api-key-here", "your-openai-token-here"]:
        missing_vars.append(f"❌ {var_description} ({var_name}) no está configurado correctamente")

# Only raise error in development/local environment
# In production (Azure), these should be provided via Key Vault
if missing_vars and not os.getenv("WEBSITES_ENABLE_APP_SERVICE_STORAGE"):  # Azure App Service indicator
    print("⚠️  Advertencia: Variables de OpenAI no configuradas:")
    for var in missing_vars:
        print(f"   {var}")
    print("   La aplicación continuará pero las funciones de IA no estarán disponibles.")
elif missing_vars:
    raise ValueError("\n".join(missing_vars))

# Exportar variables verificadas
GPT_API_KEY = os.getenv("GPT_API_KEY")
# Allow GPT_MODEL to be configured via environment variable, with fallback
GPT_MODEL = os.getenv("GPT_MODEL", "gpt-4o-mini")  # Updated to use gpt-4o-mini as default
OPENAI_TOKEN = os.getenv("OPENAI_TOKEN")
