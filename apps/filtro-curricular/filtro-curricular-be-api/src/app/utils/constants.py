import os
from pathlib import Path
from dotenv import load_dotenv

def is_azure_app_service():
    """Detecta si la aplicación está ejecutándose en Azure App Service"""
    # Azure App Service sets these environment variables
    azure_indicators = [
        "WEBSITE_SITE_NAME",           # Always present in Azure App Service
        "WEBSITE_RESOURCE_GROUP",      # Always present in Azure App Service
        "WEBSITE_OWNER_NAME"           # Always present in Azure App Service
    ]
    return any(os.getenv(indicator) for indicator in azure_indicators)

def load_environment():
    """Carga las variables de entorno desde diferentes ubicaciones posibles"""
    # In Azure App Service, environment variables are provided via Key Vault references
    # Only try to load .env files in local development
    if is_azure_app_service():
        print("🔵 Ejecutándose en Azure App Service - usando variables de entorno del sistema")
        return

    # Local development - try to load .env files
    env_locations = [
        Path('/app/local-env/.env'),  # Ubicación en Docker local
        Path(__file__).parent.parent.parent.parent.parent / 'local-env' / '.env',  # Ubicación local
        Path.cwd() / 'local-env' / '.env'  # Ubicación relativa
    ]

    env_loaded = False
    for env_path in env_locations:
        if env_path.exists():
            print(f"✅ Archivo .env encontrado en: {env_path}")
            load_dotenv(env_path)
            env_loaded = True
            break

    if not env_loaded:
        paths_checked = "\n- ".join([str(p) for p in env_locations])
        print(f"❌ No se encontró el archivo .env en ninguna ubicación:\n- {paths_checked}")
        print("💡 En desarrollo local, asegúrate de tener un archivo .env con las credenciales")

# Cargar variables de entorno
load_environment()

# Variables requeridas y sus mensajes de error
REQUIRED_VARS = {"OPENAI_TOKEN": "Token de OpenAI","GPT_API_KEY": "API Key de GPT"}

# Verificar todas las variables requeridas
missing_vars = []
for var_name, var_description in REQUIRED_VARS.items():
    var_value = os.getenv(var_name)
    # Check if variable is missing or contains placeholder values
    if not var_value or var_value in ["your-openai-api-key-here", "your-openai-token-here", ""]:
        missing_vars.append(f"❌ {var_description} ({var_name}) no está configurado correctamente")

# Handle missing variables based on environment
if missing_vars:
    if is_azure_app_service():
        print("⚠️  Variables de OpenAI no configuradas en Azure App Service:")
        for var in missing_vars:
            print(f"   {var}")
        print("🔧 Posibles causas:")
        print("   1. Key Vault secrets no están configurados")
        print("   2. App Service no tiene acceso a Key Vault")
        print("   3. Referencias de Key Vault no están configuradas correctamente")
        print("   4. Managed Identity no está configurado correctamente")
        print("🚀 La aplicación continuará pero las funciones de IA no estarán disponibles")
        print("   Esto permite que la aplicación inicie y sea debuggeable")
        # Don't fail in Azure - allow app to start for debugging
        # The application will handle missing credentials gracefully
    else:
        print("⚠️  Advertencia: Variables de OpenAI no configuradas en desarrollo local:")
        for var in missing_vars:
            print(f"   {var}")
        print("   La aplicación continuará pero las funciones de IA no estarán disponibles.")
        print("💡 Para desarrollo local, crea un archivo .env con las credenciales necesarias")

# Exportar variables verificadas
GPT_API_KEY = os.getenv("GPT_API_KEY")
# Allow GPT_MODEL to be configured via environment variable, with fallback
GPT_MODEL = os.getenv("GPT_MODEL", "gpt-4o-mini")  # Updated to use gpt-4o-mini as default
OPENAI_TOKEN = os.getenv("OPENAI_TOKEN")
