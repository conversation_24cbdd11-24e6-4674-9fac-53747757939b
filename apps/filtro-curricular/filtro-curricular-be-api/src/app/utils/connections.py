# utils/connections.py

from openai import OpenAI

# Handle imports for both Docker and local environments
try:
    # Try relative imports first (for package structure)
    from .constants import (
        OPENAI_TOKEN,
        GPT_API_KEY
    )
except ImportError:
    # Fall back to absolute imports (for Docker/direct execution)
    from constants import (
        OPENAI_TOKEN,
        GPT_API_KEY
    )

def init_openai_clients():
    """Inicializa y retorna los clientes de OpenAI"""
    try:
        # Check if API keys are properly configured
        if not OPENAI_TOKEN or OPENAI_TOKEN in ["your-openai-token-here", ""]:
            print("⚠️  OPENAI_TOKEN no configurado, usando cliente mock")
            assistant_client = None
        else:
            assistant_client = OpenAI(api_key=OPENAI_TOKEN)

        if not GPT_API_KEY or GPT_API_KEY in ["your-openai-api-key-here", ""]:
            print("⚠️  GPT_API_KEY no configurado, usando cliente mock")
            casual_client = None
        else:
            casual_client = OpenAI(api_key=GPT_API_KEY)

        print("[OK] Clientes OpenAI inicializados (algunos pueden ser mock si faltan credenciales)")
        return assistant_client, casual_client

    except Exception as e:
        print(f"[ERROR] Error inicializando clientes OpenAI: {str(e)}")
        # Return None clients instead of raising to allow app to start
        return None, None

# Inicializar clientes
try:
    assistant_client, casual_client = init_openai_clients()
    if assistant_client is None:
        print("⚠️  Assistant client no disponible - funciones de IA limitadas")
    if casual_client is None:
        print("⚠️  Casual client no disponible - funciones de IA limitadas")
except Exception as e:
    print(f"[ERROR] Error crítico inicializando servicios: {str(e)}")
    # Set to None instead of raising to allow app to start
    assistant_client, casual_client = None, None